<template>
	<view class="notice_warp">
		<view class="msg_warp">
			<view class="msg_title">{{strTitle}}</view>
			<view class="msg_time">{{i18n.time}}：{{strDate}}</view>
		</view>
		<image class="img" :src="$tools.setImgUrl(strImage)" />
		<view class="contents" v-html="strings"></view>
	</view>
</template>

<script>
	export default {
		name: 'activityDetail',
		components: {},

		computed: {
		    i18n () {
		       return this.$t("activity")
		    }
		},

		data() {
			return {
				strTitle: '',
				strings: '',
				strDate: '',
				strImage: ''
			}
		},
		filters:{
		            //过滤器 用于格式化时间
		            dateFormat: function(value) {
		                var date = new Date(value*1000); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
		                var year = date.getFullYear();
		                var month = ("0" + (date.getMonth() + 1)).slice(-2);
		                var sdate = ("0" + date.getDate()).slice(-2);
		                var hour = ("0" + date.getHours()).slice(-2);
		                var minute = ("0" + date.getMinutes()).slice(-2);
		                var second = ("0" + date.getSeconds()).slice(-2);
		                // 拼接
		                var result = year + "年" + month + "月" + sdate + "日 " + hour + ":" + minute //+ ":" + second;
		                // 返回
		                return result;
		            },
		},
		// 页面加载
		onLoad(options) {
			uni.setNavigationBarTitle({
				title: this.$t('new2.txt2')
			})
            this.strTitle = options.strTitle;
            this.strings = options.strings;
            this.strDate = options.strDate;
			this.strImage = options.strImage;
		},
		created() {

		},

		mounted() {

		},
		methods: {

		}
	}
</script>

<style lang="less" scoped>
page {
	width: 100%;
	height: 100%;
	overflow: hidden;

	.notice_warp {
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		font-size: 28rpx;
		padding-top: 1rpx;

		.msg_warp {
			width: 100%;
			background: #FFFFFF;
			padding: 30rpx 26rpx;
			margin: 0 auto;
			border-bottom: 10rpx solid rgba(245, 245, 245, 1);

			.msg_title {
				font-size: 36rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #333333;
				margin-bottom: 10rpx;
			}

			.msg_time {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
			}
		}

		.image{
			width: calc(100% - 52rpx);
			margin: 33rpx 26rpx 0 26rpx;
		}

		.contents {
			padding: 26rpx;
			color: #323232;
			line-height: 40rpx;
			font-size: 28rpx;
			text-indent: 25rpx;
			image{
				width: 100%;
			}
		}
	}
}
</style>
