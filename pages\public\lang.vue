<template>
	<view class="main">
       <view v-for="(item, index) in langList" :key="index" 
             class="item" 
             :class="{'active': current === index}"
             @click="changeLang(index)">
          {{item.name}}
       </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        langList: [
            {name: '简体中文', value: 'CN'},
            {name: '繁體中文', value: 'TW'},
            {name: 'English', value: 'EN'},
            {name: 'Tiếng Việt', value: 'VN'},
            {name: 'Bahasa Indonesia', value: 'IDN'},
            {name: '<PERSON>spa<PERSON><PERSON>', value: 'ES'},
            {name: 'Português', value: 'PT'},
            {name: 'Deuts<PERSON>', value: 'DE'}
        ],
        current: 0
      }
    },
    onLoad() {
        // 获取当前语言设置
        const currentLang = uni.getStorageSync('lang') || this.getDefaultLanguage();
        this.langList.forEach((item, index) => {
            if(item.value === currentLang) {
                this.current = index;
            }
        });
    },
	computed: {
	    i18n () {
	       return this.$t("login")
	    }
	},
	onShow() {

	},
	onReady() {
		uni.setNavigationBarTitle({
			title:this.$t('new.mineMenu8')
		})
	},
    methods:{
        // 获取默认语言设置
        getDefaultLanguage() {
            const systemLang = uni.getSystemInfoSync().language.toLowerCase();
            let defaultLang = 'EN'; // 默认英语

            // 根据系统语言设置默认语言
            if (systemLang.includes('zh')) {
                defaultLang = systemLang.includes('tw') ? 'TW' : 'CN';
            } else if (systemLang.includes('es')) {
                defaultLang = 'ES';
            } else if (systemLang.includes('pt')) {
                defaultLang = 'PT';
            } else if (systemLang.includes('id')) {
                defaultLang = 'IDN';
            } else if (systemLang.includes('vi')) {
                defaultLang = 'VN';
            } else if (systemLang.includes('de')) {
                defaultLang = 'DE';
            }

            return defaultLang;
        },
        
        changeLang(index) {
            this.current = index;
            const lang = this.langList[index].value;
            
            // 保存语言设置
            uni.setStorageSync('lang', lang);
            this.$i18n.locale = lang;
            
            // 显示加载提示
            uni.showLoading({
                title: 'Loading...'
            });
            
            // 延迟执行以确保语言切换完成
            setTimeout(() => {
                uni.hideLoading();
                
                // 获取当前页面栈
                const pages = getCurrentPages();
                const prevPage = pages[pages.length - 2];
                
                // 如果上一页是登录页，则刷新登录页
                if (prevPage && prevPage.route === 'pages/public/login') {
                    // 触发登录页的语言初始化方法
                    prevPage.$vm.initLanguage();
                    uni.navigateBack();
                } else {
                    // 如果不是从登录页来的，则返回首页
                    uni.reLaunch({
                        url: '/pages/home/<USER>'
                    });
                }
            }, 500);
        }
    }
  }
</script>

<style scoped lang="less">
.main{
  padding:20rpx 30rpx;
  background: #fff;
  height: 100%;
  min-height: 100vh;
}
   .item{
      height: 90rpx;
      line-height: 90rpx;
      text-align: left;
      border-bottom: 1px solid #ddd;
      font-size: 16px;
      color: #000;
      padding: 0 20rpx;
      
      &.active {
          color: #65B11D;
          background-color: rgba(101, 177, 29, 0.1);
      }
   }
</style>
