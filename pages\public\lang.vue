<template>
	<view class="main">
       <view v-for="(item, index) in langList" :key="index" 
             class="item" 
             :class="{'active': current === index}"
             @click="changeLang(index)">
          {{item.name}}
       </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        langList: [
            {name: '简体中文', value: 'CN'},
            {name: '繁體中文', value: 'TW'},
            {name: 'English', value: 'EN'},
            {name: 'Tiếng Việt', value: 'VN'},
            {name: 'Bahasa Indonesia', value: 'IDN'},
            {name: '<PERSON>spa<PERSON><PERSON>', value: 'ES'},
            {name: 'Português', value: 'PT'},
            {name: '<PERSON>uts<PERSON>', value: 'DE'}
        ],
        current: 0,
        isChanging: false
      }
    },
    onLoad() {
        // 获取当前语言设置
        const currentLang = uni.getStorageSync('lang') || this.getDefaultLanguage();
        this.langList.forEach((item, index) => {
            if(item.value === currentLang) {
                this.current = index;
            }
        });
    },
	computed: {
	    i18n () {
	       return this.$t("login")
	    }
	},
	onShow() {

	},
	onReady() {
		uni.setNavigationBarTitle({
			title:this.$t('new.mineMenu8')
		})
	},
    methods:{
        // 获取默认语言设置
        getDefaultLanguage() {
            const systemLang = uni.getSystemInfoSync().language.toLowerCase();
            let defaultLang = 'EN'; // 默认英语

            // 根据系统语言设置默认语言
            if (systemLang.includes('zh')) {
                defaultLang = systemLang.includes('tw') ? 'TW' : 'CN';
            } else if (systemLang.includes('es')) {
                defaultLang = 'ES';
            } else if (systemLang.includes('pt')) {
                defaultLang = 'PT';
            } else if (systemLang.includes('id')) {
                defaultLang = 'IDN';
            } else if (systemLang.includes('vi')) {
                defaultLang = 'VN';
            } else if (systemLang.includes('de')) {
                defaultLang = 'DE';
            }

            return defaultLang;
        },
        
        changeLang(index) {
            // 防止重复点击
            if (this.isChanging) return;
            this.isChanging = true;

            this.current = index;
            const lang = this.langList[index].value;

            try {
                // 保存语言设置
                uni.setStorageSync('lang', lang);

                // 更新i18n实例的语言设置
                if (this.$i18n) {
                    this.$i18n.locale = lang;
                }

                // 显示切换成功提示
                uni.showToast({
                    title: this.getLanguageSuccessMessage(lang),
                    icon: 'success',
                    duration: 1000
                });

                // 延迟执行以确保语言切换完成
                setTimeout(() => {
                    try {
                        // 获取当前页面栈
                        const pages = getCurrentPages();

                        // 安全地返回上一页，不依赖特定方法或会话状态
                        if (pages.length > 1) {
                            // 简单返回上一页，让页面自己处理语言更新
                            uni.navigateBack({
                                success: () => {
                                    console.log('语言切换完成，已返回上一页');
                                },
                                fail: (err) => {
                                    console.warn('返回上一页失败:', err);
                                    // 如果返回失败，则跳转到首页
                                    uni.reLaunch({
                                        url: '/pages/home/<USER>'
                                    });
                                }
                            });
                        } else {
                            // 如果没有上一页，则返回首页
                            uni.reLaunch({
                                url: '/pages/home/<USER>'
                            });
                        }
                    } catch (error) {
                        console.error('页面导航错误:', error);
                        // 发生错误时，强制跳转到首页
                        uni.reLaunch({
                            url: '/pages/home/<USER>'
                        });
                    } finally {
                        this.isChanging = false;
                    }
                }, 800);

            } catch (error) {
                console.error('语言切换错误:', error);
                uni.showToast({
                    title: '语言切换失败，请重试',
                    icon: 'none',
                    duration: 2000
                });
                this.isChanging = false;
            }
        },

        // 获取语言切换成功提示信息
        getLanguageSuccessMessage(lang) {
            const messages = {
                'CN': '语言已切换为简体中文',
                'TW': '語言已切換為繁體中文',
                'EN': 'Language switched to English',
                'VN': 'Ngôn ngữ đã chuyển sang tiếng Việt',
                'IDN': 'Bahasa telah beralih ke Indonesia',
                'ES': 'Idioma cambiado a Español',
                'PT': 'Idioma alterado para Português',
                'DE': 'Sprache auf Deutsch umgestellt'
            };
            return messages[lang] || 'Language switched';
        }
    }
  }
</script>

<style scoped lang="less">
.main{
  padding:20rpx 30rpx;
  background: #fff;
  height: 100%;
  min-height: 100vh;
}
   .item{
      height: 90rpx;
      line-height: 90rpx;
      text-align: left;
      border-bottom: 1px solid #ddd;
      font-size: 16px;
      color: #000;
      padding: 0 20rpx;
      
      &.active {
          color: #65B11D;
          background-color: rgba(101, 177, 29, 0.1);
      }
   }
</style>
