.container{
		font-family: PingFang SC;
		position: fixed;
		top:0;
		right:0;
		bottom:0;
		left:0;
		overflow: scroll;
		background: rgba(245, 245, 245, 1);
		// background-size: 100% 100%;
        .nav_bar{
            position: sticky;
            width: 100%;
            left: 0;
            right: 0;
            top:0;
            z-index: 3;
            .status_bar {
                height: var(--status-bar-height);
                width: 100%;
                background: #172D52;
            }
            .nav_wrap{
                height: 100rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #172D52;
				position: relative;

                .title{
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #FFFFFF;
                }
                .picker_wrap{
                    display: flex;
                    align-items: center;
					position: absolute;
					right: 30rpx;
                    .picker{
                        display: flex;
                        align-items: center;
                        .time{
                            width: 32rpx;
                            height: 32rpx;
                            margin-top: 2px;
                        }
                        text{
                            font-size: 28rpx;
                            font-weight: 500;
                            color: #FFFFFF;
                            padding: 0 10rpx;
                        }
                        .select{
                            width: 22rpx;
                            height: 10rpx;
                            margin-top: 1px;
                        }
                    }
                }
            }
        }
		.tabs{
			display: flex;
			justify-content: space-between;
			width: 100%;
			height: 92rpx;
			// padding-bottom: 20rpx;
			background: #172D52;
			// border-bottom:10rpx solid #F5F5F5;
			position: fixed;
			z-index: 99;
			box-sizing: border-box;
			view{
				font-size: 14px;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				flex:1;
				height: 100%;
			    display: flex;
				align-items: center;
				justify-content: center;
				border-bottom:10rpx solid transparent;
				box-sizing: border-box;
				margin: 0 30rpx;
			}
			.current{
				// background: url('../../static/images/dikuai.png') no-repeat center;
				// background-size: 100% 100%;
				font-weight: bold;
				color: rgba(144, 219, 47, 1);
				border-bottom:10rpx solid rgba(144, 219, 47, 1);

			}
			.bottombg{
				position: absolute;
				left: -30rpx;
				bottom: 0;
				width: 100%;
				height: 10rpx;
				background-color: #172D52;
				z-index: -1;
			}
		}
		.list_wrap{
			padding-top: 100rpx;
			height: calc(100vh - 240rpx);
			overflow-y: auto;
			-webkit-overflow-scrolling: touch;
			position: relative;
			z-index: 1;
			background: #f5f5f5;
			
			&::-webkit-scrollbar {
				display: none;
			}
			
			.noList{
				display: flex;
				flex-direction: column;
				align-items: center;
				font-size: 12px;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				padding-top: 100rpx;
			}
			
			.list{
				height: 240rpx;
				background: #FFFFFF;
				border-radius: 20rpx;
				margin: 20rpx 20rpx 0 20rpx;
				padding: 0 20rpx;
				transform: translateZ(0);
				will-change: transform;
				.listTop{
					height: 100rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1rpx solid rgba(224, 224, 224, 1);
					.listTop-name{
						height: 60rpx;
						background: #172D52;
						border-radius: 14rpx;
						padding: 0 33rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 32rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #FFFFFF;
					}
					.listTop-date{
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
					}
				}
				.item{
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 139rpx;
					.item-left{
						height: 139rpx;
						display: flex;
						flex-direction: column;
						justify-content: center;
					}
					.item-order{
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
					}
					.item-txt{
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #333333;
						display: flex;
						align-items: center;
						margin-top: 18rpx;
						span{
							width: 1rpx;
							height: 24rpx;
							background: #333333;
							margin: 0 21rpx;
							display: block;
						}
					}
					.item-not{
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #999999;
						height: 139rpx;
						display: flex;
						align-items: center;
					}
					.item-in{
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #80BF3E;
						height: 139rpx;
						display: flex;
						align-items: center;
					}
					.item-on{
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #FF7200;
						height: 139rpx;
						display: flex;
						align-items: center;
					}
					.item-img{
						width: 60rpx;
						height: 60rpx;
						margin-left: 7rpx;
					}
				}
			}
		}
	}
.example-body{
	padding: 20rpx;
	background: #f5f5f5;
	margin-bottom: 100rpx;
}
