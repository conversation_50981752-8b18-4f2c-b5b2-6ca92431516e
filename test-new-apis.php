<?php
/**
 * 测试新添加的API接口
 * 1. /api/health - 健康检查接口
 * 2. /api/user/refresh - 会话刷新接口
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 测试配置
$baseUrl = 'http://localhost/admin.inspap.com/api/'; // 根据实际情况修改

echo "<h1>新API接口测试报告</h1>\n";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";

/**
 * 发送HTTP请求
 */
function makeRequest($url, $method = 'GET', $data = [], $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // 设置请求头
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if (!empty($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    } elseif ($method === 'GET' && !empty($data)) {
        $url .= '?' . http_build_query($data);
        curl_setopt($ch, CURLOPT_URL, $url);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'httpCode' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

/**
 * 测试健康检查接口
 */
function testHealthAPI($baseUrl) {
    echo "<h2>1. 健康检查接口测试 (/api/health)</h2>\n";
    
    $result = makeRequest($baseUrl . 'health', 'GET');
    
    echo "<p><strong>HTTP状态码:</strong> {$result['httpCode']}</p>\n";
    
    if ($result['httpCode'] === 200) {
        echo "<p style='color: green;'>✅ 接口可访问</p>\n";
        
        $data = json_decode($result['response'], true);
        if ($data) {
            echo "<p><strong>响应数据:</strong></p>\n";
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
            
            if (isset($data['status']) && $data['status'] === 200) {
                echo "<p style='color: green;'>✅ 响应格式正确</p>\n";
                
                if (isset($data['data']['status']) && $data['data']['status'] === 'ok') {
                    echo "<p style='color: green;'>✅ 健康检查通过</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠️ 健康状态未知</p>\n";
                }
            } else {
                echo "<p style='color: red;'>❌ 响应格式错误</p>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ 响应数据解析失败</p>\n";
            echo "<p><strong>原始响应:</strong> " . htmlspecialchars($result['response']) . "</p>\n";
        }
    } elseif ($result['httpCode'] === 404) {
        echo "<p style='color: red;'>❌ 接口不存在 (404)</p>\n";
        echo "<p><strong>建议:</strong> 检查路由配置是否正确</p>\n";
    } else {
        echo "<p style='color: red;'>❌ HTTP错误: {$result['httpCode']}</p>\n";
        if ($result['error']) {
            echo "<p style='color: red;'>错误信息: {$result['error']}</p>\n";
        }
        echo "<p><strong>响应内容:</strong> " . htmlspecialchars($result['response']) . "</p>\n";
    }
    
    echo "<hr>\n";
}

/**
 * 测试会话刷新接口
 */
function testRefreshAPI($baseUrl) {
    echo "<h2>2. 会话刷新接口测试 (/api/user/refresh)</h2>\n";
    echo "<p><em>注意: 此接口需要有效的认证token</em></p>\n";
    
    // 测试无token的情况
    echo "<h3>2.1 无认证token测试</h3>\n";
    $result1 = makeRequest($baseUrl . 'user/refresh', 'POST', [
        'language' => 'CN'
    ]);
    
    echo "<p><strong>HTTP状态码:</strong> {$result1['httpCode']}</p>\n";
    
    if ($result1['httpCode'] === 401) {
        echo "<p style='color: green;'>✅ 正确拒绝未认证请求</p>\n";
    } elseif ($result1['httpCode'] === 404) {
        echo "<p style='color: red;'>❌ 接口不存在 (404)</p>\n";
        echo "<p><strong>建议:</strong> 检查路由配置是否正确</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ 意外的HTTP状态码: {$result1['httpCode']}</p>\n";
    }
    
    if ($result1['response']) {
        $data = json_decode($result1['response'], true);
        if ($data) {
            echo "<p><strong>响应数据:</strong></p>\n";
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
        } else {
            echo "<p><strong>原始响应:</strong> " . htmlspecialchars($result1['response']) . "</p>\n";
        }
    }
    
    // 测试无效token的情况
    echo "<h3>2.2 无效token测试</h3>\n";
    $result2 = makeRequest($baseUrl . 'user/refresh', 'POST', [
        'language' => 'CN'
    ], [
        'Authorization: Bearer invalid_token_12345',
        'Content-Type: application/x-www-form-urlencoded'
    ]);
    
    echo "<p><strong>HTTP状态码:</strong> {$result2['httpCode']}</p>\n";
    
    if ($result2['httpCode'] === 401) {
        echo "<p style='color: green;'>✅ 正确拒绝无效token</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ HTTP状态码: {$result2['httpCode']}</p>\n";
    }
    
    if ($result2['response']) {
        $data = json_decode($result2['response'], true);
        if ($data) {
            echo "<p><strong>响应数据:</strong></p>\n";
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
        } else {
            echo "<p><strong>原始响应:</strong> " . htmlspecialchars($result2['response']) . "</p>\n";
        }
    }
    
    echo "<hr>\n";
}

/**
 * 测试总结
 */
function testSummary() {
    echo "<h2>3. 测试总结</h2>\n";
    
    echo "<h3>✅ 已修复的404错误:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>/api/health</strong> - 健康检查接口已添加</li>\n";
    echo "<li><strong>/api/user/refresh</strong> - 会话刷新接口已添加并配置认证</li>\n";
    echo "</ul>\n";
    
    echo "<h3>🔧 技术实现:</h3>\n";
    echo "<ul>\n";
    echo "<li>健康检查接口返回服务器状态信息</li>\n";
    echo "<li>会话刷新接口使用auth:api中间件保护</li>\n";
    echo "<li>统一的响应格式 (status, msg, data)</li>\n";
    echo "<li>适当的错误处理和状态码</li>\n";
    echo "</ul>\n";
    
    echo "<h3>📋 下一步建议:</h3>\n";
    echo "<ul>\n";
    echo "<li>使用有效token测试会话刷新功能</li>\n";
    echo "<li>在前端测试页面验证接口集成</li>\n";
    echo "<li>监控生产环境中的接口使用情况</li>\n";
    echo "</ul>\n";
}

// 运行所有测试
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
hr { margin: 20px 0; border: 1px solid #ddd; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>\n";

testHealthAPI($baseUrl);
testRefreshAPI($baseUrl);
testSummary();

echo "<p><strong>测试完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
