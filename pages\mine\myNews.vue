<template>
	<view class="list_wrap">
		<view class="listItem" v-for="(item, index) in newsList" :key="index">
			<view class="listItem-top">
				<view class="listItem-txt">{{item.title}}<span>{{item.updated_at}}</span></view>
			</view>
			<view class="listItem-det">{{item.body.replace(/<.*?>/g,"")}}</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newsList:[],
                total: 0,
                pageIndex: 1,
                pageSize: 10,
                hasMore: false,
                status: 'more',
                contentText: {
                    contentdown: '查看更多',
                    contentrefresh: '加载中',
                    contentnomore: '没有更多了'
                },
				staffId: 0,
				uname: ''
			}
		},
		onLoad(e){
			this.queryByInput()
		},
		computed: {
		    i18n () {
		       return this.$t("mine")
		    },
			more() {
				return this.$t("contentText")
			},
			myNews() {
				return this.$t("myNews")
			}
		},
		filters:{
			timeDep(timestamp){
				let date;
				if((timestamp + '').length == "10"){
				    date = new Date(timestamp * 1000); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
				}
				else{
				    date = new Date(timestamp); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
				}
				var Y = date.getFullYear() + "-";
				var M =
				  (date.getMonth() + 1 < 10
					? "0" + (date.getMonth() + 1)
					: date.getMonth() + 1) + "-";
				var D = date.getDate() + " ";
				var h = date.getHours();
				var m = date.getMinutes() ;
				var s = date.getSeconds();
				if(h >=0 && h <=9) {
										h = '0' + h
									}
									if(m >=0 && m <=9) {
										m = '0' + m
									}
									if(s >=0 && s <=9) {
										s = '0' + s
									}
				// return Y + M + D + h + m + s;
				return Y + M + D + h + ":" + m + ":" + s ;
			}
		},
		onShow(){
			this.contentText = {
				contentdown: this.more.contentdown,
				contentrefresh: this.more.contentrefresh,
				contentnomore: this.more.contentnomore
			}
		},
		onReady(){
			uni.setNavigationBarTitle({
				title:this.i18n.msg
			})
		},
        //上拉加载
        onReachBottom(){
            if (this.status == 'noMore'){
                return;
            }
            this.pageIndex ++;
            this.getNewsList();
        },
        //下拉刷新
        onPullDownRefresh(){
            uni.stopPullDownRefresh();
            this.newsList = [];
            this.queryByInput()
        },
		methods: {
            // 初始数据
            queryByInput(){
                this.getUserInfo();
            },
			// 获取用户信息
			getUserInfo(){
			    this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
			        if(res.status == 200){
						this.staffId = res.data.staff_id
						this.uname = res.data.uname
						this.getNewsList() // 获取公告列表
			        } else {
			            uni.showToast({
			                title: res.msg,
			                duration: 1500,
			                icon:'none'
			            });
			        }
			    })
			},
			// 获取消息列表
			getNewsList(){
                var that = this;
			    this.$tools.Post("api/user/letter", {
					api_token: uni.getStorageSync('token'),
					staff_id: this.staffId,
					uname: this.uname,
					language: uni.getStorageSync('lang')
				}).then((res) =>{
			        if(res.status == 200){
			            //这里只会在接口是成功状态返回
			            that.newsList = res.data
			        } else {
			            uni.showToast({
			                title: res.msg,
			                duration: 1500,
			                icon:'none'
			            });
			        }
			    }).catch(function(error) {
                    //这里只会在接口是失败状态返回，不需要去处理错误提示
                    console.log(error);
                });
			},
		}
	}
</script>

<style lang="less" scoped>
	.item_head{
		text-align: left;
		font-size: 30rpx;
		margin-bottom: 8rpx;
	}
	.itemMsg{

		// border-bottom: 1px solid #ddd;
	}
	.item_time{
		margin-top: 8rpx;
		text-align: right;
		font-size: 20rpx;
		color: #999999;
	}
	page{
		background-color: #fff;
		.list_wrap{
			.listItem{
				margin: 0 20rpx;
				border-bottom: 1rpx solid #E0E0E0;
				padding-bottom: 36rpx;
				padding-top: 33rpx;
				.listItem-top{
					height: 80rpx;
					display: flex;
					align-items: center;
				}
				.listItem-txt{
					font: normal normal medium 26rpx/26rpx Roboto;
					letter-spacing: 0;
					color: #38383D;
					font-weight: bold;
					height: 80rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					span{
						font: normal normal normal 24rpx/24rpx Roboto;
						letter-spacing: 0;
						color: #787878;
						margin-top: 14rpx;
					}
				}
				.listItem-det{;
					font: normal normal normal 26rpx/38rpx Roboto;
					letter-spacing: 0;
					color: #212121;
					margin-top: 26rpx;
				}
			}
		}
	}

</style>
