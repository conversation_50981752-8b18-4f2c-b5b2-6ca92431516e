<template>
    <view class="container">
        <view class="list_wrap">
        	<view class="list" v-for="(item,index) in list" :key='index'>
                <view class="list-top">
                    <span :class="parseFloat(item.price)>0?'red':'green'" v-if="parseInt(item.action)===1">{{$t('new.billTxt1')}}</span>
                    <span :class="parseFloat(item.price)>0?'red':'green'" v-if="parseInt(item.action)===2">{{$t('new.billTxt2')}}</span>
                    <span :class="parseFloat(item.price)>0?'red':'green'" v-if="parseInt(item.action)===3">{{$t('new.billTxt3')}}</span>
                    <span :class="parseFloat(item.price)>0?'red':'green'" v-if="parseInt(item.action)===4">{{$t('new.billTxt4')}}</span>
                    <span :class="parseFloat(item.price)>0?'red':'green'" v-if="parseInt(item.action)===5">{{$t('new.billTxt5')}}</span>
                    {{item.price}}
                </view>
                <view class="list-foot">{{item.created_at}}<span>{{item.action_user}}</span></view>
        	</view>
            <view class="example-body">
                <uni-load-more :status="status" color="#000" :content-text="contentText"/>
            </view>
        </view>

    </view>
</template>

<script>
    import uniLoadMore from "@/components/uni-load-more/uni-load-more.vue"
	import {format} from "@/static/js/utils/filters.js"
	export default {
		data() {
			return {
                total: 0,
                pageIndex: 1,
                pageSize: 1000000,
                timeCreatedStart:"",
                timeCreatedEnd:"",
                hasMore: false,
                status: 'more',
                contentText: {
                    contentdown: '查看更多',
                    contentrefresh: '加载中',
                    contentnomore: '没有更多了'
                },
                title:"",
				type:"",
                index:0,
                pickerName:"全部",
                timeArray:[
                    {name:"全部",time:"1"},
                    {name:"今天",time:"2"},
                    {name:"昨天",time:"3"},
                    {name:"七天",time:"4"}
                ],
                list:[],
				userInfo: {}
			}
		},
		onLoad(e){
            this.getUserInfo()
		},
        computed: {
            i18n () {
               return this.$t("bill")
            },
			more() {
				return this.$t("contentText")
			}
        },
		onShow(){
            uni.setNavigationBarTitle({
                title: this.i18n.head_title
            })
			this.timeArray = [
                    {name:this.i18n.all,time:"1"},
                    {name:this.i18n.today,time:"2"},
                    {name:this.i18n.yesday,time:"3"},
                    {name:this.i18n.seven,time:"4"}
            ],
			this.pickerName = this.i18n.all
			// this.contentText = {
			// 	contentdown: this.more.contentdown,
			// 	contentrefresh: this.more.contentrefresh,
			// 	contentnomore: this.more.contentnomore
			// }
			this.contentText = {
				contentdown: '',
				contentrefresh: '',
				contentnomore: ''
			}
		},

		onReady(e){
		},

		methods: {
            // 初始数据
            queryByInput(type){
                this.pageIndex = 1;
                this.list = [];
                this.getUserInfo()
            },

            //上拉加载
            onReachBottom(){
                if (this.status == 'noMore'){
                    return;
                }
                this.pageIndex ++;
                //this.getBilList()
            },

            //下拉刷新
            onPullDownRefresh(){
                uni.stopPullDownRefresh();
                //this.list = [];
                //this.queryByInput()
            },
            // 获取用户信息
            getUserInfo(){
                this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
                    language: uni.getStorageSync('lang')
				}).then((res) =>{
                    if(res.status == 200){
                        this.userInfo = res.data
						this.getBilList()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },

            // 获取账单记录
            getBilList(){
                var that = this;
                var params = {
					api_token: uni.getStorageSync('token'),
					uid: this.userInfo.uid,
                    language: uni.getStorageSync('lang')
                }
                this.$tools.Post("api/user/bill",params).then((res) =>{
                    if(res.status == 200){
                        //这里只会在接口是成功状态返回
                        that.list = res.data.reverse()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },

            bindPickerChange(e){
            	this.index = e.detail.value;
            	this.pickerName = this.timeArray[this.index].name;
                let range = this.timeArray[this.index].time;
                let date = (format(new Date())).slice(0, 10)    // 今天的年月日
                let startTime = date + ' ' + '00:00:00'         //今天年月日+00:00:00
                let nowTime = format(new Date());               //当前时间日期
                if(range=="1"){//全部
                    this.timeCreatedStart = "";
                    this.timeCreatedEnd = "";
                }
                else if(range=="2"){//今天
                    this.timeCreatedStart = (new Date(startTime).getTime())/1000;
                    this.timeCreatedEnd = (new Date(nowTime).getTime())/1000;
                }
                else if(range=="3"){//昨天
                    this.timeCreatedStart = ((new Date(startTime).getTime()) - 24 * 3600 *1000)/1000        //昨天年月日+00:00:00 （秒）
                    this.timeCreatedEnd = (new Date(startTime).getTime())/1000;                            //昨天年月日+23:59:59（秒）
                }
                else if(range=="4"){//七天
                    this.timeCreatedStart = ((new Date(startTime).getTime()) - 7 * 24 * 3600 *1000)/1000;         //七天前年月日+00:00:00 （秒）
                    this.timeCreatedEnd = (new Date(nowTime).getTime())/1000;                               //当前时间转 （秒）
                }
                this.queryByInput()
            },

			navigateBack(){
                uni.navigateBack({
                    delta: 1
                });
            }
		},


        components:{
            uniLoadMore
        }
	}
</script>

<style lang="less" scoped>
	page{
		background-color: rgba(245, 245, 245, 1);
        .container{
            .nav_wrap{
                position: sticky;
                left: 0;
                right: 0;
                top:0;
                width: 100%;
                height: 90rpx;


                display: flex;
                align-items: center;
                justify-content: space-between;
                background: rgba(23, 45, 82, 1);
                .backIcon{
                    height: 60rpx;
                    width: 60rpx;
                    border-radius: 50%;
                    background-size: 18rpx 32rpx;
                }
                .title{
                    font: normal normal normal 17px/212px Roboto;
                    letter-spacing: 0px;
                    color: #FFFFFF;
                }
                .picker_wrap{
                    width: 60rpx;
                }
            }
            .list_wrap{
            	padding: 0 20rpx;
            	.list{
                    height: 150rpx;
                    background: #FFFFFF;
                    border-radius: 20rpx;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    padding: 0 20rpx;
                    margin-top: 20rpx;
                    .list-top{
                        font-size: 32rpx;
                        font-family: PingFang SC;
                        font-weight: bold;
                        color: #333333;
                        height: 44rpx;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        .red{
                            font-size: 32rpx;
                            font-family: PingFang SC;
                            font-weight: bold;
                            color: #333333;
                        }
                        .green{
                            font-size: 32rpx;
                            font-family: PingFang SC;
                            font-weight: bold;
                            color: #333333;
                        }
                    }
                    .list-foot{
                        height: 40rpx;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 28rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        color: #999999;
                        margin-top: 20rpx;
                        span{
                            font-size: 28rpx;
                            font-family: PingFang SC;
                            font-weight: 500;
                            color: #787878;
                        }
                    }
            	}
            }
        }

	}

</style>
