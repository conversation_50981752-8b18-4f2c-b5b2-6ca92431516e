<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\AdminUser;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Modal;
use App\Admin\Renderable\BillTable;
use Dcat\Admin\Http\Controllers\AdminController;

class AdminUserController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AdminUser(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('username');
            $grid->column('name');

            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
              
                 // append一个操作
                 $code = Modal::make()
                 ->xl()
                 ->title('邀请码')
                 ->body(BillTable::make(['id' => $this->id,'uid' =>$this->uid ])) // Modal 组件支持直接传递 渲染类实例
                 ->button('<i class="fa fa-file-text-o"></i>');
                 $actions->append($code);
         
              
             });


            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AdminUser(), function (Show $show) {
            $show->field('id');
            $show->field('username');
            $show->field('password');
            $show->field('name');
            $show->field('avatar');
            $show->field('remember_token');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AdminUser(), function (Form $form) {
            $form->display('id');
            $form->text('username');
            $form->text('password');
            $form->text('name');
            $form->text('avatar');
            $form->text('remember_token');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
