<template>
    <view class="container">
        <view class="list_wrap">
            <view class="item">
                <view class="title">{{$t('new').rechargeTxt1}}</view>
                <radio-group class="ridioBox" @change="radioChange">
                    <label class="radio" v-for="(item, index) in paytypecodeList" :key="item.value">
                        {{item.name}}
                        <radio :value="item.value" :checked="index === current" color="rgba(101, 177, 29, 1)" />
                    </label>
                </radio-group>
            </view>
            <view class="item" v-if="current===15">
                <view class="title">{{$t('new').rechargeTxt2}}</view>
                <input type="text" v-model="payname" :placeholder="$t('new').rechargeTip2" placeholder-class="input-placeholder"/>
            </view>
            <view class="item" v-if="current===15">
                <view class="title">{{$t('new').rechargeTxt3}}</view>
                <input type="text" v-model="payemail" :placeholder="$t('new').rechargeTip3" placeholder-class="input-placeholder"/>
            </view>
            <view class="item" v-if="current===15">
                <view class="title">{{$t('new').rechargeTxt4}}</view>
                <input type="number" v-model="payphone" :placeholder="$t('new').rechargeTip4" placeholder-class="input-placeholder"/>
            </view>
            <view class="item">
                <view class="title">{{$t('new').rechargeTxt5}}</view>
                <input type="number" v-model="amount" :placeholder="$t('new').rechargeTip5" placeholder-class="input-placeholder"/>
            </view>
        </view>

        <view class="submit" @click="set" v-if="sub===false">{{$t('new.rechargeBtn')}}</view>
        <view class="submit" v-else>{{$t('new.rechargeBtn')}}……</view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                payname: '',
                payemail: '',
                payphone: '',
                amount: '',
                paytypecode: '',
                paytypecodeList: [],
                current: 0,
                userInfo: {},
                sub: false
            }
        },
        onLoad(){

        },

        onShow(){
            this.getList()
            this.getUserInfo()
        },
        onReady(){
            uni.setNavigationBarTitle({
                title:this.$t('new.rechargeTitle')
            })
        },
        methods: {
            radioChange: function(evt) {
                for (let i = 0; i < this.paytypecodeList.length; i++) {
                    if (this.paytypecodeList[i].value === evt.detail.value) {
                        this.current = i;
                        console.log(this.current)
                        break;
                    }
                }
            },
            getList(){
                this.$tools.Get("api/play/list", {
                    api_token: uni.getStorageSync('token'),
                    language: uni.getStorageSync('lang')
                }).then((res) =>{
                    if(res.status == 200){
                        this.paytypecodeList = []
                        let list = res.data
                        let listLength = list.length
                        let a
                        for(a=0;a<listLength;a++){
                            this.paytypecodeList.push({
                                value: list[a].month_code,
                                name: list[a].month_name,
                                type: list[a].payname
                            })
                        }
                        console.log(res.data)
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
            // 获取用户信息
            getUserInfo(){
                this.$tools.Post("api/user/info", {
                    api_token: uni.getStorageSync('token'),
                    language: uni.getStorageSync('lang')
                }).then((res) =>{
                    if(res.status == 200){
                        this.userInfo = res.data;
                        uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
            // 充值
            set(){
                if(this.sub === false){
                    this.sub = true
                    if(this.paytypecodeList[this.current].value==='12006' || this.paytypecodeList[this.current].value==='1094'){
                        if(!this.payname){
                            uni.showToast({
                                title: this.$t('new.rechargeTip2'),
                                duration: 1500,
                                icon:'none'
                            });
                            this.sub = false
                            return false
                        }
                        if(!this.payemail){
                            uni.showToast({
                                title: this.$t('new.rechargeTip3'),
                                duration: 1500,
                                icon:'none'
                            });
                            this.sub = false
                            return false
                        }
                        let mailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;
                        if(!mailReg.test(this.payemail)){
                            uni.showToast({
                                title: this.$t('new.rechargeTip6'),
                                duration: 1500,
                                icon:'none'
                            });
                            this.sub = false
                            return false
                        }
                        if(!this.payphone){
                            uni.showToast({
                                title: this.$t('new.rechargeTip4'),
                                duration: 1500,
                                icon:'none'
                            });
                            this.sub = false
                            return false
                        }
                    }
                    if(!this.amount){
                        uni.showToast({
                            title: this.$t('new.rechargeTip5'),
                            duration: 1500,
                            icon:'none'
                        });
                        this.sub = false
                        return false
                    }
                    if(parseInt(this.amount)<50000 || !this.amount){
                        uni.showToast({
                            title: this.$t('new.rechargeTip1'),
                            duration: 1500,
                            icon:'none'
                        });
                        this.sub = false
                        return false
                    }
                    if(parseInt(this.amount)>100000000 || !this.amount){
                        uni.showToast({
                            title: this.$t('new3.rechargeTip1'),
                            duration: 1500,
                            icon:'none'
                        });
                        this.sub = false
                        return false
                    }
                    this.$tools.Post("api/playfastpay", {
                        api_token: uni.getStorageSync('token'),
                        action: 'pay',
                        amount: this.amount,
                        payemail: this.payemail,
                        payname: this.payname,
                        payphone: this.payphone,
                        paytypecode: this.paytypecodeList[this.current].value,
                        bank_server: this.paytypecodeList[this.current].type,
                        uname: this.userInfo.uname,
                        uid: this.userInfo.uid,
                        user_amount: this.userInfo.price
                    }).then((res) =>{
                        if(this.paytypecodeList[this.current].type==='fastpay'){
                            if(res.status == 'success'){
                                window.open(res.order_data)
                                this.sub = false
                            } else {
                                uni.showToast({
                                    title: res.status_mes,
                                    duration: 1500,
                                    icon:'none'
                                });
                                this.sub = false
                            }
                        }else{
                            if(res.message == 'success'){
                                window.open(res.data.url)
                                this.sub = false
                            } else {
                                uni.showToast({
                                    title: res.message,
                                    duration: 1500,
                                    icon:'none'
                                });
                                this.sub = false
                            }
                        }
                    })
                }
            },
        }
    }
</script>

<style scoped lang="less">
    page{
        background: #fff;
        .container{
            padding: 0 30rpx;
            .list_wrap{
                .item{
                    .title{
                        height: 95rpx;
                        display: flex;
                        align-items: center;
                        font-size: 32rpx;
                        font-family: PingFang SC;
                        font-weight: bold;
                        color: #333333;
                    }
                    .input-placeholder{
                        font-size: 32rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        color: #B3B3B3;
                    }
                    input{
                        height: 88rpx;
                        background: #EBEFF5;
                        border: 1rpx solid #172D52;
                        border-radius: 20rpx;
                        padding: 0 22rpx;
                        font-size: 32rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        color: #333;
                    }

                }
            }
            .submit{
                height: 100rpx;
                background: #65B11D;
                border-radius: 20rpx;
                font-size: 32rpx;
                font-family: PingFang SC;
                font-weight: bold;
                color: #FFFFFF;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 60rpx;
            }
        }
        .ridioBox{
            display: flex;
            flex-wrap: wrap;
            .radio{
                width: 100%;
                height: 80rpx;
                background: #FFFFFF;
                border: 1rpx solid #172D52;
                border-radius: 20rpx;
                margin: 5rpx 0;
                padding: 0 23rpx;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
        }
    }
</style>
