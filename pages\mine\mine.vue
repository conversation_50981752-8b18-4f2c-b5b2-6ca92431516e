<template>
	<view class="container">
		<view class="top">
			<image class="top-set" src="../../static/img/set.png" @click="toRecord('/pages/mine/user')" />
		</view>
		<view class="topBox">
			<view class="topBox-top">
				<view class="topBox-head"><image src="../../static/img/touxiang.png" /></view>
				<view class="topBox-det">
					<view class="topBox-name-wrap">
						<view class="topBox-name">{{userInfo.uname || '　'}}</view>
					</view>
					<view class="topBox-con">
						<view class="topBox-vip" v-if="userInfo.lv" @click="toRecord('/pages/mine/level')">
							<image class="vip" src="../../static/img/vip.png" />{{$t('new3.txt'+userInfo.lv.replace("VIP",""))}}
						</view>
						<view class="topBox-vip" v-else @click="toRecord('/pages/mine/level')">
							<image class="vip" src="../../static/img/vip.png" />
							{{$t('new3.txt'+sendLv)}}
						</view>
						<view class="topBox-vip">
							<image class="hron" src="../../static/img/rongyu.png" />{{$t('new.mineNew1')}}{{userInfo.credit || 0}}
						</view>
					</view>
				</view>
			</view>
			<view class="topBox-foot">
				<view class="topBox-item">
					<view class="topBox-item-top">
						{{$t('new.mineTit1')}}
					</view>
					<view class="topBox-item-foot">{{thousand(userInfo.price || 0)}}</view>
				</view>
			</view>
		</view>

		<view class="colorLine" @click="toRecord('/pages/activity/activity')">{{$t('new.mineMenu1')}}</view>
		<view class="menu">
			<view class="menu-list" @click="toRecord('/pages/mine/user')">
				<image class="menu-icon" src="../../static/img/mine1.png" />{{$t('new.mineMenu2')}}
			</view>
			<view class="menu-list" @click="toRecord('/pages/mine/billRecord')">
				<image class="menu-icon" src="../../static/img/mine2.png" />{{$t('new.mineMenu3')}}
			</view>
			<view class="menu-list" @click="toRecord('/pages/mine/level')">
				<image class="menu-icon" src="../../static/img/mine3.png" />{{$t('new.mineMenu5')}}
			</view>
			<view class="menu-list" @click="toRecord('/pages/mine/myNews')">
				<image class="menu-icon" src="../../static/img/mine4.png" />{{$t('new.mineMenu6')}}
			</view>
			<view class="menu-list" @click="toRecord('/pages/mine/kefu')">
				<image class="menu-icon" src="../../static/img/mine5.png" />{{$t('new2.txt3')}}
			</view>
			<view class="menu-list" @click="toRecord('/pages/public/lang')">
				<image class="menu-icon" src="../../static/img/mine6.png" />{{$t('new.mineMenu8')}}
			</view>
		</view>
		<view class="out" @click="outLogin()">{{$t('new.userOut')}}</view>
		<bottom :num='5'></bottom>
		<a-tc></a-tc>
	</view>
</template>

<script>
	import bottom from '../home/<USER>'
	
	export default {
		data() {
			return {
                userInfo: {},
				list:[],
				recordsAll:[],
				levelArr: [],
				sendLv: 0,
				see1: true,
				see2: true,
				keti: 0
			}
		},
        onLoad() {
			uni.showLoading({
				title: ''
			});
        },
		computed: {
		    i18n () {
		       return this.$t("mine")
		    }
		},
		components:{
			bottom
		},
        onShow(){
			if(!uni.getStorageSync('token')){
				uni.reLaunch({
					url: '/pages/public/login'
				})
				return;
			}
			this.getVip();
        	this.getUserInfo();
			this.list = [
				{name:this.i18n.card,cnName:"银行卡管理",url:'',path:"cardManage"},
				{name:this.i18n.gong,cnName:"网站公告",url:'',path:"noticeList"},
			]
        },

		methods: {
			getVip(){
				this.$tools.Get('api/user/lv', {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) => {
					this.levelArr = res.data;
					let length = this.levelArr.length;
					if(!this.userInfo.lv){
						for(let a = 0; a < length; a++){
							if(parseFloat(this.levelArr[a].price) < parseFloat(this.userInfo.price) && a < length){
								this.sendLv = this.levelArr[a].lv;
							}
						}
					}
				}).catch((error) => {
					console.log(error);
				});
			},
			
			outLogin() {
				uni.showToast({
				    title: this.i18n.outClear,
				    duration: 1500,
				    icon:'none'
				});
				setTimeout(function(){
					uni.reLaunch({
						url: '/pages/public/login'
					})
				},2000)
			},
			
			thousand(num) {
			    return Math.floor(num).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
			},
			
			refresh(){
				uni.showLoading({
					title: ''
				});
				this.getUserInfo();
			},
			
            getUserInfo(){
                this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
                    if(res.status == 200){
                        this.userInfo = res.data;
						this.getKeti();
                        uni.setStorageSync('realName', this.userInfo.bname);
						uni.setStorageSync('phone', this.userInfo.phone);
						uni.setStorageSync('username', this.userInfo.uname);
						uni.hideLoading();
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                }).catch(() => {
					uni.hideLoading();
				});
            },
			
			getKeti(){
				this.$tools.Post("api/user/get_user_yue", {
					api_token: uni.getStorageSync('token'),
					uid: this.userInfo.uid,
					language: uni.getStorageSync('lang')
				}).then((res) =>{
					if(res.status == 200){
						this.keti = res.data.nowdm;
					} else {
						uni.showToast({
							title: res.msg,
							duration: 1500,
							icon:'none'
						});
					}
				});
			},

			toRecord(value){
				uni.navigateTo({
					url: value
				});
			}
		}
	}
</script>

<style lang="less" scoped>
    @import url('../../static/css/mine.less');
	
	.topBox {
		.topBox-top {
			.topBox-det {
				.topBox-name-wrap {
					display: flex;
					justify-content: center;
					margin-bottom: 20rpx;
					
					.topBox-name {
						font-size: 40rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #95E32E;
					}
				}
			}
		}
	}
</style>
