<template>
    <view>
        <view class="search">
            <view class="search-box">
                <image src="../../static/img/home-search.png" class="search-icon" />
                <input class="search-input" v-model="key" :placeholder="$t('new.bankSearch')" />
            </view>
        </view>
        <view class="title">{{$t('new.bankTxt1')}}</view>
        <view class="list" v-for="(item, index) in list" :key="index" v-show="item.bankname.indexOf(key) !== -1" @click="addCard(item.bankname, item.ico)">
            <view class="list-left">{{item.bankname}}</view>
            <image src="../../static/img/mine-arrow.png" class="list-right" />
        </view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                key: '',
                list: []
            }
        },
        // 页面加载
        onLoad() {
            uni.setNavigationBarTitle({
                title: this.$t("bindcard").head_title
            })
            this.getBankCardList()
        },
        created() {

        },
        mounted() {

        },
        methods: {
            // 获取银行卡列表
            getBankCardList(){
                this.$tools.Get("api/bank/list", {
                    language: uni.getStorageSync('lang')
                }).then((res) =>{
                    if(res.status == 200){
                        this.list = [...res.data]
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
            addCard(name, img){
                uni.navigateTo({
                    url: '/pages/mine/bindCard?bank='+name+"&img="+img
                });
            }
        }
    }
</script>

<style lang="less" scoped>
    page {
        width: 100%;
        height: 100%;
        overflow-y: scroll;
        background: #fff;
        .search{
            height: 121rpx;
            display: flex;
            align-items: center;
            padding: 0 20rpx;
            border-bottom: 10rpx solid rgba(245, 245, 245, 1);
            .search-box{
                width: 100%;
                box-sizing: border-box;
                height: 80rpx;
                background: #FFFFFF;
                border: 1rpx solid #172D52;
                border-radius: 20rpx;
                padding: 0 36rpx;
                display: flex;
                align-items: center;
            }
            .search-icon{
                width: 33rpx;
                height: 33rpx;
                margin-right: 20rpx;
            }
            .search-input{
                width: calc(100% - 60rpx);
                height: 80rpx;
                line-height: 80rpx;
                font-size: 32rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #333;
            }
        }
        .title{
            height: 110rpx;
            padding: 0 30rpx;
            display: flex;
            align-items: center;
            background: #fff;
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #333333;
        }
        .list{
            margin: 0 32rpx;
            height: 89rpx;
            border-bottom: 1rpx solid #EEEEEE;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .list-left{
                height: 89rpx;
                display: flex;
                align-items: center;
                font: normal normal bold 28rpx/28rpx Roboto;
                letter-spacing: 0;
                color: #333333;
                image{
                    width: 48rpx;
                    height: 48rpx;
                    border-radius: 50%;
                    margin-right: 30rpx;
                }
            }
            .list-right{
                width: 18rpx;
                height: 33rpx;
            }
        }
    }
</style>
