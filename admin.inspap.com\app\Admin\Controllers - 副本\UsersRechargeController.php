<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UsersRecharge;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Http\Controllers\Tools;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Admin;
class UsersRechargeController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UsersRecharge(), function (Grid $grid) {

           // 复写了数据仓库
           $grid->model()->orderBy('id','desc'); //正式用户
            $grid->disableCreateButton();
            $grid->withBorder();
            $grid->column('id')->sortable();
            $grid->column('uname','用户账号');

    

            $grid->column('uid','持卡人')->display(function () {
                
                $re = User::where('uid',$this->uid)->first();
                return $re->bname;
                
            });
           
            $grid->column('num','充值笔数');


            $grid->column('action','类型')->display(function () {
                
                $actionType = Tools::actionType(); //操作方式

                return $actionType[$this->action];
                
            });
            $grid->column('price','充值金额');
            $grid->column('amount','变更前金额');

            $grid->column('status','状态')->display(function () {
                if($this->status==1){
                    return '正常';
                }
 
                else{
                    return '未知';
                }
                
            });

            $grid->column('msg','备注');
            $grid->column('action_user','操作人');
           
            $grid->column('created_at','充值日期');
        
            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('uname','用户名')->width(3);
        
            });
                 
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                //$actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();
                $rowArray = $actions->row->staff_id;
                if($rowArray==2){

                }
                print_r($rowArray);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UsersRecharge(), function (Show $show) {
            $show->field('id');
            $show->field('uid');
            $show->field('action');
            $show->field('price');
            $show->field('status');
            $show->field('msg');
            $show->field('action_user');
            $show->field('amount');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UsersRecharge(), function (Form $form) {
            $form->display('id');
            $form->text('uid');
            $form->text('action');
            $form->text('price');
            $form->text('status');
            $form->text('msg');
            $form->text('action_user');
            $form->text('amount');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
