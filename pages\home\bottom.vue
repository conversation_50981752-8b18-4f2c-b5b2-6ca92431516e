<template>
	<view>
		<view class="boty">
			<view class="xiank">
				<!-- 主页 -->
				<view class="tgh" @click="choose(1)">
					<view class="tgh-box">
						<image src="../../static/img/tab/home_on.png" v-show="num==1" />
						<image src="../../static/img/tab/home.png" v-show="num!=1" />
						<view class="webc" :class="num == 1?'active':''">{{$t('new.menu1')}}</view>
					</view>
					<view class="tgh-ball" v-if="num == 1"></view>
				</view>

				<!-- 订单 -->
				<view class="tgh" @click="choose(2)">
					<view class="tgh-box">
						<image src="../../static/img/tab/order_on.png"  v-show="num==2" />
						<image src="../../static/img/tab/order.png"  v-show="num!=2" />
						<view class="webc" :class="num == 2?'active':''">{{$t('new.menu2')}}</view>
					</view>
					<view class="tgh-ball" v-if="num == 2"></view>
				</view>

				<!-- 我的 -->
				<view class="tgh" @click="choose(5)">
					<view class="tgh-box">
						<image src="../../static/img/tab/me_on.png" v-show="num==5" />
						<image src="../../static/img/tab/me.png" v-show="num!=5" />
						<view class="webc" :class="num == 5?'active':''">{{$t('new.menu5')}}</view>
					</view>
					<view class="tgh-ball" v-if="num == 5"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:["num",],
		data() {
			return {}
		},
		methods: {
			choose(e){
				if(e==1){
					uni.navigateTo({ url: '/pages/home/<USER>' });
				}else if(e==2){
					uni.navigateTo({ url: '/pages/order/order' });
				}else if(e==5){
					uni.navigateTo({ url: '/pages/mine/mine' });
				}
			}
		}
	}
</script>

<style  lang="less" scoped='scoped'>
.boty{
	position: fixed;
	bottom:0;
	left:0;
	width:100%;
	height: 168rpx;
	z-index: 22;
	background: rgba(23, 45, 84, 1);
	.xiank{
		width:100%;
		height: 109rpx;
		display: flex;
		justify-content: space-between;
		.tgh{
			width:33.33%;  /* 关键修改：3项均分宽度 */
			height: 109rpx;
			position: relative;
			.tgh-box{
				width: 100%;
				height: 100%;
				position: absolute;
				left: 0;
				top: 0;
				z-index: 2;
				display: flex;
				align-items: center;
				flex-direction: column;
				image{
					width:48rpx;
					height:48rpx;
					margin-top: 12rpx;
				}
				.webc{
					font-size: 20rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #ADC2E7;
				}
				.active{
					color: #FFFFFF;
				}
			}
			.tgh-ball{
				width: 128rpx;
				height: 128rpx;
				background: linear-gradient(135deg, #97E62B, #669B43);
				border-radius: 50%;
				position: absolute;
				bottom: 0;
				z-index: 1;
				left: calc(50% - 64rpx);
			}
		}
	}
}
</style>