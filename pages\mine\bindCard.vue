<template>
	<view>
		<view class="title">{{$t('new.bankTxt2')}}</view>
		<view class="card" @click="addCard">{{bankName}}<image src="../../static/img/bank-arrow.png" /></view>
		<view class="box">
			<view class="list">
				<view class="list-txt">{{i18n.cardUser}}</view>
				<view class="list-box">
					<input v-model="realName" :placeholder="$t('new3.place_name')" confirm-type='done' placeholder-class="input-placeholder"/>
				</view>
			</view>
			<view class="list">
				<view class="list-txt">{{i18n.card}}</view>
				<view class="list-box">
					<input type="number" v-model="card" :placeholder="i18n.place_card" confirm-type='done' placeholder-class="input-placeholder"/>
				</view>
			</view>
		</view>
		<view class="tip"><image src="../../static/img/bank-icon.png" />{{$t('new.bankTip')}}</view>
		<view class="submit" @click="submit">{{i18n.submit}}</view>
	</view>
</template>

<script>
	export default {
        name: 'bindCard',
		data() {
			return {
                realName:"",
                bankName:"",
				img: "",
                card:"",
                isDisabled:true,
				userInfo: {}
			}
		},
        computed: {
            i18n () {
               return this.$t("bindcard")
            }
        },
        onLoad(options){
        	this.userName =  uni.getStorageSync('realName');
        	this.bankName = options.bank
			this.img = options.img
            if(!this.userName){
                this.isDisabled = false;
            }
            else{
                this.realName = this.userName;
            }
        },

        onShow(){
		  this.getUserInfo()
        },
        onReady(){
        	uni.setNavigationBarTitle({
        		title:this.i18n.head_title
        	})
        },

		methods: {
			addCard(){
				uni.navigateTo({
					url: '/pages/mine/bankList'
				});
			},
            // 获取用户信息
            getUserInfo(){
                this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
                    if(res.status == 200){
                        this.userInfo = res.data;
                        this.realName = this.userInfo.bname
						uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
			submit(){
                if(this.bankName == ''){
                    uni.showToast({
                        title: this.i18n.place_bank,
                        duration: 1500,
                        icon:'none'
                    });
                    return false
                }else if(this.card == ''){
                    uni.showToast({
                        title: this.i18n.place_card,
                        duration: 1500,
                        icon:'none'
                    });
                    return false
                }else if(this.realName == ''){
					uni.showToast({
						title: this.$t('new3.place_name'),
						duration: 1500,
						icon:'none'
					});
					return false
				}
                uni.showLoading()
                let pamars = {
					uid: this.userInfo.uid,
					bname: this.realName,
					bank_name:this.bankName,
					bank_id:this.card,
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
					}
                this.$tools.Post("api/user/bank/add",pamars).then((res) =>{
                    if(res.status == 200){
						uni.hideLoading()
                        uni.showToast({
                            title: this.i18n.succ,
                            duration: 1500,
                            icon:'success',
                            success(){
								uni.navigateTo({
									url: '/pages/mine/cardManage'
								});
                            }
                        });
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'error',
                        });
                    }
                })
            }
		}
	}
</script>

<style scoped lang="less">
	page{
		background: #fff;
		.submit{
			height: 100rpx;
			background: #65B11D;
			border-radius: 20rpx;
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 60rpx 35rpx 0 35rpx;
		}
		.title{
			height: 93rpx;
			padding: 0 40rpx;
			display: flex;
			align-items: center;
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #333333;
		}
		.card{
			height: 120rpx;
			margin: 0 30rpx;
			background: url("../../static/img/bank-card.png");
			background-size: 100% 100%;
			background-repeat: no-repeat;
			padding: 0 19rpx;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			font-size: 36rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #FFFFFF;
			image{
				width: 14rpx;
				height: 24rpx;
				margin-left: 21rpx;
			}
		}
		.box{
			padding: 0 30rpx;
		}
		.list{
			display: flex;
			flex-direction: column;
			justify-content: center;
			.list-txt{
				height: 90rpx;
				padding: 0 10rpx;
				display: flex;
				align-items: center;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #333333;
			}
			.list-box{
				height: 80rpx;
				background: #FFFFFF;
				border: 1rpx solid #172D52;
				border-radius: 20rpx;
				padding: 0 22rpx;
				display: flex;
				align-items: center;
				.list-box-name{
					font-size: 32rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
				}
				input{
					width: 100%;
					font-size: 32rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
					height: 80rpx;
					line-height: 80rpx;
				}
			}
		}
		.tip{
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #333333;
			display: flex;
			align-items: center;
			margin: 39rpx 40rpx 0 40rpx;
			image{
				width: 22rpx;
				height: 26rpx;
				margin-right: 10rpx;
			}
		}
	}
</style>
