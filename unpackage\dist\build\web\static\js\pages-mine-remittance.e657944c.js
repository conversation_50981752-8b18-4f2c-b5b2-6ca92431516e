(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-mine-remittance"],{"055c":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("e966"),n("5ef2"),n("64aa");var a=i(n("b7c7")),o={data:function(){return{index:0,bankId:"",bankCard:"请选择回款账户",bankName:"",money:"",array:[{name:"王立",account:"555555"},{name:"张三",account:"666"}],withdrawalInfo:{},bankCardList:[],bankCardList2:[],backMoney:"",shenTime:"",timer:null,full:0,minPrice:0}},onLoad:function(){this.getWi(),this.getWithdrawalInfo()},computed:{i18n:function(){return this.$t("remittance")}},onShow:function(){this.bankCard=this.i18n.select},onUnload:function(){},onReady:function(){uni.setNavigationBarTitle({title:this.i18n.head_title})},methods:{toRecord:function(t){uni.navigateTo({url:t})},getTime:function(){var t=new Date,e=t.getFullYear(),n=t.getMonth()+1,i=t.getDate(),a=t.getHours()<10?"0"+t.getHours():t.getHours(),o=t.getMinutes()<10?"0"+t.getMinutes():t.getMinutes(),r=t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds();n>=1&&n<=9&&(n="0"+n),i>=0&&i<=9&&(i="0"+i);var d=e+"-"+n+"-"+i+" "+a+":"+o+":"+r;return d},getBankCardList:function(){var t=this;this.$tools.Post("api/user/bank/get",{uid:this.withdrawalInfo.uid,api_token:uni.getStorageSync("token"),language:uni.getStorageSync("lang")}).then((function(e){200==e.status?e.data&&(t.bankCardList=(0,a.default)(e.data),t.bankId=t.bankCardList[t.index].bank_id,t.bankCard=t.bankCardList[t.index].bank_id,t.bankName=t.bankCardList[t.index].bank_name):uni.showToast({title:e.msg,duration:1500,icon:"none"})}))},getWithdrawalInfo:function(){var t=this;this.$tools.Post("api/user/info",{api_token:uni.getStorageSync("token"),language:uni.getStorageSync("lang")}).then((function(e){200==e.status?(t.withdrawalInfo=e.data,t.backMoney=t.i18n.min+e.data.price,t.getBankCardList()):uni.showToast({title:e.msg,duration:1500,icon:"none"})}))},bindPickerChange:function(t){this.index=t.detail.value,this.bankId=this.bankCardList[this.index].bank_id,this.bankCard=this.bankCardList[this.index].bank_id,this.bankName=this.bankCardList[this.index].bank_name},getWi:function(){var t=this;this.$tools.Get("api/system/info",{language:uni.getStorageSync("lang")}).then((function(e){200===e.status?(t.full=e.data[0].is_int,t.minPrice=e.data[0].minPrice):uni.showToast({title:e.msg,duration:1500,icon:"none"})}))},submit:function(){return parseInt(this.withdrawalInfo.credit)<100?(uni.showToast({title:this.$t("new.mineNew2"),duration:1500,icon:"none"}),!1):1==this.full&&String(this.money).indexOf(".")>-1?(uni.showToast({title:this.i18n.noWith,duration:1500,icon:"none"}),!1):this.bankId?this.money?Number(this.money)<Number(this.minPrice)?(uni.showToast({title:this.i18n.wMin+this.minPrice,duration:1500,icon:"none"}),!1):void this.submitWithdrawal():(uni.showToast({title:this.i18n.toast_money,duration:1500,icon:"none"}),!1):(uni.showToast({title:this.i18n.select,duration:1500,icon:"none"}),!1)},submitWithdrawal:function(){var t=this,e={money:this.money,bank_id:this.bankId,bank_name:this.bankName,api_token:uni.getStorageSync("token"),uid:this.withdrawalInfo.uid,language:uni.getStorageSync("lang")};this.$tools.Post("api/user/withdraw/add",e).then((function(e){200==e.status?uni.showToast({title:t.i18n.succ,duration:1e3,icon:"none",success:function(){setTimeout((function(){uni.navigateTo({url:"/pages/mine/mine"})}),1e3)}}):uni.showToast({title:e.msg,duration:1500,icon:"none"})}))}}};e.default=o},"2e15":function(t,e,n){var i=n("fb47");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("31a5dcba",i,!0,{sourceMap:!1,shadowMode:!1})},"30f7":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("7a76"),n("c9b5")},"33cb":function(t,e,n){"use strict";n.r(e);var i=n("055c"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},4733:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(n("8d0b"))},"4b56":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("v-uni-view",{staticClass:"top"},[t._v(t._s(t.i18n.yue)+"："+t._s(Math.floor(Number(t.withdrawalInfo.price))))]),n("v-uni-view",{staticClass:"title"},[t._v(t._s(t.$t("new.remTxt1"))),n("span",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toRecord("/pages/mine/billRecord")}}},[t._v(t._s(t.$t("bill").head_title))])]),n("v-uni-view",{staticClass:"list"},[n("v-uni-view",{staticClass:"listItem"},[n("span",[t._v(t._s(t.i18n.user))]),n("v-uni-text",{staticClass:"right"},[t._v(t._s(t.withdrawalInfo.bname))])],1),n("v-uni-view",{staticClass:"listItem"},[n("span",[t._v(t._s(t.i18n.huizh))]),n("v-uni-picker",{staticStyle:{"margin-right":"-26rpx"},attrs:{value:t.index,range:t.bankCardList,"range-key":"bank_id"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindPickerChange.apply(void 0,arguments)}}},[n("v-uni-text",{staticClass:"uni-input"},[t._v(t._s(t.bankCard))])],1)],1),n("v-uni-view",{staticClass:"listItem",staticStyle:{border:"0"}},[n("span",[t._v(t._s(t.i18n.huiMoney))]),n("v-uni-input",{attrs:{type:"digit",placeholder:t.i18n.toast_money,"confirm-type":"done","placeholder-class":"input-placeholder"},model:{value:t.money,callback:function(e){t.money=e},expression:"money"}})],1)],1),n("v-uni-view",{staticClass:"btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submit.apply(void 0,arguments)}}},[t._v(t._s(t.i18n.submit))])],1)},a=[]},"97d0":function(t,e,n){"use strict";n.r(e);var i=n("4b56"),a=n("33cb");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("a3fd");var r=n("828b"),d=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"74bbcedb",null,!1,i["a"],void 0);e["default"]=d.exports},a3fd:function(t,e,n){"use strict";var i=n("2e15"),a=n.n(i);a.a},b7c7:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,a.default)(t)||(0,o.default)(t)||(0,r.default)()};var i=d(n("4733")),a=d(n("d14d")),o=d(n("5d6b")),r=d(n("30f7"));function d(t){return t&&t.__esModule?t:{default:t}}},d14d:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("08eb")},fb47:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,".footer[data-v-74bbcedb]{position:absolute;bottom:%?30?%;width:calc(100% - %?60?%)}.footer .title[data-v-74bbcedb]{font-size:16px;font-family:PingFang SC;font-weight:500;color:#999;margin-bottom:%?20?%}.footer .box[data-v-74bbcedb]{width:100%;border:1px solid #ffc0a9;border-radius:5px;overflow:hidden}.footer .box .item[data-v-74bbcedb]{display:flex}.footer .box .item .left[data-v-74bbcedb]{background-color:#fcd8cb;display:flex;height:%?72?%;align-items:center;width:%?230?%;font-size:16px;font-family:PingFang SC;font-weight:500;color:#e78f6f;text-align:center;justify-content:center}.footer .box .item .right[data-v-74bbcedb]{background-color:#fff8f5;flex:1;display:flex;height:%?72?%;align-items:center;padding-left:%?32?%}.footer .box .item[data-v-74bbcedb]:nth-child(2){border-top:1px solid #ffc0a9;border-bottom:1px solid #ffc0a9}.myBanlace[data-v-74bbcedb]{height:80px!important;background:linear-gradient(0deg,#fcbba3,#ffb194);border-radius:5px;display:flex}.myBanlace .item[data-v-74bbcedb]{margin:%?30?% 0 %?20?%;display:flex;flex-direction:column;flex:1;justify-content:space-between;border-right:1px solid #fff}.myBanlace .item .money[data-v-74bbcedb]{font-size:20px;font-family:Euclid Circular A;font-weight:500;color:#fff;text-align:center}.myBanlace .item .title[data-v-74bbcedb]{font-size:14px;font-family:PingFang SC;font-weight:500;color:#fff;text-align:center}.myBanlace .item[data-v-74bbcedb]:last-child{border-right:none}uni-page-body[data-v-74bbcedb]{background:#eee}body.?%PAGE?%[data-v-74bbcedb]{background:#eee}uni-page-body .top[data-v-74bbcedb]{height:%?166?%;display:flex;align-items:center;padding:0 %?32?%;font:normal normal 700 %?34?%/%?34?% Roboto;letter-spacing:0;background:#fff;color:#38383d}uni-page-body .title[data-v-74bbcedb]{height:%?114?%;display:flex;align-items:center;justify-content:space-between;font:normal normal 700 %?34?%/%?34?% Roboto;letter-spacing:0;color:#38383d;padding:0 %?32?%}uni-page-body .title span[data-v-74bbcedb]{font:normal normal medium %?28?%/%?28?% Roboto;letter-spacing:0;color:#65b11d}uni-page-body .list[data-v-74bbcedb]{height:%?378?%;background:#fff 0 0 no-repeat padding-box;border-radius:%?16?%;margin:0 %?32?%;padding:0 %?41?%}uni-page-body .list .listItem[data-v-74bbcedb]{height:%?120?%;display:flex;align-items:center;border-bottom:%?1?% solid #707070;justify-content:space-between}uni-page-body .list .listItem span[data-v-74bbcedb]{font:normal normal normal %?32?%/%?32?% Roboto;letter-spacing:0;color:#38383d}uni-page-body .list .listItem uni-input[data-v-74bbcedb]{text-align:right;font:normal normal 700 %?32?%/%?32?% Roboto;letter-spacing:0;color:#38383d}uni-page-body .list .listItem .right[data-v-74bbcedb]{font:normal normal 700 %?32?%/%?32?% Roboto;letter-spacing:0;color:#38383d}uni-page-body .list .listItem .uni-input[data-v-74bbcedb]{font:normal normal 700 %?32?%/%?32?% Roboto;letter-spacing:0;color:#38383d}uni-page-body .btn[data-v-74bbcedb]{height:%?100?%;background:#65b11d;border-radius:%?20?%;font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#fff;display:flex;align-items:center;justify-content:center;margin:%?60?% %?35?% 0 %?35?%}",""]),t.exports=e}}]);