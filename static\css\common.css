
	
*{
    font-size: 100%;
}
/* flex布局 */
.u-f,.u-f-ac,.u-f-ajc{
	display: flex;
}
.u-f-ac,.u-f-ajc{
	align-items: center;
}
.u-f-ajc{
	justify-content: center;
}
.u-f-jsb{
	justify-content: space-between;
}
.u-f1{
	flex: 1;
}
.u-f-column{
	/* flex-direction: column; */
}
::-webkit-scrollbar {
    display:none

}
.loading-more {
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-top: 10px;
    padding-bottom: 10px;
    text-align: center;
}
.loading-more-text{
	font-size: 16rpx;
	color: #999;
}
 view,image,text {
   box-sizing: border-box;
 }