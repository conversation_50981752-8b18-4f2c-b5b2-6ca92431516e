<template>
    <view class="home" >
		<view class="homeTop">
			<swiper class="swiper" :indicator-dots="indicatorDots" :circular="true" :autoplay="autoplay" :interval="interval" :duration="duration" indicator-color="rgba(0, 0, 0, .3)" indicator-active-color="#FFFFFF">
				<swiper-item v-for="(item, index) in imgList" :key="index">
					<image style="width: 100%;height:100%;" 
						:src="$tools.setImgUrl(item)" 
						:id="index"
						mode="aspectFill"
						:lazy-load="true"
						@load="imageLoaded(index)"
						@error="imageError(index)"
					/>
				</swiper-item>
			</swiper>
		</view>
		<view class="homeSearch">
			<image src="../../static/img/home-search.png" />
			<input :placeholder="$t('new.homeSearch')" v-model="key" />
		</view>
		<view class="homeBox">
			<view class="homeAll">
				<view class="homeAll-title">
					{{$t('new.homeTitle')}}
					<view class="homeAll-title-txt"><image src="../../static/img/home-card-icon.png" />official</view>
				</view>
				<view class="homeAll-box">
					<view class="homeAll-item" v-for="(item, index) in brand" :key="index" @click="toDetail(item,index)">
						<image :src="$tools.setImgUrl(item.images)" />
					</view>
				</view>
			</view>
		</view>
		<bottom :num='1'></bottom>
		<a-tc></a-tc>
		<network-status @retry="handleNetworkRetry" />
    </view>
</template>

<script>
	import HeaderTop from '../../components/common/headertopBar.vue'
	import bottom from './bottom.vue'
	import NetworkStatus from '@/components/network-status/network-status.vue'
	
	// 缓存key常量
	const BANNER_CACHE_KEY = 'home_banner_cache';
	const BANNER_CACHE_TIME = 5 * 60 * 1000; // 5分钟缓存时间
	
	export default {
		name: 'home',
		components:{
			HeaderTop,
			bottom,
			NetworkStatus
		},
		data() {
			return {
				key: '',
				userInfo:{},
				indicatorDots: true,
				autoplay: true,
				interval: 3000,
				duration: 500,
                noticeList:[],
                noticeList1: [],
				imgList:[],
                brand: [],
				loadedImages: new Set(),
				isLoading: false,
				bannerCache: {
					data: [],
					timestamp: 0
				},
				cacheCleanupTimer: null
			}
		},
		created() {
			// 启动定期清理缓存的定时器
			this.startCacheCleanup();
		},
		beforeDestroy() {
			// 清理定时器
			if(this.cacheCleanupTimer) {
				clearInterval(this.cacheCleanupTimer);
				this.cacheCleanupTimer = null;
			}
		},
        onShow() {
            this.loadBannerFromCache();
            this.getBrandList();
        },
		onHide() {
			// 页面隐藏时保存缓存
			this.saveBannerCache();
		},
        methods: {
			// 启动缓存清理定时器
			startCacheCleanup() {
				// 每小时检查一次缓存
				this.cacheCleanupTimer = setInterval(() => {
					this.cleanupExpiredCache();
				}, 60 * 60 * 1000);
			},
			
			// 清理过期缓存
			cleanupExpiredCache() {
				try {
					const now = Date.now();
					const cache = uni.getStorageSync(BANNER_CACHE_KEY);
					if (cache) {
						const { timestamp } = JSON.parse(cache);
						if (now - timestamp > BANNER_CACHE_TIME) {
							uni.removeStorageSync(BANNER_CACHE_KEY);
						}
					}
				} catch (error) {
					console.error('清理缓存错误:', error);
				}
			},
			
			// 从缓存加载banner数据
			loadBannerFromCache() {
				try {
					const cache = uni.getStorageSync(BANNER_CACHE_KEY);
					if (cache) {
						const { data, timestamp } = JSON.parse(cache);
						const now = Date.now();
						if (data && data.length > 0 && now - timestamp < BANNER_CACHE_TIME) {
							this.imgList = data;
							this.loadedImages.clear();
						}
					}
				} catch (error) {
					console.error('加载缓存错误:', error);
					// 缓存出错时清除
					uni.removeStorageSync(BANNER_CACHE_KEY);
				}
				// 无论是否有缓存，都重新获取最新数据
				this.getBannerList();
			},
			
			// 保存banner缓存
			saveBannerCache() {
				if (this.imgList.length > 0) {
					const cache = {
						data: this.imgList,
						timestamp: Date.now()
					};
					uni.setStorageSync(BANNER_CACHE_KEY, JSON.stringify(cache));
				}
			},
			
			// 异步更新缓存
			async updateBannerCache() {
				if (this.isLoading) return;
				this.isLoading = true;
				
				try {
					const res = await this.$tools.Get("api/system/info", {
						language: uni.getStorageSync('lang') || 'CN'
					});
					
					if (res.status === 200 && res.data && res.data[0] && res.data[0].banner) {
						const bannerData = JSON.parse(res.data[0].banner);
						if (Array.isArray(bannerData) && bannerData.length > 0) {
							const newBannerStrings = bannerData.map(b => typeof b === 'string' ? b : b.image).filter(Boolean);
							const oldDataStr = JSON.stringify(this.imgList);
							const newDataStr = JSON.stringify(newBannerStrings);
							
							if (oldDataStr !== newDataStr) {
								this.imgList = newBannerStrings;
								this.saveBannerCache();
							}
						}
					}
				} catch (error) {
					console.error('更新banner缓存错误：', error);
				} finally {
					this.isLoading = false;
				}
			},
			
            // 获取banner
            async getBannerList() {
				if (this.isLoading) return;
				this.isLoading = true;
				this.autoplay = false; // 加载期间暂停自动播放
				
                try {
                    const res = await this.$tools.Get("api/system/info", {
                        language: uni.getStorageSync('lang') || 'CN',
                        _t: Date.now() // 添加时间戳防止缓存
                    });

                    if (res.status === 200 && res.data && res.data[0] && res.data[0].banner) {
                        const bannerData = JSON.parse(res.data[0].banner);
                        if (Array.isArray(bannerData) && bannerData.length > 0) {
                            // 确保imgList是字符串数组
                            this.imgList = bannerData.map(b => typeof b === 'string' ? b : b.image).filter(Boolean);
                            this.loadedImages.clear(); // 清空已加载记录
                            // 保存到缓存
                            this.saveBannerCache();
                        } else {
                            console.warn('Banner数据为空或解析后非数组');
                            this.imgList = []; // 确保imgList为空数组
                        }
                    } else {
                        console.warn('无banner数据');
                        this.imgList = []; // 确保imgList为空数组
                    }
                } catch (error) {
                    console.error('Banner加载错误：', error);
                    uni.showToast({
                        title: this.$t('common.networkError'),
                        duration: 1500,
                        icon: 'none'
                    });
                } finally {
					this.isLoading = false;
				}
            },
			
			// 图片加载成功
			imageLoaded(index) {
				this.loadedImages.add(index);
				if (this.loadedImages.size === this.imgList.length) {
					// 所有图片加载完成
					this.autoplay = true; // 确保所有图片加载完才开始自动播放
				}
			},
			
			// 图片加载失败
			imageError(index) {
				console.error(`Banner图片 ${index} 加载失败`);
				// 可以在这里设置默认图片
				// this.imgList[index] = 'default-image-url';
			},

            // 商家列表
            getBrandList(){
                this.$tools.Get("api/product/list", {
                    language: uni.getStorageSync('lang') || 'CN'
                }).then((res) =>{
                    console.log('产品列表响应：', res); // 添加日志
                    if(res.status == 200){
                        if(res.data && res.data.length > 0) {
                            this.brand = res.data;
                            console.log('加载产品数据成功，数量：', this.brand.length); // 添加日志
                        } else {
                            if(this.langage !== 'CN') {
                                // 如果不是中文，尝试加载中文数据
                                this.$tools.Get("api/product/list", {
                                    language: 'CN'
                                }).then((cnRes) => {
                                    if(cnRes.status == 200 && cnRes.data) {
                                        this.brand = cnRes.data;
                                        console.log('加载中文产品数据成功'); // 添加日志
                                    }
                                });
                            }
                        }
                    } else {
                        uni.showToast({
                            title: this.$t('common.loadFailed'),
                            duration: 1500,
                            icon:'none'
                        });
                    }
                }).catch(err => {
                    console.error('产品列表加载错误：', err);
                    uni.showModal({
                        title: this.$t('common.tips'),
                        content: this.$t('common.retryLoad'),
                        success: (res) => {
                            if (res.confirm) {
                                this.getBrandList();
                            }
                        }
                    });
                });
            },

            // 去品牌内页
            toDetail(item,index){
                if (!item || !item.pid) {
                    console.error('无效的产品数据：', item);
                    return;
                }
                uni.navigateTo({
                    url: '/pages/business/businessList?id='+item.pid + '&images='+ encodeURIComponent(item.imgbg || '') + '&imagesList='+ encodeURIComponent(item.imglist || '') + '&pname=' + encodeURIComponent(item.pname || '')
                });
            },

            // 处理网络重试
            handleNetworkRetry() {
                // 重新加载数据
                this.getBannerList();
                this.getBrandList();
            }
        }
	}
</script>

<style lang="less" scoped>
@import url('../../static/css/index.less');
</style>
