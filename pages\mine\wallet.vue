<template>
    <view class="page">
        <view class="page-top">
            <view class="page-title">{{$t('new.walletTitle')}}</view>
            <view class="page-mine">
                <view class="page-mine-left">
                    <view class="page-mine-name">{{userInfo.uname}}</view>
                    <view class="page-mine-date">{{$t('new.walletTxt1')}}{{userInfo.updated_at}}</view>
                    <view class="page-mine-num">{{thousand(userInfo.price)}}</view>
                    <view class="page-mine-txt">{{$t('new.mineTit1')}}</view>
                </view>
                <image src="../../static/img/wallet4.png" class="page-mine-ball" />
            </view>
            <view class="page-box">
                <view class="page-box-btn" @click="remittance"><image src="../../static/img/wallet2.png" />{{$t("mine").dui}}</view>
                <view class="page-box-btn" @click="buyOrder"><image src="../../static/img/wallet3.png" />{{$t("mine").chong}}</view>
            </view>
        </view>
        <view class="menu" @click="toRecord('/pages/mine/cardManage')">{{$t('new.walletTxt2')}}<image src="../../static/img/wallet-arrow.png" /></view>
        <bottom :num='3'></bottom>
    </view>
</template>

<script>

    import bottom from "../home/<USER>";

    export default {

        data() {
            return {
                userInfo:{},
                bankCardList: []
            };
        },
        components: {
            bottom
        },
        onReady() {
            uni.setNavigationBarTitle({
                title: this.$t("new.userTitle")
            })
        },
        onShow() {
            this.getUserInfo()
        },
        methods:{
            remittance(){
                if(this.bankCardList.length>0){
                    uni.navigateTo({
                        url: '/pages/mine/remittance'
                    });
                }
                else{
                    uni.navigateTo({
                        url: '/pages/mine/bankList'
                    });
                }
            },
            buyOrder(){
                //判断
                uni.navigateTo({
                    url: '/pages/mine/recharge'
                });

            },
            thousand(num) {
                return  Math.floor(num).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
            },
            getUserInfo(){
                this.$tools.Post("api/user/info", {
                    api_token: uni.getStorageSync('token'),
                    language: uni.getStorageSync('lang')
                }).then(res=>{
                    if(res.status==200){
                        this.userInfo=res.data;
                        this.getBankCardList()
                    }
                });
            },
            getBankCardList(){
                this.$tools.Post("api/user/bank/get", {
                    uid: this.userInfo.uid,
                    api_token: uni.getStorageSync('token'),
                    language: uni.getStorageSync('lang')
                }).then((res) =>{
                    if(res.status == 200){
                        this.bankCardList = [...res.data]
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
            outLogin() {
                uni.showToast({
                    title: this.$t("mine").outClear,
                    duration: 1500,
                    icon:'none'
                });
                setTimeout(function(){
                    uni.reLaunch({
                        url: '/pages/public/login'
                    })
                },2000)
            },
            toRecord(value){
                uni.navigateTo({
                    url: value
                });
            },
            goUser(){
                uni.reLaunch({
                    url: '/pages/mine/user'
                })
            }
        }
    }
</script>

<style lang="less" scoped>
    page{
        background: rgba(245, 245, 245, 1);
        padding-bottom: 200rpx;
    }
    .page-top{
        height: 720rpx;
        background: #506589;
        border-radius: 0 0 50rpx 50rpx;
        .page-title{
            height: 174rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #FFFFFF;
        }
    }
    .page-mine{
        height: 340rpx;
        background: #172D52;
        border-radius: 50rpx;
        margin: 0 20rpx;
        display: flex;
        align-items: center;
        padding: 0 35rpx;
        justify-content: space-between;
        .page-mine-left{
            height: 340rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .page-mine-ball{
            width: 177rpx;
            height: 195rpx;
        }
        .page-mine-name{
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #FFFFFF;
        }
        .page-mine-date{
            height: 40rpx;
            background: #172D52;
            border-radius: 20rpx;
            margin-top: 15rpx;
            padding: 0 18rpx;
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .page-mine-num{
            font-size: 60rpx;
            font-family: Euclid Circular A;
            font-weight: 500;
            color: #FFFFFF;
            margin-top: 48rpx;
        }
        .page-mine-txt{
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #FFFFFF;
            margin-top: 10rpx;
        }
    }
    .page-box{
        height: 140rpx;
        margin-top: 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20rpx;
        .page-box-btn{
            width: 340rpx;
            height: 140rpx;
            background: #172D52;
            border-radius: 50rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 31rpx;
            box-sizing: border-box;
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #FFFFFF;
            image{
                width: 100rpx;
                height: 100rpx;
            }
        }
    }
    .menu{
        height: 180rpx;
        margin: 40rpx 30rpx 0 30rpx;
        background: url("../../static/img/wallet1.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20rpx 20rpx 0 20rpx;
        font-size: 36rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        image{
            width: 19rpx;
            height: 33rpx;
            margin-left: 126rpx;
        }
    }
</style>
