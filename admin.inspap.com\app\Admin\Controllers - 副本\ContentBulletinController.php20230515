<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ContentBulletin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;
use App\Http\Controllers\Tools;
use Illuminate\Support\Facades\Auth;
class ContentBulletinController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

      
        return Grid::make(new ContentBulletin(), function (Grid $grid) {

            Tools::viewroles($grid);  //视图权限筛选
            $grid->withBorder();
            $grid->column('title');
            $grid->column('body')->width(30);

            $grid->column('win_mods')->display(function () {
                if($this->status==1){
                    return '打开';
                }
              
                else{
                    return '关闭';
                }
                
            });
            $grid->column('created_at');
            $grid->enableDialogCreate(); //开启 弹窗新增

            $grid->actions(function (Grid\Displayers\Actions $actions) {
               // $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();

            });
        
   
            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('title','名称')->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ContentBulletin(), function (Show $show) {
            $show->field('id');
            $show->field('type');
            $show->field('win_mods');
            $show->field('title');
            $show->field('body');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ContentBulletin(), function (Form $form) {

            Admin::style(
                <<<CSS
                #layui-layer1 {
                    width: auto!important;
                }
                CSS            
            );
            $form->text('title')->required();
  
            $form->radio('type')->options([
                '1' => '公告列表', '2'=> '关于我们','3'=> '帮助'
            ])->default('m')->required();
            $form->switch('win_mods')->required();
            $form->editor('body')->required();
            if($form->isCreating()){
                $admin   = Auth::guard('admin')->user(); 
                $form->hidden('staff_id');
                $form->hidden('team_id');
                $form->staff_id = $admin->id;
                $form->team_id = $admin->team_id;

            }
        });
    }
}
