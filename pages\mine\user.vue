<template>
	<view class="page_index">
		<view class="page_top">
			{{$t("new2.txt1")}}
			<image src="../../static/img/head.png" />
		</view>
		<view class="page-box">
			<view class="page-list">
				<view class="page-list-left">{{$t("new.userName")}}</view>
				<view class="page-list-txt">{{userInfo.bname}}</view>
			</view>
			<view class="page-list">
				<view class="page-list-left">{{$t("new.userName2")}}</view>
				<view class="page-list-txt">{{userInfo.uname}}</view>
			</view>
			<view class="page-list" @click="goEdit(3)">
				<view class="page-list-left">{{$t("new.userName3")}}</view>
				<view class="page-list-txt">{{userInfo.birthday}}<image src="../../static/img/mine-arrow.png" /></view>
			</view>
			<view class="page-list" @click="goEdit(4)">
				<view class="page-list-left">{{$t("new.userName4")}}</view>
				<view class="page-list-txt">
					<span v-if="userInfo.sex==='男'">{{$t('new.sex1')}}</span>
					<span v-if="userInfo.sex==='女'">{{$t('new.sex2')}}</span>
					<span v-if="userInfo.sex==='不公布'">{{$t('new.sex3')}}</span>
					<image src="../../static/img/mine-arrow.png" />
				</view>
			</view>
			<view class="page-list" @click="goEdit(5)">
				<view class="page-list-left">{{$t("new.userName5")}}</view>
				<view class="page-list-txt">{{$t('new.nationality').split('、')[userInfo.nationality]}}<image src="../../static/img/mine-arrow.png" /></view>
			</view>
			<view class="page-list" @click="goEdit(6)">
				<view class="page-list-left">{{$t("new.userName6")}}</view>
				<view class="page-list-txt">{{userInfo.phone}}<image src="../../static/img/mine-arrow.png" /></view>
			</view>
			<view class="page-list" @click="goEdit(7)">
				<view class="page-list-left">{{$t("new.userName7")}}</view>
				<view class="page-list-txt">{{userInfo.email}}<image src="../../static/img/mine-arrow.png" /></view>
			</view>
			<view class="page-list" @click="goPass">
				<view class="page-list-left">{{$t("new.userName8")}}</view>
				<view class="page-list-txt"><image src="../../static/img/mine-arrow.png" /></view>
			</view>
		</view>
		<view class="page-out" @click="outLogin">{{$t("new.userOut")}}</view>
	</view>
</template>

<script>

	export default {

		data() {
			return {
				userInfo:{}
			};
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.$t("new.userTitle")
			})
		},
		onShow() {
				this.getUserInfo()
		},
		methods:{
			getUserInfo(){
				this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then(res=>{
					if(res.status==200){

						this.userInfo=res.data;
					}
				});
			},
			outLogin() {
				uni.showToast({
					title: this.$t("mine").outClear,
					duration: 1500,
					icon:'none'
				});
				setTimeout(function(){
					uni.reLaunch({
						url: '/pages/public/login'
					})
				},2000)
			},
			goEdit(type){
				uni.navigateTo({
					url: '/pages/mine/edit?type='+type
				})
			},
			goPass(){
				uni.navigateTo({
					url: '/pages/mine/modifyPassword'
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	page{
		background: rgba(245, 245, 245, 1);
		padding-bottom: 142rpx;
	}
	.page_top{
		height: 140rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		margin: 20rpx 20rpx 0 20rpx;
		padding: 0 34rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: space-between;
		image{
			width: 89rpx;
			height: 89rpx;
			border-radius: 50%;
		}
	}
	.page-box{
		background: #FFFFFF;
		border-radius: 20rpx;
		margin: 20rpx 20rpx 0 20rpx;
		padding: 0 20rpx;
	}
	.page-list{
		height: 90rpx;
		border-bottom: 1rpx solid rgba(224, 224, 224, 1);
		padding: 0 14rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		.page-list-left{
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #333333;
		}
		.page-list-txt{
			height: 90rpx;
			display: flex;
			align-items: center;
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #333333;
			image{
				width: 14rpx;
				height: 24rpx;
				margin-left: 12rpx;
			}
		}
	}
	.page-out{
		height: 100rpx;
		background: #65B11D;
		border-radius: 20rpx;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 54rpx 35rpx 0 35rpx;
	}
</style>
