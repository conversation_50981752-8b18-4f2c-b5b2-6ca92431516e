<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UsersVpi;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UsersVpiController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UsersVpi(), function (Grid $grid) {
            $grid->withBorder();
            $grid->column('id');
            $grid->column('lv','VIP等级');
            $grid->column('price','所需积分')->editable(true);
            $grid->enableDialogCreate(); //开启 弹窗新增
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UsersVpi(), function (Show $show) {
            $show->field('id');
            $show->field('lv');
            $show->field('price');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UsersVpi(), function (Form $form) {
            $form->display('id');
        
            $form->text('lv','VIP等级');
            $form->text('price','所需积分')->type('number');
        });
    }
}
