<?php

namespace App\Admin\Controllers;

use App\Admin\Metrics\Examples;
use App\Http\Controllers\Controller;
use Dcat\Admin\Http\Controllers\Dashboard;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Widgets\Card;
use App\Admin\Forms\Setting;
use App\Admin\Forms\IndexDateForm;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index(Content $content)
    {
        return $content
            ->header('平台首页')
            ->body(view('index'));
           
    }

    public function welecome(Content $content){
        return $content
        ->title('站点设置')
        ->body(new Card(new Setting()));
    }

    public function CountCenter(Content $content,$action){

        if($action=='staff'){
            return $content
            ->title('数据中心')
            ->description($action)
            ->body(view('CountCenter'));
        }
        if($action=='team'){
            return $content
            ->title('数据中心')
            ->description($action)
            ->body(view('CountCenterTeam'));
        }
    }

    public function test(Content $content){
        return $content
        ->title('测试')
        ->body(view('test'));
    }

}
