# 修复下注页面缓存问题

## 问题描述
用户报告下注页面出现"期号已更新，请重新选择"的提示，这是因为我们之前的性能优化将下注相关的API也加入了缓存，破坏了下注系统的实时性逻辑。

## 问题根源
下注系统需要跟随期号实时更新，但我们的缓存机制缓存了以下关键API：
- `api/product/issue` - 期号查询
- `api/product/open` - 开奖历史  
- `api/user/ordering` - 下注操作
- `api/product/list` - 产品列表
- `api/user/info` - 用户信息（余额等）

## 修复方案

### 1. 更新缓存排除列表
在 `static/js/utils/Tools.js` 中更新了两处缓存逻辑：

**第一处（第305-321行）**：
```javascript
const noCacheUrls = [
  'api/user/login', 
  'api/user/register', 
  'api/user/logout',
  // 下注相关API - 需要实时数据，不能缓存
  'api/product/issue',     // 期号查询 - 跟随期号实时更新
  'api/product/open',      // 开奖历史 - 实时开奖结果
  'api/user/ordering',     // 下注操作 - 实时下注和查询
  'api/product/list',      // 产品列表 - 可能包含实时状态
  // 用户实时数据
  'api/user/info',         // 用户信息 - 余额等实时数据
  'api/user/bill',         // 账单记录 - 实时交易记录
  'api/user/withdraw'      // 提现操作 - 实时操作
];
```

**第二处（第371-388行）**：
同样更新了成功响应时的缓存逻辑。

### 2. 添加下注缓存清理方法
在 `Tools.js` 中添加了 `clearBettingCache()` 方法：

```javascript
clearBettingCache() {
  const bettingUrls = [
    'api/product/issue',     // 期号查询
    'api/product/open',      // 开奖历史
    'api/user/ordering',     // 下注操作
    'api/product/list',      // 产品列表
    'api/user/info',         // 用户信息
    'api/user/bill'          // 账单记录
  ];
  // 清除所有下注相关的缓存
}
```

### 3. 应用启动时清理缓存
在 `main.js` 中添加了启动时清理：

```javascript
// 清除下注相关缓存，确保期号等实时数据不被缓存
if(typeof uni !== 'undefined') {
  Tools.clearBettingCache()
}
```

## 修复效果

### ✅ 解决的问题
- 期号查询 (`api/product/issue`) 不再被缓存，确保实时获取最新期号
- 开奖历史 (`api/product/open`) 不再被缓存，确保实时开奖结果
- 下注操作 (`api/user/ordering`) 不再被缓存，确保实时下注状态
- 用户余额等信息 (`api/user/info`) 不再被缓存，确保实时余额显示

### ✅ 保留的优化
- 静态内容（如配置信息）仍然被缓存
- 非实时性要求的API仍然享受缓存性能优化
- 缓存机制本身保持完整，只是排除了实时性要求高的API

## 测试建议

1. **下注流程测试**：
   - 进入下注页面
   - 选择下注项
   - 提交下注
   - 验证不再出现"期号已更新，请重新选择"提示

2. **期号更新测试**：
   - 等待期号自然更新
   - 刷新下注页面
   - 验证显示最新期号

3. **余额实时性测试**：
   - 进行下注操作
   - 检查余额是否实时更新
   - 验证不显示缓存的旧余额

## 注意事项

- 此修复确保了下注系统的实时性，但可能会略微增加这些API的请求频率
- 用户体验将得到显著改善，不再出现期号过期的错误提示
- 系统稳定性得到保障，下注逻辑恢复正常工作
