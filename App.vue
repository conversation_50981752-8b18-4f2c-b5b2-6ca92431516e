<script>
	import routerIntercept from './static/js/utils/routerIntercept.js'

	export default {
		onLaunch: function() {
            // 初始化语言设置
            this.initLanguage();
            
            // #ifdef APP-PLUS
            // token标志来判断
            let token = uni.getStorageSync('token');
            if (token) {
                //存在则关闭启动页进入首页
                plus.navigator.closeSplashscreen();
            } else {
                //不存在则跳转至登录页
                uni.reLaunch({
                    url: "/pages/public/login",
                    success: () => {
                        plus.navigator.closeSplashscreen();
                    }
                })
            }
            // #endif
            
            // 添加全局路由拦截
            uni.addInterceptor('navigateTo', {
                invoke(e) {
                    return routerIntercept.beforeEach(e.url)
                }
            })
            uni.addInterceptor('redirectTo', {
                invoke(e) {
                    return routerIntercept.beforeEach(e.url)
                }
            })
            uni.addInterceptor('reLaunch', {
                invoke(e) {
                    return routerIntercept.beforeEach(e.url)
                }
            })
            uni.addInterceptor('switchTab', {
                invoke(e) {
                    return routerIntercept.beforeEach(e.url)
                }
            })
		},
		onShow: function() {
			this.globalRefresh();
            
            // 检查token是否过期
            if (routerIntercept.checkTokenExpired()) {
                routerIntercept.handleTokenExpired();
            }
		},
		onHide: function() {
		},
        methods: {
            initLanguage() {
                const systemLang = uni.getSystemInfoSync().language.toLowerCase();
                let defaultLang = 'EN'; // 默认英语

                // 根据系统语言设置默认语言
                if (systemLang.includes('zh')) {
                    defaultLang = systemLang.includes('tw') ? 'TW' : 'CN';
                } else if (systemLang.includes('es')) {
                    defaultLang = 'ES';
                } else if (systemLang.includes('pt')) {
                    defaultLang = 'PT';
                } else if (systemLang.includes('id')) {
                    defaultLang = 'IDN';
                } else if (systemLang.includes('vi')) {
                    defaultLang = 'VN';
                } else if (systemLang.includes('de')) {
                    defaultLang = 'DE';
                }

                // 如果没有存储的语言设置，使用默认语言
                const storedLang = uni.getStorageSync('lang');
                if (!storedLang) {
                    uni.setStorageSync('lang', defaultLang);
                    this.$i18n.locale = defaultLang;
                }
            }
        }
	}
</script>

<style lang="scss">
	.flexCenter{
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
	}
	uni-checkbox .uni-checkbox-input {
	    width: 32rpx !important;
	    height: 32rpx !important;
	    border-color: #FFB296 !important;
	    border-radius: 4px !important;
	    color: #ffffff !important;
	  }

	  uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
	    //border: none !important;
	    background: #FFB296;
	    border-color: #FFB296;
	  }

	  uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked::before {
	    width: 20rpx;
	    height: 20rpx;
	    line-height: 20rpx;
	    text-align: center;
	    font-size: 18rpx;
	    color: #fff;
	    background: transparent;
	    //transform: translate(-70%, -50%) scale(1);
	    //-webkit-transform: translate(-70%, -50%) scale(1);
	  }
	.uni-tabbar{
		height: 125upx;
	}
	.uni-tabbar__icon{
		width: 76upx!important;
		height: 76upx!important;
		margin-top: -10upx!important;
	}
	.uni-tabbar .uni-tabbar__label{
		margin-top: 0!important;
	}
	.uni-tabbar__item:nth-child(4) {
		margin-top: -30upx;
	}
	.uni-tabbar__item:nth-child(4) .uni-tabbar__icon{
		width: 90upx!important;
		height: 90upx!important;
	}
	.uni-tabbar__item:nth-child(4) .uni-tabbar__label{
		margin-top: 16upx!important;
	}
    /* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import "uview-ui/index.scss";
	/*每个页面公共css */
	/* 引入官方css库 */
	@import './static/css/uni.css';
	/* 引入自定义图标库 */
	@import './static/css/icon.css';
	/* 引入动画库 */
	@import './static/css/animate.css';
	/* 公共样式 */
	@import './static/css/common.css';
    .status_bar {
		height: var(--status-bar-height);
		width: 100%;
	}
	.message-layer{
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 999;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background-color: rgba(0,0,0,0.6);
			.content-box{
				width: 608rpx;
				position: relative;
				image{
					width: 100%;
				}
				.content{
					text-align: left;
					padding:30rpx 30rpx 0;
					height:250rpx;
					overflow-y: auto;
				}
				.box{
					position: absolute;
					top: 330rpx;
					left:50%;
					transform: translateX(-50%);
					width: 100%;
					text-align: center;
					.title{
						color:#172D52;
						font-weight:bold;
					}
					.btns{
						display: flex;
						padding:40rpx 50rpx 0;
						view,navigator{
							flex:1;
							height: 80rpx;
							line-height: 80rpx;
							border-radius: 200rem;
							border:2rpx solid #E0E0E0;
						}
						.look{
							margin-left: 33rpx;
							border-color:#172D52;
							background-color:#172D52;
							color:#fff;
							box-shadow: 0px 10px 10px 0px rgba(0, 0, 0, 0.25);

						}
					}
				}
				.close{
					text-align: center;
					padding-top: 20rpx;
					image{
						width: 60rpx;
						height: 60rpx;
					}
				}
			}
	}

</style>
