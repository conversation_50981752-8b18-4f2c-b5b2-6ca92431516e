{"name": "@intlify/shared", "version": "9.2.2", "description": "@intlify/shared", "keywords": ["i18n", "internationalization", "intlify", "utitlity"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/shared#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/shared"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "index.mjs", "dist"], "main": "index.js", "module": "dist/shared.esm-bundler.js", "types": "dist/shared.d.ts", "engines": {"node": ">= 14"}, "buildOptions": {"name": "IntlifyShared", "formats": ["esm-bundler", "cjs"]}, "exports": {".": {"import": {"node": "./index.mjs", "default": "./dist/shared.esm-bundler.js"}, "require": "./index.js"}, "./dist/*": "./dist/*", "./index.mjs": "./index.mjs", "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "sideEffects": false}