(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-public-register"],{"0c46":function(t,n,o){var e=o("c86c"),i=o("2ec5"),a=o("dcd8");n=e(!1);var s=i(a);n.push([t.i,"uni-page-body .loginWrap[data-v-465178b8]{background:url("+s+") no-repeat top;background-size:100% 100%;min-height:100vh}uni-page-body .loginWrap .loginWrap_cont_title[data-v-465178b8]{height:%?360?%;display:flex;align-items:center;justify-content:center}uni-page-body .loginWrap .loginWrap_cont_title uni-image[data-v-465178b8]{width:%?236?%;height:%?236?%}uni-page-body .loginWrap .cont_textCon_item[data-v-465178b8]{display:flex;margin:%?30?% %?55?% 0 %?55?%;flex-direction:column;justify-content:center;font-size:%?32?%;font-family:PingFang SC;font-weight:500;color:#fff}uni-page-body .loginWrap .cont_textCon_item .inputStyle[data-v-465178b8]{margin-top:%?11?%;height:%?88?%;background:#fff;border-radius:%?20?%;padding:0 %?19?%;border:0;font-size:%?32?%;font-family:PingFang SC;font-weight:500;color:#333}uni-page-body .loginWrap .cont_textCon_item .inpuyh[data-v-465178b8]{color:#b3b3b3}uni-page-body .loginWrap .loginWrap_cont_tip[data-v-465178b8]{padding:%?63?% %?65?% 0 %?65?%;font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#fff}uni-page-body .loginWrap .loginWrap_cont_tip span[data-v-465178b8]{color:#96e727}uni-page-body .loginWrap .cont_textCon_but[data-v-465178b8]{height:%?88?%;background:#65b11d;border-radius:%?20?%;font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#fff;display:flex;align-items:center;justify-content:center;margin:%?60?% %?55?% 0 %?55?%}uni-page-body[data-v-465178b8] :-moz-placeholder{\n  /* Mozilla Firefox 4 to 18 */color:#b3b3b3}uni-page-body[data-v-465178b8] ::-moz-placeholder{\n  /* Mozilla Firefox 19+ */color:#b3b3b3}uni-page-body uni-input[data-v-465178b8]:-ms-input-placeholder{color:#b3b3b3}uni-page-body uni-input[data-v-465178b8]::-webkit-input-placeholder{color:#b3b3b3}",""]),t.exports=n},"1d82":function(t,n,o){"use strict";o("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,o("23f4"),o("7d2f"),o("5c47"),o("9c4e"),o("ab80"),o("0506");var e={data:function(){return{formData:{username:"",password:"",new_password:"",staffId:"",bname:""}}},onLoad:function(){uni.setNavigationBarTitle({title:this.$t("regis.title")})},methods:{goLogin:function(){uni.navigateTo({url:"/pages/public/login"})},submit:function(){var t=this;if(this.formData.username.length<4)return uni.showToast({title:this.$t("regis.tip1"),icon:"none"}),!1;var n=new RegExp(/[^\w\.\/]/gi);if(n.test(this.formData.username))return uni.showToast({title:this.$t("regis.tip2"),icon:"none"}),!1;if(0==this.formData.password.length)return uni.showToast({title:this.$t("regis.txt4"),icon:"none"}),!1;if(0==this.formData.new_password.length)return uni.showToast({title:this.$t("regis.txt5"),icon:"none"}),!1;if(this.formData.password!=this.formData.new_password)return uni.showToast({title:this.$t("regis.tip3"),icon:"none"}),!1;if(0==this.formData.staffId.length)return uni.showToast({title:this.$t("regis.txt1"),icon:"none"}),!1;var o={uname:this.formData.username,password:this.formData.password,code:this.formData.staffId,phone:"",language:uni.getStorageSync("lang")},e=this;uni.showLoading({title:this.$t("regis.registering")||"注册中...",mask:!0}),this.$tools.Post("api/user/register",o).then((function(t){200==t.status?(uni.showToast({title:t.msg,duration:1e3,icon:"none"}),setTimeout((function(){e.$tools.Get("api/user/login",{uname:e.formData.username,password:e.formData.password,language:uni.getStorageSync("lang")}).then((function(t){uni.hideLoading(),200===t.status?(uni.setStorageSync("loginInfo",{uname:e.formData.username,password:e.formData.password}),uni.setStorageSync("less",!0),uni.setStorage({key:"token",data:t.data,success:function(){uni.reLaunch({url:"../home/<USER>"})}})):uni.showToast({title:t.msg,duration:1500,icon:"none"})})).catch((function(){uni.hideLoading(),uni.showToast({title:e.$t("regis.networkError")||"网络错误，请重试",duration:2e3,icon:"none"})}))}),500)):(uni.hideLoading(),uni.showToast({title:t.msg,duration:1500,icon:"none"}))})).catch((function(){uni.hideLoading(),uni.showToast({title:t.$t("regis.networkError")||"网络错误，请重试",duration:2e3,icon:"none"})}))},logoLoaded:function(){console.log("Logo loaded successfully")},logoError:function(){console.log("Logo failed to load")}}};n.default=e},"268f":function(t,n,o){"use strict";var e=o("d3d3"),i=o.n(e);i.a},"2ec5":function(t,n,o){"use strict";t.exports=function(t,n){return n||(n={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),n.hash&&(t+=n.hash),/["'() \t\n]/.test(t)||n.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},"4d38":function(t,n,o){"use strict";o.r(n);var e=o("d7d6"),i=o("6f09");for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(n,t,(function(){return i[t]}))}(a);o("268f");var s=o("828b"),r=Object(s["a"])(i["default"],e["b"],e["c"],!1,null,"465178b8",null,!1,e["a"],void 0);n["default"]=r.exports},"6f09":function(t,n,o){"use strict";o.r(n);var e=o("1d82"),i=o.n(e);for(var a in e)["default"].indexOf(a)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(a);n["default"]=i.a},7429:function(t,n,o){t.exports=o.p+"static/img/logo.png"},d3d3:function(t,n,o){var e=o("0c46");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var i=o("967d").default;i("0e3c2dbf",e,!0,{sourceMap:!1,shadowMode:!1})},d7d6:function(t,n,o){"use strict";o.d(n,"b",(function(){return e})),o.d(n,"c",(function(){return i})),o.d(n,"a",(function(){}));var e=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{staticClass:"loginWrap"},[e("v-uni-view",{staticClass:"loginWrap_cont_title"},[e("v-uni-image",{attrs:{src:o("7429"),mode:"aspectFit","lazy-load":!1},on:{load:function(n){arguments[0]=n=t.$handleEvent(n),t.logoLoaded.apply(void 0,arguments)},error:function(n){arguments[0]=n=t.$handleEvent(n),t.logoError.apply(void 0,arguments)}}})],1),e("v-uni-view",{staticClass:"cont_textCon_item"},[t._v(t._s(t.$t("regis.tit2"))),e("v-uni-input",{staticClass:"inputStyle",attrs:{type:"text","placeholder-class":"inpuyh",placeholder:t.$t("regis.txt2")},model:{value:t.formData.username,callback:function(n){t.$set(t.formData,"username",n)},expression:"formData.username"}})],1),e("v-uni-view",{staticClass:"cont_textCon_item"},[t._v(t._s(t.$t("regis.tit4"))),e("v-uni-input",{staticClass:"inputStyle",attrs:{type:"password","placeholder-class":"inpuyh",placeholder:t.$t("regis.txt4")},model:{value:t.formData.password,callback:function(n){t.$set(t.formData,"password",n)},expression:"formData.password"}})],1),e("v-uni-view",{staticClass:"cont_textCon_item"},[t._v(t._s(t.$t("regis.tit4-1"))),e("v-uni-input",{staticClass:"inputStyle",attrs:{type:"password","placeholder-class":"inpuyh",placeholder:t.$t("regis.txt5")},model:{value:t.formData.new_password,callback:function(n){t.$set(t.formData,"new_password",n)},expression:"formData.new_password"}})],1),e("v-uni-view",{staticClass:"cont_textCon_item"},[t._v(t._s(t.$t("regis.tit1"))),e("v-uni-input",{staticClass:"inputStyle",attrs:{type:"number",maxlength:"6","placeholder-class":"inpuyh",placeholder:t.$t("regis.txt1")},model:{value:t.formData.staffId,callback:function(n){t.$set(t.formData,"staffId",n)},expression:"formData.staffId"}})],1),e("v-uni-view",{staticClass:"cont_textCon_but",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.submit()}}},[t._v(t._s(t.$t("regis.btn")))]),e("v-uni-view",{staticClass:"loginWrap_cont_tip",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goLogin()}}},[t._v(t._s(t.$t("regis.txt6"))),e("span",[t._v(t._s(t.$t("regis.txt7")))])])],1)},i=[]},dcd8:function(t,n,o){t.exports=o.p+"static/img/login.jpg"}}]);