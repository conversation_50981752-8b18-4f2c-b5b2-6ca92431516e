{"private": true, "scripts": {"dev": "webpack --mode development --watch", "build": "NODE_ENV=production webpack --mode production", "format": "prettier --write 'resources/**/*.{css,js,ts,vue}'"}, "dependencies": {"git-url-parse": "^11.1.2", "highlight.js": "^10.4.1", "lodash": "^4.17.21", "markdown-it": "^12.3.2", "md5": "^2.2.1", "sql-formatter": "^2.3.3"}, "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/preset-env": "^7.4.5", "@babel/preset-typescript": "^7.3.3", "@fullhuman/postcss-purgecss": "^1.1.0", "@types/jest": "^24.0.15", "@types/lodash": "^4.14.133", "babel-loader": "^8.0.6", "css-loader": "^3.0.0", "husky": "^1.3.1", "jest": "^24.8.0", "lint-staged": "^8.1.5", "postcss-import": "^12.0.1", "postcss-loader": "^3.0.0", "postcss-preset-env": "^6.6.0", "prettier": "^1.16.4", "style-loader": "^0.23.1", "tailwindcss": "^1.0.4", "typescript": "^3.5.2", "vue": "^2.6.10", "vue-loader": "^15.7.0", "vue-template-compiler": "^2.6.10", "webpack": "^4.35.0", "webpack-cli": "^3.3.5"}, "husky": {"hooks": {"pre-commit": "lint-staged && yarn build && git add resources/compiled/ignition.js"}}, "lint-staged": {"linters": {"*.{css,js,ts,vue}": ["yarn format", "git add"]}, "ignore": ["resources/compiled/**/*"]}, "jest": {"testPathIgnorePatterns": ["/node_modules/", "/__helpers__/"]}}