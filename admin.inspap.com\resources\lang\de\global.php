<?php

return [
    'fields' => [
        'id'                    => 'ID',
        'name'                  => 'Name',
        'username'              => 'Benutzername',
        'email'                 => 'E-Mail',
        'http_path'             => 'HTTP-Pfad',
        'password'              => 'Passwort',
        'password_confirmation' => 'Passwort bestätigen',
        'created_at'            => 'Erstellt am',
        'updated_at'            => 'Aktualisiert am',
        'permissions'           => 'Berechtigungen',
        'slug'                  => 'Slug',
        'user'                  => 'Benutzer',
        'order'                 => 'Reihenfolge',
        'ip'                    => 'IP',
        'method'                => 'Methode',
        'uri'                   => 'URI',
        'roles'                 => 'Rollen',
        'path'                  => 'Pfad',
        'input'                 => 'Eingabe',
        'type'                  => 'Typ',
    ],
    'labels' => [
        'list'     => 'Liste',
        'edit'     => 'Bearbeiten',
        'detail'   => 'Details',
        'create'   => 'Erstellen',
        'root'     => 'Hauptebene',
        'scaffold' => 'Code-Generator',
    ],
    'options' => [
        //
    ],
];
