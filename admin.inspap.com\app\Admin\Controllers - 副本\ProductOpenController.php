<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ProductOpen;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Http\Controllers\Tools;
use App\Models\Product;

class ProductOpenController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ProductOpen(), function (Grid $grid) {

            // 启用表格异步渲染功能
            $grid->async();
            $grid->model()->orderBy('open_at','DESC');
            $grid->column('id')->sortable();
            $grid->column('issue');
            $grid->column('pname');
          
            $grid->column('dx','货品')->display(function () {
                $productType = Tools::productType();
                
                return $productType[$this->dx];
                
            });
            $grid->column('sd','单双')->display(function () {
                $productType = Tools::productType();
                
                return $productType[$this->sd];
                
            });
            $grid->column('status')->display(function () {

                return '已开奖';
            });
            $grid->column('source');
            $grid->column('open_at')->display(function () {

                $times = '';
                if(is_numeric($this->open_at)){
                   $times = date('Y-m-d H:i:s',$this->open_at);
                }
                return $times ;
                
            });

            $grid->enableDialogCreate(); //开启 弹窗新增
            $grid->actions(function (Grid\Displayers\Actions $actions) {

                $actions->disableEdit();  
                $actions->disableDelete();
                $actions->disableView();

            });

        
            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('issue','期号')->width(3);

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ProductOpen(), function (Show $show) {
            $show->field('id');
            $show->field('issue');
            $show->field('pname');
            $show->field('class');
            $show->field('dx');
            $show->field('sd');
            $show->field('status');
            $show->field('source');
            $show->field('open_at');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ProductOpen(), function (Form $form) {

            $productType = Tools::productType();
            if($form->isCreating()){  //新增
               
                $form->hidden('open_at')->value(time());
                $products = Product::get();

                //组合下拉框选项
                $option = [];
                for($i=0;$i<count($products);$i++){
                   $option[$products[$i]->pid]= $products[$i]->pname;
                };


                $form->select('pname')->options($option);  //$option 形式 ['pid'=>'pname','pid'=>'pname']
                $form->hidden('pid');
               
                $form->saving(function (Form $form) {

                    $productse = Product::get();

                    $optione = [];
                    for($i=0;$i<count($productse);$i++){
                       $optione[$productse[$i]->pid]= $productse[$i]->pname;
                    };

                    $form->pid   = $form->pname;  //获取选中的VULE 就是PID
                    $form->pname = $optione[$form->pname];  //通过PID获取到Pname
                });



                $form->text('issue','期号');

                $form->radio('dx')->options([
                    '1' => $productType[1], 
                    '2' => $productType[2], 
                  
                    
                ])->default($form->dx);

                $form->radio('sd')->options([
                    '3' => $productType[3], 
                    '4' => $productType[4], 
                  
                    
                ])->default($form->sd);

            }
   
        });
    }
}
