<template>
  <view class="network-status" v-if="!isConnected">
    <view class="network-status__content">
      <view class="network-status__icon">
        <u-icon name="wifi-off" size="80" color="#999"></u-icon>
      </view>
      <text class="network-status__text">{{ message }}</text>
      <view class="network-status__actions">
        <button class="network-status__retry" @tap="handleRetry">
          <u-icon name="reload" size="14" color="#fff"></u-icon>
          <text>重试</text>
        </button>
        <button class="network-status__settings" @tap="openSettings">
          <u-icon name="setting" size="14" color="#666"></u-icon>
          <text>网络设置</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'network-status',
  data() {
    return {
      isConnected: true,
      message: '网络连接失败，请检查网络设置'
    }
  },
  created() {
    // 初始检查网络状态
    this.checkNetworkStatus();
    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      this.isConnected = res.isConnected;
    });
  },
  methods: {
    checkNetworkStatus() {
      uni.getNetworkType({
        success: (res) => {
          this.isConnected = res.networkType !== 'none';
        }
      });
    },
    handleRetry() {
      this.checkNetworkStatus();
      this.$emit('retry');
    },
    openSettings() {
      // #ifdef APP-PLUS
      if (uni.getSystemInfoSync().platform === 'ios') {
        plus.runtime.openURL('app-settings:');
      } else {
        var Intent = plus.android.importClass("android.content.Intent");
        var Settings = plus.android.importClass("android.provider.Settings");
        var mainActivity = plus.android.runtimeMainActivity();
        var intent = new Intent(Settings.ACTION_WIRELESS_SETTINGS);
        mainActivity.startActivity(intent);
      }
      // #endif
      
      // #ifdef H5
      this.$emit('settings');
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.network-status {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &__content {
    text-align: center;
    padding: 30rpx;
  }
  
  &__icon {
    margin-bottom: 30rpx;
  }
  
  &__text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 40rpx;
  }
  
  &__actions {
    display: flex;
    justify-content: center;
    gap: 20rpx;
  }
  
  &__retry,
  &__settings {
    min-width: 180rpx;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 28rpx;
    border-radius: 35rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      margin-left: 4rpx;
    }
  }
  
  &__retry {
    background-color: #2979ff;
    color: #fff;
  }
  
  &__settings {
    background-color: #f5f5f5;
    color: #666;
  }
}
</style> 