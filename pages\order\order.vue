<template>
	<view class="container">
        <!-- 自定义导航栏 -->
        <view class="nav_bar">
            <view class="status_bar"> </view>
            <view class="nav_wrap">
                <view class="title">{{i18n.order}}</view>
            </view>
        </view>

		<view class="tabs">
			<view :class="flag==index?'current':''" v-for="(item,index) in tabs" :key='index' @click="tabFun(index, item.val)">
				{{item.name}}
			</view>
			<view class="bottombg"></view>
		</view>

		<view class="list_wrap">
			<!-- 修改骨架屏显示逻辑 -->
			<order-skeleton v-if="showSkeleton && list.length === 0" />
			
			<!-- 修改列表显示逻辑 -->
			<view v-show="!(showSkeleton && list.length === 0)">
			<!-- 订单列表 -->
			<template v-if="flag2===''">
				<view v-for="(item,index) in list" :key="item.oid + '_' + index" class="list">
					<view class="listTop">
						<text class="listTop-name">{{item.pname}}</text>
						<text class="listTop-date">{{i18n.di}}{{item.issue}}{{i18n.qi}}</text>
					</view>
					<view class="item">
						<view class="item-left">
							<view class="item-order">{{item.oid}}</view>
							<view class="item-txt">
								<text v-if="parseInt(item.pair_data)===1">{{$t("business").you}}</text>
								<text v-if="parseInt(item.pair_data)===2">{{$t("business").tong}}</text>
								<text v-if="parseInt(item.pair_data)===3">{{$t("business").dan}}</text>
								<text v-if="parseInt(item.pair_data)===4">{{$t("business").shuang}}</text>
								<span></span>
								<text>{{i18n.pipei}}{{thousand(Math.floor(Number(item.quantity_price)))}}{{i18n.jifen}}</text>
							</view>
						</view>
						<view v-if="item.status == 2" class="item-on">{{i18n.success}}<image class="item-img" src="../../static/img/order3.png" /></view>
						<view v-if="item.status == 1" class="item-in">{{i18n.padding}}<image class="item-img" src="../../static/img/order2.png" /></view>
						<view v-if="item.status == -1" class="item-not">{{i18n.error}}<image class="item-img" src="../../static/img/order1.png" /></view>
					</view>
				</view>
			</template>
			<template v-if="flag2===-1">
				<view v-for="(item,index) in list" :key="item.oid + '_' + index" class="list" v-show="item.status == -1">
					<view class="right">
						<view class="item">
							<text class="name">{{item.pname}}</text>
							<text class="data">{{i18n.di}}{{item.issue}}{{i18n.qi}}</text>
						</view>
						<view class="item" style="padding: 0;">
							<text class="match1">{{item.oid}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===1">{{$t("business").you}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===2">{{$t("business").tong}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===3">{{$t("business").dan}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===4">{{$t("business").shuang}}</text>
						</view>
						<view class="item">
							<text class="match">{{i18n.pipei}}{{thousand(Math.floor(Number(item.quantity_price)))}}{{i18n.jifen}}</text>
							<text v-if="item.status == 2" class="cg">{{i18n.success}}</text>
							<text v-if="item.status == 1" class="dd">{{i18n.padding}}</text>
							<text v-if="item.status == -1" class="sb">{{i18n.error}}</text>
						</view>
					</view>
				</view>
			</template>
			<template v-if="flag2===1">
				<view v-for="(item,index) in list" :key="item.oid + '_' + index" class="list" v-show="item.status == 1">
					<view class="right">
						<view class="item">
							<text class="name">{{item.pname}}</text>
							<text class="data">{{i18n.di}}{{item.issue}}{{i18n.qi}}</text>
						</view>
						<view class="item" style="padding: 0;">
							<text class="match1">{{item.oid}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===1">{{$t("business").you}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===2">{{$t("business").tong}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===3">{{$t("business").dan}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===4">{{$t("business").shuang}}</text>
						</view>
						<view class="item">
							<text class="match">{{i18n.pipei}}{{thousand(Math.floor(Number(item.quantity_price)))}}{{i18n.jifen}}</text>
							<text v-if="item.status == 2" class="cg">{{i18n.success}}</text>
							<text v-if="item.status == 1" class="dd">{{i18n.padding}}</text>
							<text v-if="item.status == -1" class="sb">{{i18n.error}}</text>
						</view>
					</view>
				</view>
			</template>
			<template v-if="flag2===2">
				<view v-for="(item,index) in list" :key="item.oid + '_' + index" class="list" v-show="item.status == 2">
					<view class="right">
						<view class="item">
							<text class="name">{{item.pname}}</text>
							<text class="data">{{i18n.di}}{{item.issue}}{{i18n.qi}}</text>
						</view>
						<view class="item" style="padding: 0;">
							<text class="match1">{{item.oid}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===1">{{$t("business").you}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===2">{{$t("business").tong}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===3">{{$t("business").dan}}</text>
							<text class="money" v-if="parseInt(item.pair_data)===4">{{$t("business").shuang}}</text>
						</view>
						<view class="item">
							<text class="match">{{i18n.pipei}}{{thousand(Math.floor(Number(item.quantity_price)))}}{{i18n.jifen}}</text>
							<text v-if="item.status == 2" class="cg">{{i18n.success}}</text>
							<text v-if="item.status == 1" class="dd">{{i18n.padding}}</text>
							<text v-if="item.status == -1" class="sb">{{i18n.error}}</text>
						</view>
					</view>
				</view>
			</template>
			
			<!-- 空状态 -->
			<view v-if="list.length === 0 && !isLoading && !showSkeleton" class="empty-state">
				<text>{{i18n.noOrders}}</text>
			</view>

			</view>
		</view>
		<view class="example-body">
		    <uni-load-more :status="status" :content-text="contentText"/>
		</view>

		<bottom :num='2'></bottom>
	</view>
</template>

<script>
	import uniLoadMore from "@/components/uni-load-more/uni-load-more.vue"
	import bottom from '../home/<USER>'
	import OrderSkeleton from '@/components/order-skeleton/order-skeleton.vue'
	// import {format} from "@/static/js/utils/filters.js" // format函数在当前代码中未被使用
	
	// 支持的语言列表
	const SUPPORTED_LANGUAGES = ['CN', 'EN', 'ES', 'PT', 'IDN', 'VN'];
	const DEFAULT_LANGUAGE = 'EN';
	
	export default {
		name:'orderRecords',
		components: {
		    uniLoadMore,
			bottom,
			OrderSkeleton
		},
		data() {
			return {
				contentText: {
				    contentdown: '',
				    contentrefresh: '',
				    contentnomore: ''
				},
				flag:0,
				flag2:'', // 初始状态为空字符串，对应"全部"
                timeVal: 0,
				tabs:[],
                isWin: '',
                timeCreatedStart: '',
                timeCreatedEnd: '',
                pageIndex: 1,
                pageSize: 10, // 每页获取10条数据
                hasMore: false,
                status: 'more', // uni-load-more 状态
				list:[],
				pickerName:"",
                timeArray:[],
                userInfo: null,
				showSkeleton: true, // 初始显示骨架屏
				// showContent: false, // 此变量似乎未使用
				// contentReady: false, // 此变量似乎未使用
				isLoading: false, // 是否正在加载数据
				// cachedOrders: new Map(), // 此变量似乎未使用
				// refreshing: false, // 此变量似乎未使用，使用 isRefreshing
				currentLanguage: '',
				// enablePullDownRefresh: false, // 此变量似乎未使用
				// refreshTrigger: 0, // 此变量似乎未使用
				// touchStartY: 0, // 此变量似乎未使用
				isRefreshing: false, // 是否正在下拉刷新
				// loadingChunks: false, // 分块加载相关，当前未使用
				// chunkSize: 5, // 分块加载相关，当前未使用
				// renderedItems: [], // 分块加载相关，当前未使用
				// observer: null, // IntersectionObserver 相关，当前未使用
				retryCount: 0,
				maxRetries: 3,
				retryDelay: 1000,
				silentLoading: false,
				networkStatus: true,
				// isFirstLoad: true, // 此变量似乎未使用
				scrollTop: 0,
				showTopBtn: false,
				bottomHeight: 160,
			}
		},
		computed: {
		    i18n () {
		       return this.$t("order")
		    },
			more() {
				return this.$t("contentText")
			}
		},
		onShow() {
			this.checkNetworkStatus(); // 检查网络状态
			this.detectAndSetLanguage(); // 检测并设置语言
			this.initializeUI(); // 初始化UI相关的文本
            
            // 如果list为空，或者强制刷新标识存在，则加载数据
            // 避免每次onShow都无条件刷新，除非是从其他页面提交订单后返回需要强制刷新
            if (this.list.length === 0 || (this.options && this.options.forceRefresh)) {
                this.initData(true); 
                if (this.options) delete this.options.forceRefresh; // 清除强制刷新标记
            } else {
                // 非首次加载或非强制刷新时，可以考虑仅更新用户信息（如果需要）
                // 但通常订单列表不需要频繁更新，除非有明确指示
            }
		},
		
		onLoad(options) {
            this.options = options; // 保存options，以便onShow中使用
			if(options.flag2){ // 处理从其他页面跳转并指定初始tab的情况
				this.flag2 = options.flag2
				if(this.flag2==2){ // '已完成'
					this.flag = 1 // 对应tabs数组中的索引
				} else if (this.flag2 == -1) { // '失败'
                    this.flag = 2;
                } else if (this.flag2 == 1) { // '进行中'
                    this.flag = 3;
                } else { // '全部'或其他
                    this.flag = 0;
                }
			}
            // this.queryByInput(); // queryByInput 已合并到 initData
			// 获取底部导航栏高度
			const systemInfo = uni.getSystemInfoSync();
			const safeAreaInsets = systemInfo.safeAreaInsets || { bottom: 0 };
			// 160rpx 是 <bottom> 组件的高度，需要加上安全区域的底部间距
			// 注意 rpx 和 px 的转换，这里假设 bottomHeight 单位是 rpx
			// 如果 <bottom> 组件高度固定为 80px (uni.upx2px(160)), 则 this.bottomHeight = uni.upx2px(160) + safeAreaInsets.bottom;
			// 但CSS中直接写160rpx，这里也用rpx单位，uni-app会自动处理
			this.bottomHeight = 160 + (safeAreaInsets.bottom > 0 ? uni.px2upx(safeAreaInsets.bottom) : 0);


            // 监听订单提交成功事件，用于从其他页面返回时刷新列表
			uni.$on('orderSubmitted', (data) => {
                if(data && data.forceRefresh) {
                    this.initData(true); // 强制刷新
                }
			});
		},
		
		onUnload() {
			uni.$off('orderSubmitted');
			// if (this.observer) { // 如果使用了 IntersectionObserver
			// 	this.observer.disconnect();
			// }
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			if (this.isRefreshing) return;
			this.isRefreshing = true;
			this.pageIndex = 1;
			// this.list = []; // 清空列表，由getOrderAll的逻辑处理
			this.status = 'more'; // 重置加载更多状态
			this.initData(true); // 强制刷新数据
		},
		
		onReachBottom(){
            if (this.status === 'noMore' || this.isLoading || this.isRefreshing){
                return;
            }
            this.pageIndex++;
            this.getOrderAll(false); // 非强制刷新，追加数据
        },
		// mounted() { // IntersectionObserver 相关，当前未使用
		// 	this.initVirtualScroll();
		// },
		// beforeDestroy() { // IntersectionObserver 相关，当前未使用
		// 	if (this.observer) {
		// 		this.observer.disconnect();
		// 	}
		// },
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
			this.showTopBtn = e.scrollTop > 300;
        },
		methods: {
			// 检查网络状态
			async checkNetworkStatus() {
				try {
					const networkType = await this.getNetworkType();
					this.networkStatus = networkType !== 'none';
					if (!this.networkStatus) {
						this.handleOffline();
					}
				} catch (err) {
					console.log('网络状态检查失败');
					// 即使检查失败，也假设有网络，让后续流程尝试加载
					this.networkStatus = true; 
				}
			},

			// 获取网络类型
			getNetworkType() {
				return new Promise((resolve) => {
					uni.getNetworkType({
						success: (res) => resolve(res.networkType),
						fail: () => resolve('none') // 发生错误时返回'none'
					});
				});
			},

			// 处理离线状态
			handleOffline() {
				this.showSkeleton = false; // 无网络时隐藏骨架屏
				if (!this.list.length) { // 仅当列表为空时提示
					uni.showToast({
						title: this.$t("networkError"), // 使用i18n
						icon: 'none',
						duration: 2000
					});
				}
			},

			// 检测并设置语言
			async detectAndSetLanguage() {
				try {
					let storedLang = uni.getStorageSync('lang');
					
					if (!storedLang) {
						const systemInfo = uni.getSystemInfoSync();
						let deviceLanguage = systemInfo.language;
						let mappedLanguage = this.mapSystemLanguage(deviceLanguage);
						
						if (!SUPPORTED_LANGUAGES.includes(mappedLanguage)) {
							mappedLanguage = DEFAULT_LANGUAGE;
						}
						
						uni.setStorageSync('lang', mappedLanguage);
						this.currentLanguage = mappedLanguage;
					} else {
						this.currentLanguage = storedLang;
					}
					
					this.$i18n.locale = this.currentLanguage; //确保i18n实例使用当前语言
					uni.$emit('languageChanged', this.currentLanguage); //通知其他组件语言变更
					
				} catch (error) {
					console.error('语言检测错误:', error);
					this.currentLanguage = DEFAULT_LANGUAGE;
					uni.setStorageSync('lang', DEFAULT_LANGUAGE);
					this.$i18n.locale = DEFAULT_LANGUAGE;
				}
			},
			
			mapSystemLanguage(systemLang) {
				if (!systemLang) return DEFAULT_LANGUAGE;
				const langMap = {
					'zh': 'CN', 'zh-cn': 'CN', 'zh-hans': 'CN',
					'en': 'EN',
					'es': 'ES',
					'pt': 'PT',
					'id': 'IDN', 'in': 'IDN', // 'in' is old code for Indonesian
					'vi': 'VN'
				};
				const lowerLang = systemLang.toLowerCase();
				const baseLang = lowerLang.split('-')[0];
				return langMap[lowerLang] || langMap[baseLang] || DEFAULT_LANGUAGE;
			},
			
			initializeUI() {
				// 更新tab和picker的文本，因为它们依赖i18n
				this.timeArray=[
					{name:this.i18n.all,time:"1"}, // '全部时间'等选项，如果业务需要筛选时间
					{name:this.i18n.today,time:"2"},
					{name:this.i18n.yesday,time:"3"},
					{name:this.i18n.seven,time:"4"}
				];
				this.pickerName = this.i18n.all; // 默认时间选择
				
				this.tabs = [ // 订单状态tab
					{name:this.$t("order").all, val:''},
					{name: this.$t("order").success, val: 2},
					{name: this.$t("order").error, val: -1},
					{name: this.$t("order").padding, val: 1}
				];
				
				this.contentText = { // uni-load-more的文本
					contentdown: this.more.contentdown || '',
					contentrefresh: this.more.contentrefresh || '',
					contentnomore: this.more.contentnomore || ''
				};
			},
			
			thousand(num) {
			    return num ? Math.floor(Number(num)).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") : '0';
			},
			
			// queryByInput(){ // 合并到initData
			//     this.pageIndex = 1;
			// 	this.getUserInfo();
			// },
			
            async getUserInfo(forceRefresh = false) {
                try {
                    if (!this.networkStatus && !this.silentLoading) {
                        this.handleOffline();
                        return null; 
                    }
                    // console.log('开始获取用户信息');
                    const res = await this.$tools.Post("api/user/info", {
                        api_token: uni.getStorageSync('token'),
                        language: uni.getStorageSync('lang'),
                        _timestamp: new Date().getTime() // Cache buster for user info
                    });
                    console.log('用户信息返回:', JSON.stringify(res)); // Log user info response

                    if (res.status == 200) {
                        this.userInfo = res.data;
                        this.retryCount = 0; // 成功后重置重试计数
                        return this.userInfo;
                    } else {
                        throw new Error(res.msg || 'Failed to get user info');
                    }
                } catch (err) {
                    console.error('获取用户信息失败:', err);
                    await this.handleRequestError('getUserInfo', forceRefresh);
                    return null; // 返回null表示获取失败
                }
            },

            async getOrderAll(isRefresh = false) {
                if (this.isLoading && !isRefresh) return; // 防止重复加载更多
                    this.isLoading = true;
                if (isRefresh || this.pageIndex === 1) {
                    this.showSkeleton = true; // 刷新或首页时显示骨架屏
                }
                
                if (!this.userInfo) {
                    console.log("无用户信息，无法获取订单");
                    this.isLoading = false;
                    this.showSkeleton = false;
                    if (this.isRefreshing) {
                        this.isRefreshing = false;
                        uni.stopPullDownRefresh();
                    }
                    return;
                }
                    
                    let params = {
                        limit: this.pageSize,
                        api_token: uni.getStorageSync('token'),
                        uid: this.userInfo.uid,
                        language: uni.getStorageSync('lang'),
                        page: this.pageIndex,
                    _timestamp: new Date().getTime() // Cache-busting parameter
                };
                
                console.log('Params sent to getOrderAll:', JSON.stringify(params)); // Log request params
                try {
                    const res = await this.$tools.Post("api/user/ordering/get", params);
                    console.log('Raw API Response in getOrderAll:', JSON.stringify(res)); // Log API response
                    
                    if (res.status == 200) {
                        if (res.data && res.data.data) {
                            const newOrders = res.data.data;
                            // console.log('订单数据 (before sort):', JSON.stringify(newOrders));
                            // 对数据进行时间倒序排序，确保最新订单在最前面 - 后端paginate可能已排序，但前端确认下也好
                            const sortedData = newOrders.sort((a, b) => {
                                const timeA = new Date(a.created_at || 0).getTime();
                                const timeB = new Date(b.created_at || 0).getTime();
                                return timeB - timeA;
                            });
                            // console.log('订单数据 (after sort):', JSON.stringify(sortedData));
                            
                            if (this.pageIndex === 1 || isRefresh) { // 首页或刷新
                                this.list = sortedData;
                            } else { // 加载更多
                                // 合并数据时去重 (基于oid)
                                const existingOids = new Set(this.list.map(o => o.oid));
                                const uniqueNewOrders = sortedData.filter(item => !existingOids.has(item.oid));
                                this.list = [...this.list, ...uniqueNewOrders];
                            }
                            // console.log('当前订单列表 (this.list):', JSON.stringify(this.list));
                            
                            // 判断是否有更多数据，Laravel paginate 通常返回 current_page 和 last_page
                            // 或者直接判断返回的数据量是否小于pageSize
                            if (res.data.current_page && res.data.last_page) {
                                this.hasMore = res.data.current_page < res.data.last_page;
                            } else {
                                this.hasMore = newOrders.length >= this.pageSize;
                            }
                            this.status = this.hasMore ? 'more' : 'noMore';
                        } else { // 无数据或数据格式不符
                            if (this.pageIndex === 1 || isRefresh) {
                                this.list = []; // 首页或刷新时无数据则清空
                            }
                            this.status = 'noMore';
                            this.hasMore = false;
                        }
                    } else if (res.status == 401) { // Token失效等
                        uni.removeStorageSync('token');
                        uni.reLaunch({ url: '/pages/login/login' });
                    } else { // 其他API错误
                        if (!this.silentLoading) {
                             uni.showToast({ title: res.msg || this.$t("loadFail"), duration: 1500, icon: 'none' });
                        }
                        if (this.pageIndex > 1) this.pageIndex--; // 加载更多失败，页码回退
                        this.status = 'more'; // 允许重试
                    }
                } catch (err) {
                    console.error('获取订单列表失败:', err);
                    if (!this.silentLoading) {
                        uni.showToast({ title: this.$t("networkErrorRetry"), duration: 1500, icon: 'none' });
                    }
                    if (this.pageIndex > 1) this.pageIndex--; // 加载更多失败，页码回退
                    this.status = 'more'; // 允许重试
                    await this.handleRequestError('getOrderAll', isRefresh);
                } finally {
                    this.isLoading = false;
                    this.showSkeleton = false; // 数据加载完成（或失败）后隐藏骨架屏
                    if (this.isRefreshing) {
                        this.isRefreshing = false;
                        uni.stopPullDownRefresh();
                    }
                    this.silentLoading = false;
                }
            },

            async handleRequestError(source, isRefresh = false) {
                if (this.retryCount < this.maxRetries) {
                    this.retryCount++;
                    const delay = this.retryDelay * Math.pow(2, this.retryCount - 1);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    
                    this.silentLoading = true; // 后续重试静默进行
                    if (source === 'getUserInfo') {
                        const userInfo = await this.getUserInfo(isRefresh); // 再次尝试获取用户信息
                        if (userInfo) await this.getOrderAll(isRefresh); // 如果成功，则获取订单
                    } else if (source === 'getOrderAll') {
                        await this.getOrderAll(isRefresh); // 再次尝试获取订单
                    }
                } else if (!this.silentLoading) { // 达到最大重试次数且非静默
                    this.showSkeleton = false;
                    if (this.isRefreshing) {
                        this.isRefreshing = false;
                        uni.stopPullDownRefresh();
                    }
                    // 根据source决定是否显示全局错误提示
                    // if (source === 'getUserInfo' && !this.userInfo) {
                    //     // 用户信息多次获取失败，可能需要更强的提示或处理
                    // } else if (source === 'getOrderAll' && this.list.length === 0) {
                    //     // 订单列表多次获取失败且列表为空
                    // }
                }
            },

			async initData(forceRefresh = false) {
				if (this.isLoading && !forceRefresh) return; // 如果正在加载且非强制刷新，则返回
                this.isLoading = true;
                this.showSkeleton = true; // 开始加载数据前显示骨架屏
				
                if (forceRefresh) {
                    this.pageIndex = 1;
                    this.list = []; // 强制刷新时清空列表
                    this.status = 'more'; // 重置加载状态
                }
                
                const userInfo = await this.getUserInfo(forceRefresh); // 先获取或确认用户信息
				if (userInfo) {
					await this.getOrderAll(forceRefresh); // 然后获取订单
				} else {
                    // 用户信息获取失败，停止后续操作
                    this.isLoading = false;
                    this.showSkeleton = false;
                    if (this.isRefreshing) {
					this.isRefreshing = false;
                        uni.stopPullDownRefresh();
                    }
                }
			},

			tabFun(index, val) {
				// 只更新flag和flag2，不重新从API获取数据
				// 视图会根据flag2的改变自动重新渲染（客户端筛选）
				this.flag = index;
				this.flag2 = val;
			},

			async handleRefresh() { // 已被 onPullDownRefresh 替代，但保留按钮的点击事件
				if (this.isRefreshing) return;
				
				uni.startPullDownRefresh(); // 手动触发下拉刷新处理
			},

			// 滚动到顶部方法
			scrollToTop() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				});
			},
		}
	}
</script>

<style lang="less" scoped>
    @import url('../../static/css/mian.css');
	@import url('../../static/css/order.less');
	.list_wrap {
		position: relative;
		min-height: 200rpx; // 确保容器有最小高度
		
		.order-content { // 此class在模板中未使用
			position: relative;
			width: 100%;
			transition: opacity 0.3s ease;
			will-change: opacity;
		}
	}

	.empty-state {
		display: flex;
		justify-content: center;
			align-items: center;
		min-height: 200rpx; /* 与list_wrap的min-height匹配或根据需要调整 */
		padding: 40rpx;
		color: #999;
		font-size: 28rpx;
	}

</style>
