<?php
/**
 * 测试订单API修复结果
 * 验证405和500错误是否已解决
 */

echo "<h1>🧪 订单API测试工具</h1>\n";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";

// 测试配置
$baseUrl = 'http://localhost:8080';
$testUserId = '1'; // 测试用户ID
$testToken = 'test_token'; // 测试token

// 1. 测试基础连接
echo "<h2>1. 基础连接测试</h2>\n";

function testUrl($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

// 测试基础URL
echo "<h3>1.1 测试基础URL</h3>\n";
$result = testUrl($baseUrl);
echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>\n";
echo "<p><strong>URL:</strong> $baseUrl</p>\n";
echo "<p><strong>HTTP状态码:</strong> " . $result['http_code'] . "</p>\n";
if ($result['error']) {
    echo "<p style='color: red;'><strong>错误:</strong> " . $result['error'] . "</p>\n";
} else {
    echo "<p style='color: green;'>✅ 基础连接正常</p>\n";
}
echo "</div>\n";

// 2. 测试订单API端点
echo "<h2>2. 订单API端点测试</h2>\n";

$apiTests = [
    [
        'name' => '订单获取 (GET)',
        'url' => "$baseUrl/api/user/ordering/get?api_token=$testToken&uid=$testUserId&limit=10",
        'method' => 'GET',
        'expected_codes' => [200, 401, 422] // 200=成功, 401=未认证, 422=参数错误
    ],
    [
        'name' => '订单添加 (GET)',
        'url' => "$baseUrl/api/user/ordering/add?api_token=$testToken&uid=$testUserId",
        'method' => 'GET',
        'expected_codes' => [200, 401, 422, 400] // 400=缺少必要参数
    ],
    [
        'name' => '订单获取 (POST)',
        'url' => "$baseUrl/api/user/ordering/get",
        'method' => 'POST',
        'data' => "api_token=$testToken&uid=$testUserId&limit=10",
        'expected_codes' => [200, 401, 422]
    ]
];

foreach ($apiTests as $test) {
    echo "<h3>2." . (array_search($test, $apiTests) + 1) . " " . $test['name'] . "</h3>\n";
    
    $result = testUrl($test['url'], $test['method'], $test['data'] ?? null);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<p><strong>URL:</strong> " . $test['url'] . "</p>\n";
    echo "<p><strong>方法:</strong> " . $test['method'] . "</p>\n";
    echo "<p><strong>HTTP状态码:</strong> " . $result['http_code'] . "</p>\n";
    
    // 判断结果
    if (in_array($result['http_code'], $test['expected_codes'])) {
        echo "<p style='color: green;'>✅ 状态码正常 (不是405或500错误)</p>\n";
    } elseif ($result['http_code'] == 405) {
        echo "<p style='color: red;'>❌ 405错误 - Method Not Allowed (路由问题)</p>\n";
    } elseif ($result['http_code'] == 500) {
        echo "<p style='color: red;'>❌ 500错误 - Internal Server Error (服务器内部错误)</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ 其他状态码: " . $result['http_code'] . "</p>\n";
    }
    
    if ($result['error']) {
        echo "<p style='color: red;'><strong>连接错误:</strong> " . $result['error'] . "</p>\n";
    }
    
    // 显示响应内容（前200字符）
    if ($result['response']) {
        $shortResponse = substr($result['response'], 0, 200);
        echo "<p><strong>响应内容:</strong></p>\n";
        echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>" . htmlspecialchars($shortResponse) . "...</pre>\n";
    }
    
    echo "</div>\n";
}

// 3. 测试其他相关API
echo "<h2>3. 相关API测试</h2>\n";

$relatedTests = [
    [
        'name' => '用户信息',
        'url' => "$baseUrl/api/user/info?api_token=$testToken&uid=$testUserId",
        'method' => 'GET'
    ],
    [
        'name' => '系统信息',
        'url' => "$baseUrl/api/system/info",
        'method' => 'GET'
    ],
    [
        'name' => '产品列表',
        'url' => "$baseUrl/api/product/list",
        'method' => 'GET'
    ]
];

foreach ($relatedTests as $test) {
    echo "<h3>3." . (array_search($test, $relatedTests) + 1) . " " . $test['name'] . "</h3>\n";
    
    $result = testUrl($test['url'], $test['method']);
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<p><strong>HTTP状态码:</strong> " . $result['http_code'] . "</p>\n";
    
    if ($result['http_code'] == 200) {
        echo "<p style='color: green;'>✅ 正常</p>\n";
    } elseif ($result['http_code'] == 405) {
        echo "<p style='color: red;'>❌ 405错误</p>\n";
    } elseif ($result['http_code'] == 500) {
        echo "<p style='color: red;'>❌ 500错误</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ 状态码: " . $result['http_code'] . "</p>\n";
    }
    echo "</div>\n";
}

// 4. 测试总结
echo "<h2>4. 测试总结</h2>\n";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 5px; border-left: 4px solid #007bff;'>\n";
echo "<h3>📊 测试结果分析：</h3>\n";

echo "<h4>✅ 成功指标：</h4>\n";
echo "<ul>\n";
echo "<li>没有405错误 (Method Not Allowed)</li>\n";
echo "<li>没有500错误 (Internal Server Error)</li>\n";
echo "<li>返回200、401、422等正常状态码</li>\n";
echo "</ul>\n";

echo "<h4>❌ 需要关注的问题：</h4>\n";
echo "<ul>\n";
echo "<li>如果仍有405错误：检查路由配置</li>\n";
echo "<li>如果仍有500错误：检查Laravel日志</li>\n";
echo "<li>如果有401错误：这是正常的认证错误</li>\n";
echo "<li>如果有422错误：这是正常的参数验证错误</li>\n";
echo "</ul>\n";

echo "<h4>🔧 下一步操作：</h4>\n";
echo "<ol>\n";
echo "<li>如果测试通过：使用真实的用户token和数据进行完整测试</li>\n";
echo "<li>如果仍有错误：查看Laravel日志文件获取详细错误信息</li>\n";
echo "<li>确保数据库连接正常且相关表存在</li>\n";
echo "</ol>\n";
echo "</div>\n";

// 5. 实际使用建议
echo "<h2>5. 实际使用建议</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>\n";
echo "<h4>🚀 在前端应用中测试：</h4>\n";
echo "<ol>\n";
echo "<li><strong>登录用户</strong><br>\n";
echo "确保用户已成功登录并获得有效的API token</li>\n";

echo "<li><strong>测试订单获取</strong><br>\n";
echo "在订单页面尝试获取订单列表</li>\n";

echo "<li><strong>测试订单创建</strong><br>\n";
echo "尝试创建新订单（如果有相关功能）</li>\n";

echo "<li><strong>监控错误</strong><br>\n";
echo "在浏览器开发者工具中监控网络请求和错误</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: 'Courier New', monospace; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
ol li, ul li { margin: 8px 0; }
</style>\n";

echo "<p><strong>测试完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
