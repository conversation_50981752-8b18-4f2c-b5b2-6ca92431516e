<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UsersBankInfo;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UsersBankInfoController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UsersBankInfo(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('uid');
            $grid->column('bank_name');
            $grid->column('bank_id');
            $grid->column('status');
            $grid->column('msg');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UsersBankInfo(), function (Show $show) {
            $show->field('id');
            $show->field('uid');
            $show->field('bank_name');
            $show->field('bank_id');
            $show->field('status');
            $show->field('msg');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UsersBankInfo(), function (Form $form) {
            $form->display('id');
            $form->text('uid');
            $form->text('bank_name');
            $form->text('bank_id');
            $form->text('status');
            $form->text('msg');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
