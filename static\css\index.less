.home{
	padding-bottom: 200rpx;
}
.homeTop{
	height: 580rpx;
	position: relative;
	border-radius: 0 0 50rpx 50rpx;
	overflow: hidden;
	z-index: 1;
	.swiper{
		height: 100%;
	}
}
.homeBox{
	margin: 85rpx 30rpx 0 30rpx;
	height: 710rpx;
	background: url("../../static/img/home-card.png");
	background-repeat: no-repeat;
	background-size: 100% 100%;
}
.homeSearch{
	height: 80rpx;
	background: #FFFFFF;
	border: 1rpx solid #172D52;
	border-radius: 40rpx;
	margin: 0 75rpx;
	display: flex;
	align-items: center;
	padding: 0 25rpx;
	position: relative;
	z-index: 2;
	margin-top: -40rpx;
	image{
		width: 33rpx;
		height: 33rpx;
		margin-right: 17rpx;
	}
	input{
		width: calc(100% - 50rpx);
		height: 80rpx;
		line-height: 80rpx;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #333;
	}
}
.homeMsg{
	height: 116rpx;
	background: #FFE880 0 0 no-repeat padding-box;
	border-radius: 16rpx;
	margin: 56rpx 32rpx 0 32rpx;
	padding: 0 36rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.txt{
		font: normal normal normal 28rpx/28px Roboto;
		letter-spacing: 0;
		color: #27272A;
		line-height: 32rpx;
		width: calc(100% - 207rpx);
		overflow : hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	.image{
		width: 148rpx;
		height: 38rpx;
		margin-right: 26rpx;
	}
	.arrow{
		width: 13rpx;
		height: 23rpx;
		margin-left: 20rpx;
	}
}
.homeAll{
	position: relative;
	padding-top: 120rpx;
	.homeAll-title{
		width: 320rpx;
		height: 140rpx;
		background: url("../../static/img/home-card-top.png");
		background-repeat: no-repeat;
		background-size: 100% 100%;
		position: absolute;
		top: -65rpx;
		left: calc(50% - 160rpx);
		display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
		padding-top: 10rpx;
		.homeAll-title-txt{
			display: flex;
			align-items: center;
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #1D2C49;
			margin-top: 20rpx;
			padding-right: 30rpx;
			image{
				width: 38rpx;
				height: 38rpx;
				margin-right: 6rpx;
			}
		}
	}
	.homeAll-box{
		height: 590rpx;
		display: flex;
		flex-wrap: wrap;
	}
	.homeAll-item{
		width: 130rpx; /* 如果希望更紧凑，可减小宽度 */
		height: 130rpx;
		/* 改为4个后计算公式：(100% - (130rpx * 4)) / (4*2) */
		margin: 0 calc((100% - 520rpx) / 8); 
		image{
			width: 100%;
			height: 100%;
		}
	}
}


.home{
	.headTopBg{
		top: 0;
		left: 0;
		z-index: -1;
		width: 100%;
		height: 340rpx;
		background: #FFB296;
		border-radius: 100% 100% 100% 100%/0 0 30% 30%;
	}
	.index_head{
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 40rpx 20rpx 30rpx;
		.left{
			font-size: 16px;
			font-family: PingFang SC;
			font-weight: bold;
			color: #F5FBFF;
			image{

			}
		}
		.right{
			display: flex;
			.box{
				display: flex;
				align-items: center;
				image{
					width: 32rpx;
					height: 32rpx;
					margin-right: 10rpx;
				}
				font-size: 14px;
				font-family: PingFang SC;
				font-weight: 500;
				color: #F5FBFF;
				margin-left: 30rpx;
				.xia{
					width: 22rpx;
					height: 12rpx;
					margin-left: 10rpx;
				}
			}
		}
	}
	.listFour{
		padding: 32rpx 50rpx 0;
		display: flex;
		justify-content: space-between;
		.item{
			width: 124rpx;
			image{
				width: 100%;
				height: 80rpx;
				margin-bottom: 20rpx;
			}
			font-size: 14px;
			font-family: PingFang SC;
			font-weight: 500;
			color: #333333;
			text-align: center;
		}
	}
    .fiveList{
		display: flex;
		flex-wrap: wrap;
		margin: 0 20rpx 10rpx;
		.flex2{
		   flex: 2;
			background-size: 100% 100%;
		   height: 230rpx;
		}
		.flex1{
		   flex: 1;
		   margin-left: 10rpx;
		   height: 230rpx;
		   .title{
			   font-size: 16px;
			   font-family: PingFang SC;
			   font-weight: bold;
			   margin-top: 20rpx;
			   margin-left: 20rpx;
		   }
		   .gycolor{
			    color: #FF5435;
		   }

		}
		.juan{
			background-size: 100% 100%;
		}
		.gongyi{
			background-size: 100% 100%;
		}

	}
	.fiveListBottom{
		display: flex;
		margin: 10rpx 20rpx 0;
		.flex1{
		   flex: 1;
		   margin-left: 10rpx;
		   height: 230rpx;
		   .title{
			   font-size: 16px;
			   font-family: PingFang SC;
			   font-weight: bold;
			   margin-top: 20rpx;
			   margin-left: 20rpx;
		   }
		   .gycolor{
			    color: #FF5435;

		   }

		}
		.flex1:first-child{
			margin-left: 0;
		}
		.mpcolor{
					    color: #C69952;
		}
		.hwcolor{
					    color: #26AF9D;
		}
		.jzcolor{
					     color: #3586FF;
		}
		.mingpai{
			background-size: 100% 100%;
		}
		.haiwai{
			background-size: 100% 100%;
		}
		.juzhu{
			background-size: 100% 100%;
		}
	}
	.bgTitle{
		margin: 30rpx 0;
		font-size: 18px;
		font-family: PingFang SC;
		font-weight: bold;
		color: #333333;
		image{
			width: 32rpx;
			height: 28rpx;
		}
		image:first-child{
			margin-right: 10rpx;
		}
		image:last-child{
			margin-left: 10rpx;
		}
	}
	.hotList{
		display: flex;
		flex-wrap: wrap;
		margin: 0 20rpx;
		.item{
			margin-bottom: 22rpx;
			height: 290rpx;
			width: calc((100% - 22rpx)/2);
			background-size: 100% 100%;
			position: relative;
			.hot{
				display: flex;
				justify-content: flex-end;
				// margin: 28rpx 30rpx 26rpx ;
				position: absolute;
				right: 30rpx;
				top: 28rpx;
				.hotbg{
					font-size: 12px;
					font-family: Euclid Circular A;
					font-weight: 500;
					color: #FFFFFF;
					padding: 4rpx 10rpx;
					background-size: 100% 100%;
				}
			}
			.imgbox{
				margin-top: 40rpx;
				.image{
					width:142rpx;
					height: 134rpx;
				}
			}
			.name{
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 20rpx;
				position: absolute;
				width: 100%;
				bottom: 60rpx;
				.left{
					font-size: 16px;
					font-family: PingFang SC;
					font-weight: bold;
					color: #333333;
				}
				.right{
					font-size: 16px;
					font-family: Euclid Circular A;
					font-weight: 500;
					color: #FF0A00;
				}
			}
			.all{
				font-size: 12px;
				font-family: PingFang SC;
				font-weight: 500;
				color: #333333;
				position: absolute;
				bottom: 24rpx;
				right: 20rpx;
			}
		}
		.item:nth-child(2n){
			margin-left: 22rpx;
		}
	}
}
