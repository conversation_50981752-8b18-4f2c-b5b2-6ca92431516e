/*!
  * message-compiler v9.2.2
  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var IntlifyMessageCompiler=function(e){"use strict";const t=Object.assign,n=e=>"string"==typeof e,r={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTE<PERSON>_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,__EXTEND_POINT__:15},c={[r.EXPECTED_TOKEN]:"Expected token: '{0}'",[r.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[r.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[r.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[r.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[r.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[r.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[r.EMPTY_PLACEHOLDER]:"Empty placeholder",[r.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[r.INVALID_LINKED_FORMAT]:"Invalid linked format",[r.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[r.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[r.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[r.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'"};function o(e,t,n={}){const{domain:r,messages:c,args:o}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}function s(e,t,n){return{line:e,column:t,offset:n}}function u(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const a=" ",i="\n",l=String.fromCharCode(8232),f=String.fromCharCode(8233);function d(e){const t=e;let n=0,r=1,c=1,o=0;const s=e=>"\r"===t[e]&&t[e+1]===i,u=e=>t[e]===f,a=e=>t[e]===l,d=e=>s(e)||(e=>t[e]===i)(e)||u(e)||a(e),p=e=>s(e)||u(e)||a(e)?i:t[e];function E(){return o=0,d(n)&&(r++,c=0),s(n)&&n++,n++,c++,t[n]}return{index:()=>n,line:()=>r,column:()=>c,peekOffset:()=>o,charAt:p,currentChar:()=>p(n),currentPeek:()=>p(n+o),next:E,peek:function(){return s(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,c=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)E();o=0}}}const p=void 0;function E(e,t={}){const n=!1!==t.location,r=d(e),c=()=>r.index(),o=()=>s(r.line(),r.column(),r.index()),l=o(),f=c(),E={currentType:14,offset:f,startLoc:l,endLoc:l,lastType:14,lastOffset:f,lastStartLoc:l,lastEndLoc:l,braceNest:0,inLinked:!1,text:""},L=()=>E,{onError:k}=t;function h(e,t,r){e.endLoc=o(),e.currentType=t;const c={type:t};return n&&(c.loc=u(e.startLoc,e.endLoc)),null!=r&&(c.value=r),c}const N=e=>h(e,14);function _(e,t){return e.currentChar()===t?(e.next(),t):(o(),"")}function T(e){let t="";for(;e.currentPeek()===a||e.currentPeek()===i;)t+=e.currentPeek(),e.peek();return t}function C(e){const t=T(e);return e.skipToPeek(),t}function P(e){if(e===p)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function y(e,t){const{currentType:n}=t;if(2!==n)return!1;T(e);const r=function(e){if(e===p)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function x(e){T(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function m(e,t=!0){const n=(t=!1,r="",c=!1)=>{const o=e.currentPeek();return"{"===o?"%"!==r&&t:"@"!==o&&o?"%"===o?(e.peek(),n(t,"%",!0)):"|"===o?!("%"!==r&&!c)||!(r===a||r===i):o===a?(e.peek(),n(!0,a,c)):o!==i||(e.peek(),n(!0,i,c)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function I(e,t){const n=e.currentChar();return n===p?p:t(n)?(e.next(),n):null}function O(e){return I(e,(e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}))}function S(e){return I(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57}))}function b(e){return I(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}))}function A(e){let t="",n="";for(;t=S(e);)n+=t;return n}function v(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!m(e))break;t+=n,e.next()}else if(n===a||n===i)if(m(e))t+=n,e.next();else{if(x(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function D(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return U(e,t,4);case"U":return U(e,t,6);default:return o(),""}}function U(e,t,n){_(e,t);let r="";for(let t=0;t<n;t++){const t=b(e);if(!t){o(),e.currentChar();break}r+=t}return`\\${t}${r}`}function g(e){C(e);const t=_(e,"|");return C(e),t}function M(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&o(),e.next(),n=h(t,2,"{"),C(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&o(),e.next(),n=h(t,3,"}"),t.braceNest--,t.braceNest>0&&C(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&o(),n=R(e,t)||N(t),t.braceNest=0,n;default:let r=!0,c=!0,s=!0;if(x(e))return t.braceNest>0&&o(),n=h(t,1,g(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return o(),t.braceNest=0,w(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;T(e);const r=P(e.currentPeek());return e.resetPeek(),r}(e,t))return n=h(t,5,function(e){C(e);let t="",n="";for(;t=O(e);)n+=t;return e.currentChar()===p&&o(),n}(e)),C(e),n;if(c=y(e,t))return n=h(t,6,function(e){C(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${A(e)}`):t+=A(e),e.currentChar()===p&&o(),t}(e)),C(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;T(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=h(t,7,function(e){C(e),_(e,"'");let t="",n="";const r=e=>"'"!==e&&e!==i;for(;t=I(e,r);)n+="\\"===t?D(e):t;const c=e.currentChar();return c===i||c===p?(o(),c===i&&(e.next(),_(e,"'")),n):(_(e,"'"),n)}(e)),C(e),n;if(!r&&!c&&!s)return n=h(t,13,function(e){C(e);let t="",n="";const r=e=>"{"!==e&&"}"!==e&&e!==a&&e!==i;for(;t=I(e,r);)n+=t;return n}(e)),o(),n.value,C(e),n}return n}function R(e,t){const{currentType:n}=t;let r=null;const c=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||c!==i&&c!==a||o(),c){case"@":return e.next(),r=h(t,8,"@"),t.inLinked=!0,r;case".":return C(e),e.next(),h(t,9,".");case":":return C(e),e.next(),h(t,10,":");default:return x(e)?(r=h(t,1,g(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;T(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;T(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(C(e),R(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;T(e);const r=P(e.currentPeek());return e.resetPeek(),r}(e,t)?(C(e),h(t,12,function(e){let t="",n="";for(;t=O(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?P(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===a||!t)&&(t===i?(e.peek(),r()):P(t))},c=r();return e.resetPeek(),c}(e,t)?(C(e),"{"===c?M(e,t)||r:h(t,11,function(e){const t=(n=!1,r)=>{const c=e.currentChar();return"{"!==c&&"%"!==c&&"@"!==c&&"|"!==c&&c?c===a?r:c===i?(r+=c,e.next(),t(n,r)):(r+=c,e.next(),t(!0,r)):r};return t(!1,"")}(e))):(8===n&&o(),t.braceNest=0,t.inLinked=!1,w(e,t))}}function w(e,t){let n={type:14};if(t.braceNest>0)return M(e,t)||N(t);if(t.inLinked)return R(e,t)||N(t);switch(e.currentChar()){case"{":return M(e,t)||N(t);case"}":return o(),e.next(),h(t,3,"}");case"@":return R(e,t)||N(t);default:if(x(e))return n=h(t,1,g(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:c}=function(e){const t=T(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return c?h(t,0,v(e)):h(t,4,function(e){C(e);return"%"!==e.currentChar()&&o(),e.next(),"%"}(e));if(m(e))return h(t,0,v(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=E;return E.lastType=e,E.lastOffset=t,E.lastStartLoc=n,E.lastEndLoc=s,E.offset=c(),E.startLoc=o(),r.currentChar()===p?h(E,14):w(r,E)},currentOffset:c,currentPosition:o,context:L}}const L="parser",k=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function h(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function N(e={}){const n=!1!==e.location,{onError:r}=e;function c(e,t,r){const c={type:e,start:t,end:t};return n&&(c.loc={start:r,end:r}),c}function o(e,t,r,c){e.end=t,c&&(e.type=c),n&&e.loc&&(e.loc.end=r)}function s(e,t){const n=e.context(),r=c(3,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}function u(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(5,r,s);return u.index=parseInt(t,10),e.nextToken(),o(u,e.currentOffset(),e.currentPosition()),u}function a(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(4,r,s);return u.key=t,e.nextToken(),o(u,e.currentOffset(),e.currentPosition()),u}function i(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(9,r,s);return u.value=t.replace(k,h),e.nextToken(),o(u,e.currentOffset(),e.currentPosition()),u}function l(e){const t=e.context(),n=c(6,t.offset,t.startLoc);let r=e.nextToken();if(9===r.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(8,r,s);return 12!==t.type?(n.lastStartLoc,u.value="",o(u,r,s),{nextConsumeToken:t,node:u}):(null==t.value&&(n.lastStartLoc,_(t)),u.value=t.value||"",o(u,e.currentOffset(),e.currentPosition()),{node:u})}(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(10!==r.type&&(t.lastStartLoc,_(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&(t.lastStartLoc,_(r)),n.key=function(e,t){const n=e.context(),r=c(7,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 5:null==r.value&&(t.lastStartLoc,_(r)),n.key=a(e,r.value||"");break;case 6:null==r.value&&(t.lastStartLoc,_(r)),n.key=u(e,r.value||"");break;case 7:null==r.value&&(t.lastStartLoc,_(r)),n.key=i(e,r.value||"");break;default:t.lastStartLoc;const s=e.context(),l=c(7,s.offset,s.startLoc);return l.value="",o(l,s.offset,s.startLoc),n.key=l,o(n,s.offset,s.startLoc),{nextConsumeToken:r,node:n}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=c(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let r=null;do{const c=r||e.nextToken();switch(r=null,c.type){case 0:null==c.value&&(t.lastStartLoc,_(c)),n.items.push(s(e,c.value||""));break;case 6:null==c.value&&(t.lastStartLoc,_(c)),n.items.push(u(e,c.value||""));break;case 5:null==c.value&&(t.lastStartLoc,_(c)),n.items.push(a(e,c.value||""));break;case 7:null==c.value&&(t.lastStartLoc,_(c)),n.items.push(i(e,c.value||""));break;case 8:const o=l(e);n.items.push(o.node),r=o.nextConsumeToken||null}}while(14!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function d(e){const t=e.context(),{offset:n,startLoc:r}=t,s=f(e);return 14===t.currentType?s:function(e,t,n,r){const s=e.context();let u=0===r.items.length;const a=c(1,t,n);a.cases=[],a.cases.push(r);do{const t=f(e);u||(u=0===t.items.length),a.cases.push(t)}while(14!==s.currentType);return o(a,e.currentOffset(),e.currentPosition()),a}(e,n,r,s)}return{parse:function(r){const s=E(r,t({},e)),u=s.context(),a=c(0,u.offset,u.startLoc);return n&&a.loc&&(a.loc.source=r),a.body=d(s),14!==u.currentType&&(u.lastStartLoc,r[u.offset]),o(a,s.currentOffset(),s.currentPosition()),a}}}function _(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function T(e,t){for(let n=0;n<e.length;n++)C(e[n],t)}function C(e,t){switch(e.type){case 1:T(e.cases,t),t.helper("plural");break;case 2:T(e.items,t);break;case 6:C(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function P(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&C(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function y(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?y(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const c=t.cases.length;for(let n=0;n<c&&(y(e,t.cases[n]),n!==c-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const c=t.items.length;for(let n=0;n<c&&(y(e,t.items[n]),n!==c-1);n++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),y(e,t.key),t.modifier?(e.push(", "),y(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}return e.CompileErrorCodes=r,e.ERROR_DOMAIN=L,e.LocationStub={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}},e.baseCompile=function(e,r={}){const c=t({},r),o=N(c).parse(e);return P(o,c),((e,t={})=>{const r=n(t.mode)?t.mode:"normal",c=n(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,s=null!=t.breakLineCode?t.breakLineCode:"arrow"===r?";":"\n",u=t.needIndent?t.needIndent:"arrow"!==r,a=e.helpers||[],i=function(e,t){const{sourceMap:n,filename:r,breakLineCode:c,needIndent:o}=t,s={source:e.loc.source,filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:c,needIndent:o,indentLevel:0};function u(e,t){s.code+=e}function a(e,t=!0){const n=t?c:"";u(o?n+"  ".repeat(e):n)}return{context:()=>s,push:u,indent:function(e=!0){const t=++s.indentLevel;e&&a(t)},deindent:function(e=!0){const t=--s.indentLevel;e&&a(t)},newline:function(){a(s.indentLevel)},helper:e=>`_${e}`,needIndent:()=>s.needIndent}}(e,{mode:r,filename:c,sourceMap:o,breakLineCode:s,needIndent:u});i.push("normal"===r?"function __msg__ (ctx) {":"(ctx) => {"),i.indent(u),a.length>0&&(i.push(`const { ${a.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),i.newline()),i.push("return "),y(i,e),i.deindent(u),i.push("}");const{code:l,map:f}=i.context();return{ast:e,code:l,map:f?f.toJSON():void 0}})(o,c)},e.createCompileError=o,e.createLocation=u,e.createParser=N,e.createPosition=s,e.defaultOnError=function(e){throw e},e.errorMessages=c,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
