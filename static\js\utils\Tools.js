const Tools = {
	config: {
		apiUrl: 'https://admin.inspap.com/',
		imgUrl: 'https://admin.inspap.com/upload/',
		timeout: 15000, // 缩短超时时间
		retryTimes: 3,
		retryDelay: 1000,
		maxRetryDelay: 5000, // 最大重试延迟
		tokenExpirationTime: 24 * 60 * 60 * 1000, // token有效期24小时
	},
	/**
	 * 清楚本地缓存
	 * @param  {[type]} key 键名    可填(默认清楚所有缓存)
	 * @return {[type]}     [description]
	 */
	clearLocalStorage(key) {
		if (typeof uni !== 'undefined') {
			if (key) {
				uni.removeStorageSync(key);
			} else {
				uni.clearStorageSync();
			}
		}
	},
	/**
	 * 获取本地缓存
	 * @param  {[type]} key [description]
	 * @return {[type]}     [description]
	 */
	getLocalStorage(key) {
		if (typeof uni !== 'undefined') {
			return uni.getStorageSync(key);
		}
		return null;
	},
	/**
	 * 设置本地缓存
	 * @param {[type]} key     [description]
	 * @param {[type]} value   [description]
	 * @param {[type]} expires [description]
	 */
	setLocalStorage(key, value, expires) {
		if (typeof uni !== 'undefined') {
			uni.setStorageSync(key, value);
		}
	},
	/**
	 * 隐藏加载框
	 * @return {[type]} [description]
	 */
	hideLoading() {
		if (typeof uni !== 'undefined') {
			uni.hideLoading();
		}
	},
	/**
	 * 显示提示信息
	 * @param  {[type]} title    [description]
	 * @param  {Number} duration [description]
	 * @return {[type]}          [description]
	 */
	showToast(title, duration = 2000, icon = "none") {
		if (typeof uni !== 'undefined') {
			uni.showToast({
				title,
				duration,
				icon
			});
		}
	},
	/**
	 * 显示加载框
	 * @param  {[type]} title [description]
	 * @return {[type]}       [description]
	 */
	showLoading(title = '加载中...') {
		if (typeof uni !== 'undefined') {
			uni.showLoading({
				title,
				mask: false
			});
		}
	},
	/**
	 * 接口的post请求
	 * @param  {[type]} url     [description]
	 * @param  {[type]} params  [description]
	 * @param  {Object} options [description]
	 * @return {[type]}         [description]
	 */
	Post(url, params, options = {
		isLoad: false
	}) {
		options['method'] = 'GET'; // 改为GET方法确保稳定性
		return this.$http(url, params, options);
	},
	/**
	 * 接口的get请求
	 * @param  {[type]} url     [description]
	 * @param  {[type]} params  [description]
	 * @param  {Object} options [description]
	 * @return {[type]}         [description]
	 */
	Get(url, params, options = {
		isLoad: false
	}) {
		options['method'] = 'GET';
		return this.$http(url, params, options);
	},
	/**
	 * 接口put请求
	 * @param  {[type]} url     [description]
	 * @param  {[type]} params  [description]
	 * @param  {Object} options [description]
	 * @return {[type]}         [description]
	 */
	Put(url, params, options = {}) {
		options['method'] = 'PUT';
		return this.$http(url, params, options)
	},
	/**
	 * 接口delete请求
	 * @param  {[type]} url     [description]
	 * @param  {[type]} params  [description]
	 * @param  {Object} options [description]
	 * @return {[type]}         [description]
	 */
	Dele(url, params, options = {}) {
		options['method'] = 'DELETE';
		return this.$http(url, params, options)
	},
	/**
	 * 拼接图片
	 * @param  {[string]} img [string]
	 * @return {[string]}     [string]
	 */
	setImgUrl(img) {
		if (!img) return '';
		
		try {
			// 如果已经是完整的URL，直接返回
			if (img.startsWith('http://') || img.startsWith('https://')) {
				return img;
			}
			
			// 如果是base64图片，直接返回
			if (img.startsWith('data:image/')) {
				return img;
			}
			
			// 移除开头的斜杠
			const cleanPath = img.startsWith('/') ? img.slice(1) : img;
			
			// 拼接完整URL
			const fullUrl = this.config.imgUrl + cleanPath;
			
			// 验证URL是否合法
			new URL(fullUrl);
			
			return fullUrl;
		} catch (error) {
			console.error('图片URL处理错误:', error);
			return '';
		}
	},
	/**
	 * 检查网络和会话状态
	 */
	checkNetworkAndSession() {
		return new Promise((resolve, reject) => {
			if (typeof uni === 'undefined') {
				reject(new Error('uni is not defined'));
				return;
			}

			uni.getNetworkType({
				success: (res) => {
					if (res.networkType === 'none') {
						reject(new Error('network_error'));
						return;
					}

					// 检查当前页面是否是登录页
					const pages = getCurrentPages();
					const currentPage = pages[pages.length - 1];
					const isLoginPage = currentPage && currentPage.route && currentPage.route.includes('login');

					// 如果不是登录页，才检查会话状态
					if (!isLoginPage && this.checkTokenExpiration()) {
						reject(new Error('session_expired'));
						return;
					}

					resolve(true);
				},
				fail: () => {
					reject(new Error('network_error'));
				}
			});
		});
	},
	/**
	 * 请求
	 * @return {[type]} [description]
	 */
	$http(url, params = {}, options = {}, retryCount = 0) {
		let headerConfig = {};
		let zx_token;
		let lang = typeof uni !== 'undefined' ? uni.getStorageSync('lang') : '';
		
		// 检查网络状态
		return new Promise((resolve, reject) => {
			uni.getNetworkType({
				success: (res) => {
					if (res.networkType === 'none') {
						uni.showToast({
							title: lang === 'CN' ? '网络连接失败，请检查网络设置' : 'Network connection failed, please check network settings',
							icon: 'none',
							duration: 2000
						});
						reject(new Error('No network connection'));
						return;
					}
					this.processRequest(url, params, options, headerConfig, lang, retryCount).then(resolve).catch(reject);
				},
				fail: () => {
					reject(new Error('Failed to get network status'));
				}
			});
		});
	},
	/**
	 * 处理请求
	 */
	processRequest(url, params, options, headerConfig, lang, retryCount = 0) {
		return new Promise((resolve, reject) => {
			// 检查网络状态
			if (typeof uni !== 'undefined') {
				uni.getNetworkType({
					success: (res) => {
						if (res.networkType === 'none') {
							const errMsg = lang === 'CN' ? '网络连接已断开' : 'Network disconnected';
							uni.showToast({
								title: errMsg,
								icon: 'none',
								duration: 2000
							});
							reject(new Error('No network connection'));
							return;
						}
						this.executeRequest(url, params, options, headerConfig, lang, retryCount, resolve, reject);
					},
					fail: () => {
						this.executeRequest(url, params, options, headerConfig, lang, retryCount, resolve, reject);
					}
				});
			} else {
				this.executeRequest(url, params, options, headerConfig, lang, retryCount, resolve, reject);
			}
		});
	},

	executeRequest(url, params, options, headerConfig, lang, retryCount, resolve, reject) {
		const normalizedLang = this.normalizeLanguage(lang);
		const requestTimeout = this.config.timeout;

		if (options.isLoad) this.showLoading();

		// 准备请求配置
		const requestConfig = {
			url: this.config.apiUrl + url,
			data: params,
			method: options.method || 'GET',
			header: {
				...headerConfig,
				'Accept-Language': normalizedLang,
				'content-type': options.action === 'form' ? 'application/x-www-form-urlencoded' : 'application/json;charset=UTF-8',
				'Access-Control-Allow-Origin': '*',
				'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
				'Access-Control-Allow-Headers': '*'
			},
			timeout: requestTimeout
		};

		// 添加token
		const token = this.getLocalStorage('token');
		if (token) {
			requestConfig.header.token = token;
		}

		// 发起请求
		uni.request({
			...requestConfig,
			success: (res) => {
				if (options.isLoad) this.hideLoading();

				if (res.statusCode === 200 && res.data && res.data.status === 200) {
					resolve(res.data);
				} else {
					this.handleRequestError(res, normalizedLang, url, params, options, retryCount, resolve, reject);
				}
			},
			fail: (err) => {
				if (options.isLoad) this.hideLoading();
				this.handleRequestFail(err, normalizedLang, url, params, options, retryCount, resolve, reject);
			}
		});
	},

	/**
	 * 检查语言是否被支持
	 */
	isSupportedLanguage(lang) {
		// 已确认支持的语言列表
		const supportedLanguages = [
			'CN',  // 中文
			'EN',  // 英语
			'IDN', // 印尼语
			'VN',  // 越南语
			// 待确认支持的语言
			'ES',  // 西班牙语
			'PT'   // 葡萄牙语
		];
		return supportedLanguages.includes(lang);
	},

	/**
	 * 标准化语言代码
	 */
	normalizeLanguage(lang) {
		// 语言代码映射
		const langMap = {
			'es': 'ES',
			'es-ES': 'ES',
			'es-419': 'ES',
			'pt': 'PT',
			'pt-BR': 'PT',
			'pt-PT': 'PT',
			'en': 'EN',
			'en-US': 'EN',
			'en-GB': 'EN',
			'zh': 'CN',
			'zh-CN': 'CN',
			'zh-Hans': 'CN',
			'zh-TW': 'TW',
			'zh-Hant': 'TW',
			'id': 'IDN',
			'in': 'IDN',
			'id-ID': 'IDN',
			'vi': 'VN',
			'vi-VN': 'VN'
		};

		// 转换为小写进行匹配
		const lowerLang = (lang || '').toLowerCase();
		
		// 返回标准化的语言代码，如果没有匹配则返回默认值 'EN'
		const normalizedLang = langMap[lowerLang] || langMap[lowerLang.split('-')[0]] || 'EN';
		
		// 如果是不完全支持的语言，打印警告
		if (['ES', 'PT'].includes(normalizedLang)) {
			console.warn(`Using ${normalizedLang} language - Support status needs to be verified with backend`);
		}
		
		return normalizedLang;
	},

	/**
	 * 检查token是否过期
	 * @returns {boolean}
	 */
	checkTokenExpiration() {
		const token = uni.getStorageSync('token');
		const tokenTimestamp = uni.getStorageSync('tokenTimestamp');
		
		if (!token || !tokenTimestamp) {
			return true;
		}
		
		const now = new Date().getTime();
		const tokenAge = now - tokenTimestamp;
		
		// 如果token超过有效期，则认为过期
		if (tokenAge > this.config.tokenExpirationTime) {
			this.clearLoginStatus();
			return true;
		}
		
		return false;
	},
	
	/**
	 * 设置登录状态
	 * @param {string} token 
	 * @param {boolean} rememberMe 
	 */
	setLoginStatus(token, rememberMe = false) {
		if (!token) return;
		
		uni.setStorageSync('token', token);
		uni.setStorageSync('tokenTimestamp', new Date().getTime());
		
		if (rememberMe) {
			uni.setStorageSync('rememberMe', true);
		}
	},
	
	/**
	 * 清除登录状态
	 */
	clearLoginStatus() {
		uni.removeStorageSync('token');
		uni.removeStorageSync('tokenTimestamp');
		uni.removeStorageSync('rememberMe');
		uni.removeStorageSync('userInfo');
	},

	/**
	 * 处理token过期
	 */
	handleTokenExpired(lang) {
		// 检查当前是否在登录页
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		const isLoginPage = currentPage && currentPage.route && currentPage.route.includes('login');

		if (!isLoginPage) {
			this.clearLoginStatus();
			
			const message = lang === 'CN' ? 'Please log in again' : 'Login expired, please login again';
			
			uni.showToast({
				title: message,
				icon: 'none',
				duration: 2000
			});
			
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/public/login'
				});
			}, 2000);
		}
	},

	/**
	 * 处理请求错误
	 */
	handleRequestError(res, lang, url, params, options, retryCount, resolve, reject) {
		// 处理token相关错误
		if (res.statusCode === 401 || res.data.code === 401) {
			this.handleTokenExpired(lang);
			reject(new Error('Unauthorized'));
			return;
		}
		
		// 其他错误处理逻辑保持不变
		if (res.statusCode !== 200) {
			// ... 保持现有的错误处理逻辑 ...
		}
	},

	/**
	 * 处理请求失败
	 */
	handleRequestFail(err, lang, url, params, options, retryCount, resolve, reject) {
		console.error('Request failed:', err);

		// 计算退避时间
		const delay = Math.min(this.config.retryDelay * Math.pow(2, retryCount), this.config.maxRetryDelay);

		if (retryCount < this.config.retryTimes) {
			console.log(`Retrying failed request (${retryCount + 1}/${this.config.retryTimes}) after ${delay}ms`);
			setTimeout(() => {
				this.processRequest(url, params, options, {}, lang, retryCount + 1)
					.then(resolve)
					.catch(reject);
			}, delay);
		} else {
			const errMsg = lang === 'CN' ? '网络请求失败，请检查网络连接' : 'Network request failed, please check your connection';
			uni.showToast({
				title: errMsg,
				icon: 'none',
				duration: 2000
			});
			reject(err);
		}
	},

	getAll(all) {
		return Promise.all(all)
	},
	/**
	 * 上传图片
	 * **/
	uploadImg(file, url, options = {}) {
		let headerConfig = {},
			formData = {};
		url = this.config.apiUrl + url;
		let name = 'file';
		if (options.imgType) {
			formData = {
				type: options.imgType
			}
		}
		if (options.isLoad) {
			if (typeof uni !== 'undefined') {
				uni.showLoading({
					mask: true,
				});
			}
		}
		return new Promise((reslove, reject) => {
			if (typeof uni === 'undefined') {
				reject(new Error('uni is not defined'));
				return;
			}
			uni.uploadFile({
				url: url,
				filePath: file,
				header: headerConfig,
				name,
				formData,
				success: (res) => {
					res.data = JSON.parse(res.data);
					// this.showToast('上传成功', 1000, 'success');
					if (options.isLoad) this.hideLoading();
					if (res.statusCode == 200) {
						let result = res.data;
						if (result.code == 0) {
							reslove(result);
						} else {
							reject(result);
						}
					} else {
						this.showToast(errMsg);
						reject(res);
					}
				},
				fail: (res) => {
					this.hideLoading();
					this.showToast(errMsg);
					if (typeof uni !== 'undefined') {
                    	uni.stopPullDownRefresh(); // 可以停止当前页面的下拉刷新。
					}
					reject(res);
				},
				complete: (res) => {
					if (typeof uni !== 'undefined') {
						uni.stopPullDownRefresh(); // 可以停止当前页面的下拉刷新。
					}
				}
			});
		})

	},
	/**
	 * 同 window.confirm 效果
	 * @param {*} content
	 * @param {*} title
	 */
	confirm(content = '', title = '提示') {
		return new Promise((rel, rej) => {
			if (typeof uni !== 'undefined') {
				uni.showModal({
					title,
					content,
					success: function(res) {
						res.confirm ? rel(res.confirm) : rej(res.cancel);
					}
				});
			} else {
				rej(new Error('uni is not defined'));
			}
		})
	},
	/**
	 * @param {*} phone:手机号格式
	 */
	testPhone(str) {
		const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
		return reg.test(str)
	},
	/**
	 *身份证校验
	 */
	testIDCard(str) {
		const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
		return reg.test(str)
	},

	clearNullObj(obj) {
		for (var propName in obj) {
			if (!obj[propName]) {
				delete obj[propName];
			}
		}
		return obj
	},

}
export default Tools;
