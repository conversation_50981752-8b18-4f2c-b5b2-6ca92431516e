<?php

namespace App\Admin\Controllers;

use App\Models\AdmiralGenerator;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
use App\Models\Product;
use App\Models\ProductTmp;


class AdmiralGeneratorController extends AdminController
{



    public function run_gener(){

        $product = Product::get();  
        ProductTmp::truncate(); //清空预设表
       
        for($k=0;$k<count($product);$k++){

            $data[$k] = [
                'pname' => $product[$k]->pname,
                'pid'   => $product[$k]->pid,
                'id'    => $product[$k]->id,
                'qishu' => 60*60*24 / $product[$k]->period,  //总期数
                'period'=> $product[$k]->period,    //多少秒1期
            ];
            $res = $this->get_rand($data[$k]);
        
            $ms  = ProductTmp::insert($res);

        }
       

       return [
           'message' => $ms,
           'status'  => 200
       ];
    }


    public function get_rand($data){

        $id    = $data['id'];
        $pname = $data['pname'];
        $pid   = $data['pid'];
        $qishu = $data['qishu'];
        $period= $data['period'];

        $array = [];  //生产结果合集
        $time = 0;
        $s = 0;

        for($i=0;$i<$qishu;$i++){
            $s += $period;
            $time =  time()+$s;
            array_push($array,$this->rand($id,$i,$time,$pid,$pname));
            $time = 0;
        }

        return $array;

    }

    /**
     * 产生结果
     * n1,n2,n3 工具1，2，3
     * min = 小  max =大
     * 
     * $n 生成次数
     * @return Generator
     */


    protected function rand($id,$i_key,$time,$pid,$pname)
    {

        $admin = Auth::guard('admin')->user(); 

        $order = $i_key;
        $n1 = [1,2,3,4,5,6];  
        $n2 = [6,5,4,3,2,1];
        $n3 = [6,2,4,1,2,3];

 
        $key = [];
        for($i=0;$i<3;$i++){
            $key[$i] = rand(0,5);
        }

        $rand = $n1[$key[0]] + $n1[$key[1]] + $n1[$key[2]] ;

        $min = [3,4,5,6,7,8,9,10,11];
        $max =[12,13,14,15,16,17,18,19];

        $rand_dx = '';
        if(in_array($rand,$min)){
            $rand_dx = 1;
        }
        else if(in_array($rand,$max)){
            $rand_dx = 2;
        }
        else{
            $rand_dx = '未知';
        }

        $sd = $rand & 1; //余数判断

        $rand_sd = ($sd==1) ? 3 : 4;  //3单 4双


        return [
            'pname'        => $pname,
            'pid'          => $pid,
            'issue'        => date('Ymd',time()).$id+$order,  //期号 时间戳 + 产品ID + 循环KEY
            'dx'           => $rand_dx,
            'sd'           => $rand_sd,
            'action_admin' => $admin->username,
            'open_at'      => $time,//date('Y-m-d H:i:s',$time)
            // 'n1' => $n1[$key[0]],
            // 'n2' => $n1[$key[1]],
            // 'n3' => $n1[$key[2]],
            // 'count'  => $rand,
        ];
       
       
    }

}
