/*!
  * core-base v9.2.2
  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var IntlifyCoreBase=function(e){"use strict";const t=/\{([0-9a-zA-Z]+)\}/g;const n=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),r=e=>"number"==typeof e&&isFinite(e),o=e=>"[object RegExp]"===_(e),a=e=>k(e)&&0===Object.keys(e).length;function s(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const c=Object.assign;function l(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const u=Array.isArray,i=e=>"function"==typeof e,f=e=>"string"==typeof e,m=e=>"boolean"==typeof e,p=e=>null!==e&&"object"==typeof e,d=Object.prototype.toString,_=e=>d.call(e),k=e=>"[object Object]"===_(e),h={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,__EXTEND_POINT__:15};function g(e,t,n={}){const{domain:r,messages:o,args:a}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}function T(e){throw e}function b(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const E=" ",L="\n",y=String.fromCharCode(8232),N=String.fromCharCode(8233);function C(e){const t=e;let n=0,r=1,o=1,a=0;const s=e=>"\r"===t[e]&&t[e+1]===L,c=e=>t[e]===N,l=e=>t[e]===y,u=e=>s(e)||(e=>t[e]===L)(e)||c(e)||l(e),i=e=>s(e)||c(e)||l(e)?L:t[e];function f(){return a=0,u(n)&&(r++,o=0),s(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>a,charAt:i,currentChar:()=>i(n),currentPeek:()=>i(n+a),next:f,peek:function(){return s(n+a)&&a++,a++,t[n+a]},reset:function(){n=0,r=1,o=1,a=0},resetPeek:function(e=0){a=e},skipToPeek:function(){const e=n+a;for(;e!==n;)f();a=0}}}const A=void 0;function O(e,t={}){const n=!1!==t.location,r=C(e),o=()=>r.index(),a=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},s=a(),c=o(),l={currentType:14,offset:c,startLoc:s,endLoc:s,lastType:14,lastOffset:c,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},u=()=>l,{onError:i}=t;function f(e,t,r){e.endLoc=a(),e.currentType=t;const o={type:t};return n&&(o.loc=b(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const m=e=>f(e,14);function p(e,t){return e.currentChar()===t?(e.next(),t):(a(),"")}function d(e){let t="";for(;e.currentPeek()===E||e.currentPeek()===L;)t+=e.currentPeek(),e.peek();return t}function _(e){const t=d(e);return e.skipToPeek(),t}function k(e){if(e===A)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function h(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=function(e){if(e===A)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function g(e){d(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function T(e,t=!0){const n=(t=!1,r="",o=!1)=>{const a=e.currentPeek();return"{"===a?"%"!==r&&t:"@"!==a&&a?"%"===a?(e.peek(),n(t,"%",!0)):"|"===a?!("%"!==r&&!o)||!(r===E||r===L):a===E?(e.peek(),n(!0,E,o)):a!==L||(e.peek(),n(!0,L,o)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function y(e,t){const n=e.currentChar();return n===A?A:t(n)?(e.next(),n):null}function N(e){return y(e,(e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}))}function O(e){return y(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57}))}function x(e){return y(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}))}function v(e){let t="",n="";for(;t=O(e);)n+=t;return n}function I(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!T(e))break;t+=n,e.next()}else if(n===E||n===L)if(T(e))t+=n,e.next();else{if(g(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function S(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return F(e,t,4);case"U":return F(e,t,6);default:return a(),""}}function F(e,t,n){p(e,t);let r="";for(let t=0;t<n;t++){const t=x(e);if(!t){a(),e.currentChar();break}r+=t}return`\\${t}${r}`}function P(e){_(e);const t=p(e,"|");return _(e),t}function D(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&a(),e.next(),n=f(t,2,"{"),_(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&a(),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&_(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&a(),n=M(e,t)||m(t),t.braceNest=0,n;default:let r=!0,o=!0,s=!0;if(g(e))return t.braceNest>0&&a(),n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return a(),t.braceNest=0,w(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=k(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){_(e);let t="",n="";for(;t=N(e);)n+=t;return e.currentChar()===A&&a(),n}(e)),_(e),n;if(o=h(e,t))return n=f(t,6,function(e){_(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${v(e)}`):t+=v(e),e.currentChar()===A&&a(),t}(e)),_(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){_(e),p(e,"'");let t="",n="";const r=e=>"'"!==e&&e!==L;for(;t=y(e,r);)n+="\\"===t?S(e):t;const o=e.currentChar();return o===L||o===A?(a(),o===L&&(e.next(),p(e,"'")),n):(p(e,"'"),n)}(e)),_(e),n;if(!r&&!o&&!s)return n=f(t,13,function(e){_(e);let t="",n="";const r=e=>"{"!==e&&"}"!==e&&e!==E&&e!==L;for(;t=y(e,r);)n+=t;return n}(e)),a(),n.value,_(e),n}return n}function M(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||o!==L&&o!==E||a(),o){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return _(e),e.next(),f(t,9,".");case":":return _(e),e.next(),f(t,10,":");default:return g(e)?(r=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;d(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;d(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(_(e),M(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;d(e);const r=k(e.currentPeek());return e.resetPeek(),r}(e,t)?(_(e),f(t,12,function(e){let t="",n="";for(;t=N(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?k(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===E||!t)&&(t===L?(e.peek(),r()):k(t))},o=r();return e.resetPeek(),o}(e,t)?(_(e),"{"===o?D(e,t)||r:f(t,11,function(e){const t=(n=!1,r)=>{const o=e.currentChar();return"{"!==o&&"%"!==o&&"@"!==o&&"|"!==o&&o?o===E?r:o===L?(r+=o,e.next(),t(n,r)):(r+=o,e.next(),t(!0,r)):r};return t(!1,"")}(e))):(8===n&&a(),t.braceNest=0,t.inLinked=!1,w(e,t))}}function w(e,t){let n={type:14};if(t.braceNest>0)return D(e,t)||m(t);if(t.inLinked)return M(e,t)||m(t);switch(e.currentChar()){case"{":return D(e,t)||m(t);case"}":return a(),e.next(),f(t,3,"}");case"@":return M(e,t)||m(t);default:if(g(e))return n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:o}=function(e){const t=d(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return o?f(t,0,I(e)):f(t,4,function(e){_(e);return"%"!==e.currentChar()&&a(),e.next(),"%"}(e));if(T(e))return f(t,0,I(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=l;return l.lastType=e,l.lastOffset=t,l.lastStartLoc=n,l.lastEndLoc=s,l.offset=o(),l.startLoc=a(),r.currentChar()===A?f(l,14):w(r,l)},currentOffset:o,currentPosition:a,context:u}}const x=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function v(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function I(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const o={type:e,start:n,end:n};return t&&(o.loc={start:r,end:r}),o}function o(e,n,r,o){e.end=n,o&&(e.type=o),t&&e.loc&&(e.loc.end=r)}function a(e,t){const n=e.context(),a=r(3,n.offset,n.startLoc);return a.value=t,o(a,e.currentOffset(),e.currentPosition()),a}function s(e,t){const n=e.context(),{lastOffset:a,lastStartLoc:s}=n,c=r(5,a,s);return c.index=parseInt(t,10),e.nextToken(),o(c,e.currentOffset(),e.currentPosition()),c}function l(e,t){const n=e.context(),{lastOffset:a,lastStartLoc:s}=n,c=r(4,a,s);return c.key=t,e.nextToken(),o(c,e.currentOffset(),e.currentPosition()),c}function u(e,t){const n=e.context(),{lastOffset:a,lastStartLoc:s}=n,c=r(9,a,s);return c.value=t.replace(x,v),e.nextToken(),o(c,e.currentOffset(),e.currentPosition()),c}function i(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let a=e.nextToken();if(9===a.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:a,lastStartLoc:s}=n,c=r(8,a,s);return 12!==t.type?(n.lastStartLoc,c.value="",o(c,a,s),{nextConsumeToken:t,node:c}):(null==t.value&&(n.lastStartLoc,S(t)),c.value=t.value||"",o(c,e.currentOffset(),e.currentPosition()),{node:c})}(e);n.modifier=t.node,a=t.nextConsumeToken||e.nextToken()}switch(10!==a.type&&(t.lastStartLoc,S(a)),a=e.nextToken(),2===a.type&&(a=e.nextToken()),a.type){case 11:null==a.value&&(t.lastStartLoc,S(a)),n.key=function(e,t){const n=e.context(),a=r(7,n.offset,n.startLoc);return a.value=t,o(a,e.currentOffset(),e.currentPosition()),a}(e,a.value||"");break;case 5:null==a.value&&(t.lastStartLoc,S(a)),n.key=l(e,a.value||"");break;case 6:null==a.value&&(t.lastStartLoc,S(a)),n.key=s(e,a.value||"");break;case 7:null==a.value&&(t.lastStartLoc,S(a)),n.key=u(e,a.value||"");break;default:t.lastStartLoc;const c=e.context(),i=r(7,c.offset,c.startLoc);return i.value="",o(i,c.offset,c.startLoc),n.key=i,o(n,c.offset,c.startLoc),{nextConsumeToken:a,node:n}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let c=null;do{const r=c||e.nextToken();switch(c=null,r.type){case 0:null==r.value&&(t.lastStartLoc,S(r)),n.items.push(a(e,r.value||""));break;case 6:null==r.value&&(t.lastStartLoc,S(r)),n.items.push(s(e,r.value||""));break;case 5:null==r.value&&(t.lastStartLoc,S(r)),n.items.push(l(e,r.value||""));break;case 7:null==r.value&&(t.lastStartLoc,S(r)),n.items.push(u(e,r.value||""));break;case 8:const o=i(e);n.items.push(o.node),c=o.nextConsumeToken||null}}while(14!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function m(e){const t=e.context(),{offset:n,startLoc:a}=t,s=f(e);return 14===t.currentType?s:function(e,t,n,a){const s=e.context();let c=0===a.items.length;const l=r(1,t,n);l.cases=[],l.cases.push(a);do{const t=f(e);c||(c=0===t.items.length),l.cases.push(t)}while(14!==s.currentType);return o(l,e.currentOffset(),e.currentPosition()),l}(e,n,a,s)}return{parse:function(n){const a=O(n,c({},e)),s=a.context(),l=r(0,s.offset,s.startLoc);return t&&l.loc&&(l.loc.source=n),l.body=m(a),14!==s.currentType&&(s.lastStartLoc,n[s.offset]),o(l,a.currentOffset(),a.currentPosition()),l}}}function S(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function F(e,t){for(let n=0;n<e.length;n++)P(e[n],t)}function P(e,t){switch(e.type){case 1:F(e.cases,t),t.helper("plural");break;case 2:F(e.items,t);break;case 6:P(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function D(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&P(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function M(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?M(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(M(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let n=0;n<o&&(M(e,t.items[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),M(e,t.key),t.modifier?(e.push(", "),M(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function w(e,t={}){const n=c({},t),r=I(n).parse(e);return D(r,n),((e,t={})=>{const n=f(t.mode)?t.mode:"normal",r=f(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,a=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,c=e.helpers||[],l=function(e,t){const{sourceMap:n,filename:r,breakLineCode:o,needIndent:a}=t,s={source:e.loc.source,filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:a,indentLevel:0};function c(e,t){s.code+=e}function l(e,t=!0){const n=t?o:"";c(a?n+"  ".repeat(e):n)}return{context:()=>s,push:c,indent:function(e=!0){const t=++s.indentLevel;e&&l(t)},deindent:function(e=!0){const t=--s.indentLevel;e&&l(t)},newline:function(){l(s.indentLevel)},helper:e=>`_${e}`,needIndent:()=>s.needIndent}}(e,{mode:n,filename:r,sourceMap:o,breakLineCode:a,needIndent:s});l.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(s),c.length>0&&(l.push(`const { ${c.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),l.newline()),l.push("return "),M(l,e),l.deindent(s),l.push("}");const{code:u,map:i}=l.context();return{ast:e,code:u,map:i?i.toJSON():void 0}})(r,n)}const R=[];R[0]={w:[0],i:[3,0],"[":[4],o:[7]},R[1]={w:[1],".":[2],"[":[4],o:[7]},R[2]={w:[2],i:[3,0],0:[3,0]},R[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},R[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},R[5]={"'":[4,0],o:8,l:[5,0]},R[6]={'"':[4,0],o:8,l:[6,0]};const W=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function U(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function $(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,W.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}function V(e){const t=[];let n,r,o,a,s,c,l,u=-1,i=0,f=0;const m=[];function p(){const t=e[u+1];if(5===i&&"'"===t||6===i&&'"'===t)return u++,o="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=o:r+=o},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,i=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=$(r),!1===r)return!1;m[1]()}};null!==i;)if(u++,n=e[u],"\\"!==n||!p()){if(a=U(n),l=R[i],s=l[a]||l.l||8,8===s)return;if(i=s[0],void 0!==s[1]&&(c=m[s[1]],c&&(o=n,!1===c())))return;if(7===i)return t}}const K=new Map;function j(e,t){return p(e)?e[t]:null}const G=e=>e,B=e=>"",H="text",Y=e=>0===e.length?"":e.join(""),X=e=>null==e?"":u(e)||k(e)&&e.toString===d?JSON.stringify(e,null,2):String(e);function z(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function J(e={}){const t=e.locale,n=function(e){const t=r(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(r(e.named.count)||r(e.named.n))?r(e.named.count)?e.named.count:r(e.named.n)?e.named.n:t:t}(e),o=p(e.pluralRules)&&f(t)&&i(e.pluralRules[t])?e.pluralRules[t]:z,a=p(e.pluralRules)&&f(t)&&i(e.pluralRules[t])?z:void 0,s=e.list||[],c=e.named||{};r(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,c);function l(t){const n=i(e.messages)?e.messages(t):!!p(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):B)}const m=k(e.processor)&&i(e.processor.normalize)?e.processor.normalize:Y,d=k(e.processor)&&i(e.processor.interpolate)?e.processor.interpolate:X,_={list:e=>s[e],named:e=>c[e],plural:e=>e[o(n,e.length,a)],linked:(t,...n)=>{const[r,o]=n;let a="text",s="";1===n.length?p(r)?(s=r.modifier||s,a=r.type||a):f(r)&&(s=r||s):2===n.length&&(f(r)&&(s=r||s),f(o)&&(a=o||a));let c=l(t)(_);return"vnode"===a&&u(c)&&s&&(c=c[0]),s?(i=s,e.modifiers?e.modifiers[i]:G)(c,a):c;var i},message:l,type:k(e.processor)&&f(e.processor.type)?e.processor.type:H,interpolate:d,normalize:m};return _}const Z="i18n:init";let Q=null;const q=ee("function:translate");function ee(e){return t=>Q&&Q.emit(e,t)}const te={NOT_FOUND_KEY:1,FALLBACK_TO_TRANSLATE:2,CANNOT_FORMAT_NUMBER:3,FALLBACK_TO_NUMBER_FORMAT:4,CANNOT_FORMAT_DATE:5,FALLBACK_TO_DATE_FORMAT:6,__EXTEND_POINT__:7},ne={[te.NOT_FOUND_KEY]:"Not found '{key}' key in '{locale}' locale messages.",[te.FALLBACK_TO_TRANSLATE]:"Fall back to translate '{key}' key with '{target}' locale.",[te.CANNOT_FORMAT_NUMBER]:"Cannot format a number value due to not supported Intl.NumberFormat.",[te.FALLBACK_TO_NUMBER_FORMAT]:"Fall back to number format '{key}' key with '{target}' locale.",[te.CANNOT_FORMAT_DATE]:"Cannot format a date value due to not supported Intl.DateTimeFormat.",[te.FALLBACK_TO_DATE_FORMAT]:"Fall back to datetime format '{key}' key with '{target}' locale."};function re(e,t,n){return[...new Set([n,...u(t)?t:p(t)?Object.keys(t):f(t)?[t]:[n]])]}function oe(e,t,n){let r=!0;for(let o=0;o<t.length&&m(r);o++){const a=t[o];f(a)&&(r=ae(e,t[o],n))}return r}function ae(e,t,n){let r;const o=t.split("-");do{r=se(e,o.join("-"),n),o.splice(-1,1)}while(o.length&&!0===r);return r}function se(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(u(n)||k(n))&&n[o]&&(r=n[o])}return r}const ce="9.2.2",le="en-US",ue=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let ie,fe,me;let pe=null;let de=null;let _e=0;function ke(e,t,n,r,o){const{missing:a,onWarn:s}=e;if(null!==a){const r=a(e,n,t,o);return f(r)?r:t}return t}const he=e=>e;let ge=Object.create(null);let Te=h.__EXTEND_POINT__;const be=()=>++Te,Ee={INVALID_ARGUMENT:Te,INVALID_DATE_ARGUMENT:be(),INVALID_ISO_DATE_ARGUMENT:be(),__EXTEND_POINT__:be()};const Le=()=>"",ye=e=>i(e);function Ne(e,t,n,r,o,a){const{messages:s,onWarn:c,messageResolver:l,localeFallbacker:u}=e,m=u(e,r,n);let p,d={},_=null;for(let n=0;n<m.length&&(p=m[n],d=s[p]||{},null===(_=l(d,t))&&(_=d[t]),!f(_)&&!i(_));n++){const n=ke(e,t,p,0,"translate");n!==t&&(_=n)}return[_,p,d]}function Ce(e,t,r,o,a,s){const{messageCompiler:c,warnHtmlMessage:l}=e;if(ye(o)){const e=o;return e.locale=e.locale||r,e.key=e.key||t,e}if(null==c){const e=()=>o;return e.locale=r,e.key=t,e}const u=c(o,function(e,t,r,o,a,s){return{warnHtmlMessage:a,onError:e=>{throw s&&s(e),e},onCacheKey:e=>((e,t,r)=>n({l:e,k:t,s:r}))(t,r,e)}}(0,r,a,0,l,s));return u.locale=r,u.key=t,u.source=o,u}function Ae(...e){const[t,n,o]=e,s={};if(!f(t)&&!r(t)&&!ye(t))throw Error(Ee.INVALID_ARGUMENT);const l=r(t)?String(t):(ye(t),t);return r(n)?s.plural=n:f(n)?s.default=n:k(n)&&!a(n)?s.named=n:u(n)&&(s.list=n),r(o)?s.plural=o:f(o)?s.default=o:k(o)&&c(s,o),[l,s]}const Oe=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function xe(...e){const[t,n,o,a]=e,s={};let c,l={};if(f(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(Ee.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();c=new Date(n);try{c.toISOString()}catch(e){throw Error(Ee.INVALID_ISO_DATE_ARGUMENT)}}else if("[object Date]"===_(t)){if(isNaN(t.getTime()))throw Error(Ee.INVALID_DATE_ARGUMENT);c=t}else{if(!r(t))throw Error(Ee.INVALID_ARGUMENT);c=t}return f(n)?s.key=n:k(n)&&Object.keys(n).forEach((e=>{Oe.includes(e)?l[e]=n[e]:s[e]=n[e]})),f(o)?s.locale=o:k(o)&&(l=o),k(a)&&(l=a),[s.key||"",c,s,l]}const ve=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Ie(...e){const[t,n,o,a]=e,s={};let c={};if(!r(t))throw Error(Ee.INVALID_ARGUMENT);const l=t;return f(n)?s.key=n:k(n)&&Object.keys(n).forEach((e=>{ve.includes(e)?c[e]=n[e]:s[e]=n[e]})),f(o)?s.locale=o:k(o)&&(c=o),k(a)&&(c=a),[s.key||"",l,s,c]}return e.CompileErrorCodes=h,e.CoreErrorCodes=Ee,e.CoreWarnCodes=te,e.DATETIME_FORMAT_OPTIONS_KEYS=Oe,e.DEFAULT_LOCALE=le,e.DEFAULT_MESSAGE_DATA_TYPE=H,e.MISSING_RESOLVE_VALUE="",e.NOT_REOSLVED=-1,e.NUMBER_FORMAT_OPTIONS_KEYS=ve,e.VERSION=ce,e.clearCompileCache=function(){ge=Object.create(null)},e.clearDateTimeFormat=function(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__datetimeFormatters.has(n)&&r.__datetimeFormatters.delete(n)}},e.clearNumberFormat=function(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__numberFormatters.has(n)&&r.__numberFormatters.delete(n)}},e.compileToFunction=function(e,t={}){{const n=(t.onCacheKey||he)(e),r=ge[n];if(r)return r;let o=!1;const a=t.onError||T;t.onError=e=>{o=!0,a(e)};const{code:s}=w(e,t),c=new Function(`return ${s}`)();return o?c:ge[n]=c}},e.createCompileError=g,e.createCoreContext=function(e={}){const t=f(e.version)?e.version:ce,n=f(e.locale)?e.locale:le,r=u(e.fallbackLocale)||k(e.fallbackLocale)||f(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,a=k(e.messages)?e.messages:{[n]:{}},l=k(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},d=k(e.numberFormats)?e.numberFormats:{[n]:{}},_=c({},e.modifiers||{},{upper:(e,t)=>"text"===t&&f(e)?e.toUpperCase():"vnode"===t&&p(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&f(e)?e.toLowerCase():"vnode"===t&&p(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&f(e)?ue(e):"vnode"===t&&p(e)&&"__v_isVNode"in e?ue(e.children):e}),h=e.pluralRules||{},g=i(e.missing)?e.missing:null,T=!m(e.missingWarn)&&!o(e.missingWarn)||e.missingWarn,b=!m(e.fallbackWarn)&&!o(e.fallbackWarn)||e.fallbackWarn,E=!!e.fallbackFormat,L=!!e.unresolving,y=i(e.postTranslation)?e.postTranslation:null,N=k(e.processor)?e.processor:null,C=!m(e.warnHtmlMessage)||e.warnHtmlMessage,A=!!e.escapeParameter,O=i(e.messageCompiler)?e.messageCompiler:ie,x=i(e.messageResolver)?e.messageResolver:fe||j,v=i(e.localeFallbacker)?e.localeFallbacker:me||re,I=p(e.fallbackContext)?e.fallbackContext:void 0,S=i(e.onWarn)?e.onWarn:s,F=e,P=p(F.__datetimeFormatters)?F.__datetimeFormatters:new Map,D=p(F.__numberFormatters)?F.__numberFormatters:new Map,M=p(F.__meta)?F.__meta:{};_e++;const w={version:t,cid:_e,locale:n,fallbackLocale:r,messages:a,modifiers:_,pluralRules:h,missing:g,missingWarn:T,fallbackWarn:b,fallbackFormat:E,unresolving:L,postTranslation:y,processor:N,warnHtmlMessage:C,escapeParameter:A,messageCompiler:O,messageResolver:x,localeFallbacker:v,fallbackContext:I,onWarn:S,__meta:M};return w.datetimeFormats=l,w.numberFormats=d,w.__datetimeFormatters=P,w.__numberFormatters=D,w},e.createCoreError=function(e){return g(e,null,void 0)},e.createMessageContext=J,e.datetime=function(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:o,onWarn:s,localeFallbacker:l}=e,{__datetimeFormatters:u}=e,[i,p,d,_]=xe(...t);m(d.missingWarn)?d.missingWarn:e.missingWarn,m(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn;const h=!!d.part,g=f(d.locale)?d.locale:e.locale,T=l(e,o,g);if(!f(i)||""===i)return new Intl.DateTimeFormat(g,_).format(p);let b,E={},L=null;for(let t=0;t<T.length&&(b=T[t],E=n[b]||{},L=E[i],!k(L));t++)ke(e,i,b,0,"datetime format");if(!k(L)||!f(b))return r?-1:i;let y=`${b}__${i}`;a(_)||(y=`${y}__${JSON.stringify(_)}`);let N=u.get(y);return N||(N=new Intl.DateTimeFormat(b,c({},L,_)),u.set(y,N)),h?N.formatToParts(p):N.format(p)},e.fallbackWithLocaleChain=function(e,t,n){const r=f(n)?n:le,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let a=o.__localeChainCache.get(r);if(!a){a=[];let e=[n];for(;u(e);)e=oe(a,e,t);const s=u(t)||!k(t)?t:t.default?t.default:null;e=f(s)?[s]:s,u(e)&&oe(a,e,!1),o.__localeChainCache.set(r,a)}return a},e.fallbackWithSimple=re,e.getAdditionalMeta=()=>pe,e.getDevToolsHook=function(){return Q},e.getFallbackContext=()=>de,e.getWarnMessage=function(e,...n){return function(e,...n){return 1===n.length&&p(n[0])&&(n=n[0]),n&&n.hasOwnProperty||(n={}),e.replace(t,((e,t)=>n.hasOwnProperty(t)?n[t]:""))}(ne[e],...n)},e.handleMissing=ke,e.initI18nDevTools=function(e,t,n){Q&&Q.emit(Z,{timestamp:Date.now(),i18n:e,version:t,meta:n})},e.isMessageFunction=ye,e.isTranslateFallbackWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.isTranslateMissingWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.number=function(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:o,onWarn:s,localeFallbacker:l}=e,{__numberFormatters:u}=e,[i,p,d,_]=Ie(...t);m(d.missingWarn)?d.missingWarn:e.missingWarn,m(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn;const h=!!d.part,g=f(d.locale)?d.locale:e.locale,T=l(e,o,g);if(!f(i)||""===i)return new Intl.NumberFormat(g,_).format(p);let b,E={},L=null;for(let t=0;t<T.length&&(b=T[t],E=n[b]||{},L=E[i],!k(L));t++)ke(e,i,b,0,"number format");if(!k(L)||!f(b))return r?-1:i;let y=`${b}__${i}`;a(_)||(y=`${y}__${JSON.stringify(_)}`);let N=u.get(y);return N||(N=new Intl.NumberFormat(b,c({},L,_)),u.set(y,N)),h?N.formatToParts(p):N.format(p)},e.parse=V,e.parseDateTimeArgs=xe,e.parseNumberArgs=Ie,e.parseTranslateArgs=Ae,e.registerLocaleFallbacker=function(e){me=e},e.registerMessageCompiler=function(e){ie=e},e.registerMessageResolver=function(e){fe=e},e.resolveValue=function(e,t){if(!p(e))return null;let n=K.get(t);if(n||(n=V(t),n&&K.set(t,n)),!n)return null;const r=n.length;let o=e,a=0;for(;a<r;){const e=o[n[a]];if(void 0===e)return null;o=e,a++}return o},e.resolveWithKeyValue=j,e.setAdditionalMeta=e=>{pe=e},e.setDevToolsHook=function(e){Q=e},e.setFallbackContext=e=>{de=e},e.translate=function(e,...t){const{fallbackFormat:n,postTranslation:o,unresolving:a,messageCompiler:s,fallbackLocale:c,messages:i}=e,[d,_]=Ae(...t),k=m(_.missingWarn)?_.missingWarn:e.missingWarn,h=m(_.fallbackWarn)?_.fallbackWarn:e.fallbackWarn,g=m(_.escapeParameter)?_.escapeParameter:e.escapeParameter,T=!!_.resolvedMessage,b=f(_.default)||m(_.default)?m(_.default)?s?d:()=>d:_.default:n?s?d:()=>d:"",E=n||""!==b,L=f(_.locale)?_.locale:e.locale;g&&function(e){u(e.list)?e.list=e.list.map((e=>f(e)?l(e):e)):p(e.named)&&Object.keys(e.named).forEach((t=>{f(e.named[t])&&(e.named[t]=l(e.named[t]))}))}(_);let[y,N,C]=T?[d,L,i[L]||{}]:Ne(e,d,L,c,h,k),A=y,O=d;if(T||f(A)||ye(A)||E&&(A=b,O=A),!(T||(f(A)||ye(A))&&f(N)))return a?-1:d;let x=!1;const v=ye(A)?A:Ce(e,d,N,A,O,(()=>{x=!0}));if(x)return A;const I=function(e,t,n,o){const{modifiers:a,pluralRules:s,messageResolver:c,fallbackLocale:l,fallbackWarn:u,missingWarn:i,fallbackContext:m}=e,p=r=>{let o=c(n,r);if(null==o&&m){const[,,e]=Ne(m,r,t,l,u,i);o=c(e,r)}if(f(o)){let n=!1;const a=Ce(e,r,t,o,r,(()=>{n=!0}));return n?Le:a}return ye(o)?o:Le},d={locale:t,modifiers:a,pluralRules:s,messages:p};e.processor&&(d.processor=e.processor);o.list&&(d.list=o.list);o.named&&(d.named=o.named);r(o.plural)&&(d.pluralIndex=o.plural);return d}(e,N,C,_),S=function(e,t,n){return t(n)}(0,v,J(I));return o?o(S,d):S},e.translateDevTools=q,e.updateFallbackLocale=function(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)},Object.defineProperty(e,"__esModule",{value:!0}),e}({});
