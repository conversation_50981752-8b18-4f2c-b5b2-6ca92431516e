(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-mine-edit"],{1270:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"page-name"},[t._v(t._s(t.title))]),i("v-uni-view",{staticClass:"page-input"},[3===t.type?i("v-uni-input",{attrs:{type:"number"},model:{value:t.txt,callback:function(e){t.txt=e},expression:"txt"}}):t._e(),4===t.type?i("v-uni-picker",{attrs:{value:t.sexIn,range:t.sex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindPickerChange.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.sex[t.sexIn]))])],1):t._e(),5===t.type?i("v-uni-picker",{attrs:{value:t.nationalityIn,range:t.nationality},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindPickerChange2.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"uni-input"},[t._v(t._s(t.nationality[t.nationalityIn]))])],1):t._e(),6===t.type?i("v-uni-input",{attrs:{type:"number"},model:{value:t.txt,callback:function(e){t.txt=e},expression:"txt"}}):t._e(),7===t.type?i("v-uni-input",{attrs:{type:"text"},model:{value:t.txt,callback:function(e){t.txt=e},expression:"txt"}}):t._e()],1),i("v-uni-view",{staticClass:"page-tips"},[t._v(t._s(t.tips))]),i("v-uni-view",{staticClass:"page-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setDate.apply(void 0,arguments)}}},[t._v(t._s(t.$t("new.userSave")))])],1)},a=[]},"1b78":function(t,e,i){"use strict";i.r(e);var n=i("1270"),a=i("c1990");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);i("6dd2");var u=i("828b"),o=Object(u["a"])(a["default"],n["b"],n["c"],!1,null,"44196e6c",null,!1,n["a"],void 0);e["default"]=o.exports},"37c3":function(t,e,i){var n=i("5bdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("41986494",n,!0,{sourceMap:!1,shadowMode:!1})},"3de4":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e966");var n={data:function(){return{title:"",value:"",tips:"",type:0,userInfo:{},txt:"",par:{},sex:[],sexIn:2,nationality:[],nationalityIn:0}},onLoad:function(t){this.type=parseInt(t.type),this.sex=[this.$t("new.sex1"),this.$t("new.sex2"),this.$t("new.sex3")],this.nationality=this.$t("new.nationality").split("、")},onReady:function(){1===this.type&&(this.title=this.$t("new.userName"),this.value="bname",this.tips=this.$t("new.userTips1")),2===this.type&&(this.title=this.$t("new.userName2"),this.value="uname",this.tips=this.$t("new.userTips2")),3===this.type&&(this.title=this.$t("new.userName3"),this.value="birthday",this.tips=this.$t("new.userTips3")),4===this.type&&(this.title=this.$t("new.userName4"),this.value="sex",this.tips=this.$t("new.userTips4")),5===this.type&&(this.title=this.$t("new.userName5"),this.value="nationality",this.tips=this.$t("new.userTips5")),6===this.type&&(this.title=this.$t("new.userName6"),this.value="phone",this.tips=this.$t("new.userTips6")),7===this.type&&(this.title=this.$t("new.userName7"),this.value="email",this.tips=this.$t("new.userTips7")),uni.setNavigationBarTitle({title:this.title})},onShow:function(){this.getUserInfo()},methods:{bindPickerChange:function(t){this.sexIn=t.detail.value},bindPickerChange2:function(t){this.nationalityIn=t.detail.value},getUserInfo:function(){var t=this;this.$tools.Post("api/user/info",{api_token:uni.getStorageSync("token"),language:uni.getStorageSync("lang")}).then((function(e){200==e.status&&(t.userInfo=e.data,3===t.type&&(t.txt=t.userInfo.birthday),4===t.type&&(t.userInfo.sex===t.$t("new.sex1")&&(t.sexIn=0),t.userInfo.sex===t.$t("new.sex2")&&(t.sexIn=1),t.userInfo.sex===t.$t("new.sex3")&&(t.sexIn=2)),5===t.type&&(t.nationalityIn=t.userInfo.nationality),6===t.type&&(t.txt=t.userInfo.phone),7===t.type&&(t.txt=t.userInfo.email))}))},setDate:function(){3===this.type&&(this.par={uid:this.userInfo.uid,api_token:uni.getStorageSync("token"),birthday:this.txt,language:uni.getStorageSync("lang")}),4===this.type&&(this.par={uid:this.userInfo.uid,api_token:uni.getStorageSync("token"),sex:this.sex[this.sexIn],language:uni.getStorageSync("lang")}),5===this.type&&(this.par={uid:this.userInfo.uid,api_token:uni.getStorageSync("token"),nationality:this.nationalityIn,language:uni.getStorageSync("lang")}),6===this.type&&(this.par={uid:this.userInfo.uid,api_token:uni.getStorageSync("token"),phone:this.txt,language:uni.getStorageSync("lang")}),7===this.type&&(this.par={uid:this.userInfo.uid,api_token:uni.getStorageSync("token"),email:this.txt,language:uni.getStorageSync("lang")}),this.$tools.Post("api/user/changeinfo",this.par).then((function(t){200==t.status?(uni.showToast({title:t.msg,duration:1500,icon:"none"}),setTimeout((function(){uni.navigateTo({url:"/pages/mine/user"})}),2e3)):uni.showToast({title:t.msg,duration:1500,icon:"none"})}))}}};e.default=n},"5bdc":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,"uni-page-body[data-v-44196e6c]{background:#fff;padding:0 %?32?%}body.?%PAGE?%[data-v-44196e6c]{background:#fff}uni-page-body .page-name[data-v-44196e6c]{height:%?99?%;display:flex;align-items:center;font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#333}uni-page-body .page-input[data-v-44196e6c]{height:%?88?%;background:#ebeff5;border:%?1?% solid #172d52;border-radius:%?20?%;padding:0 %?22?%}uni-page-body .page-input uni-input[data-v-44196e6c]{height:%?88?%;line-height:%?88?%;font-size:%?32?%;font-family:PingFang SC;font-weight:500;color:#333;background:#ebeff5}uni-page-body .page-tips[data-v-44196e6c]{font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#999;margin-top:%?20?%}uni-page-body .page-btn[data-v-44196e6c]{height:%?100?%;background:#65b11d;border-radius:%?20?%;font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#fff;display:flex;align-items:center;justify-content:center;margin-top:%?58?%}uni-page-body[data-v-44196e6c]  .uni-input{height:%?85?%;background:#ebeff5}",""]),t.exports=e},"6dd2":function(t,e,i){"use strict";var n=i("37c3"),a=i.n(n);a.a},c1990:function(t,e,i){"use strict";i.r(e);var n=i("3de4"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a}}]);