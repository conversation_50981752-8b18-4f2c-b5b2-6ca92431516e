<?php

namespace App\Admin\Forms;

use Dcat\Admin\Widgets\Form;
use Illuminate\Http\Request;
use Dcat\Admin\Form as Fm;
use App\Models\UsersBillInfo as Bill;   //账单模型
use App\Models\UsersRecharge as Recharge;  //充值模型
use App\Models\UsersWithdraw as Withdraw;  //提现模型
use Symfony\Component\HttpFoundation\Response;
use Dcat\Admin\Traits\LazyWidget;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

use Illuminate\Support\Facades\DB;
class MemberAmountForms extends Form
{

    use LazyWidget;
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    // 处理表单提交请求
    public function handle(array $input)
    {


        $admin = Auth::guard('admin')->user(); 

        
        // dump($input);

        // return $this->response()->error('Your error message.');
        
        if($input['form_uname']==''){
            return $this->response()->error('查询失败，请先查找用户');
        }
        else{
            $where = [
                ['uname',$input['form_uname']]
            ];
            $users  = User::where($where)->first();
            $stauts = $input['status']; 
            $price  = $input['zprice'];
           

            /**处理账单 */
            $bill = new Bill();
            $bill->uid    = $input['uid']; 
            $bill->uname  = $input['form_uname']; 

            //操作方式 
            if($input['form_type']==1){ //人工加扣款  后台充值

                $bill->action = 1 ; 

            }
            else{  //彩金加扣款

                $bill->action = 2 ; 
            }

            $bill->direction = $stauts; //1=加钱+  2=扣钱 -
            $bill->price =  ($stauts==1) ? 0 + $price : 0 - $price;
            $bill->amount = $users->price ;  //未变更前金额 直接获取 本次充值前 users表的price字段
            $bill->action_user = $admin->username;
            $bill->msg  = '';
            $bill->save();
            /**处理账单 end*/


            /**处理充值 */

            if($stauts==1){ //判断若是充值则 新增充值记录

                $Recharge = new Recharge();
                
                $Recharge->uid    = $input['uid']; 
                $Recharge->uname  = $input['form_uname']; 
                $Recharge->price =  $price ;  //充值金额
                $Recharge->amount = $users->price ;  //未变更前金额 直接获取 本次充值前 users表的price字段
                //操作方式 
                if($input['form_type']==1){ //人工加扣款  后台充值

                    $Recharge->action = 1 ; 

                    //记录充值次数
                    $wh= [
                        ['uname',$input['form_uname']],
                        ['action','!=','2'],  //排除彩金  彩金不统计为充值
                    ];
                    $list = $Recharge->where($wh)->get();

                    $Recharge->num = count($list) + 1;  

                }
                else{  //彩金加扣款

                    $Recharge->action = 2 ; 
                    $Recharge->num = 0;  
                }
                $Recharge->action_user = $admin->username;

                $Recharge->msg = '';
                $Recharge->save(); 
            }

            /**充值end */


            /**更新用户主表金额 */

            $bill_sum = DB::table('users_bill_info')
            ->where($where)
            ->select(DB::raw('sum(price) as money_sum'))
            ->get();

            $countPrice = $bill_sum[0]->money_sum;

            $es = User::where($where)->update(['price' =>$countPrice]);

            if($es){
                return $this->response()->success('操作成功')->refresh();
            }
        
            else{
                return $this->response()->error('操作失败');
            }
        }
       
       
    }



    /**
     * Build a form here.
     */
    public function form()
    {


        $action = $this->payload['action'];
        $this->confirm('增减款确认', '确定执行操作吗?');
        $this->text('uname','会员账号')->disable();  //被定义为 disable的字段不会随着表单提交 所以要单独隐藏字段然后提交
        $this->text('bname','会员姓名')->disable();
        $this->text('price','账号余额')->disable();
        $this->hidden('uid')->value('');
        $this->hidden('form_uname')->value('');
        if($action==1){
            $this->radio('status','操作类型') ->options([
                1 => '人工加款',
                2 => '人工扣款',
            ])->default(1,true);//设置默认状态  $form->model()->status 获取字段值
            $this->hidden('form_type')->value(1);
        }
        
        if($action==2){
            $this->radio('status','操作类型') ->options([
                1 => '彩金加款',
                2 => '彩金扣款',
            ])->default(1,true);//设置默认状态  $form->model()->status 获取字段值
            $this->hidden('form_type')->value(2);
        }

        $this->text('zprice','金额')->required();
        
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        // $where = [
        //     ['uid','uid_111111']
        // ];

        // $rs = UsersDm::where($where)->first();
        // $us = $res = DB::table('users')->where($where)->first();


        // return [
        //     'price' =>     $us->price,
        //     'minprice' =>  $rs->outdm,
        //     'nowprice' =>  $rs->nowdm,
        // ];
    }
}
