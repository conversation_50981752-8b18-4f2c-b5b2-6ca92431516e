<template>
	<view class="skeleton-container">
		<view class="skeleton-top">
			<view class="skeleton-set shimmer"></view>
		</view>
		<view class="skeleton-box">
			<view class="skeleton-user">
				<view class="skeleton-avatar shimmer"></view>
				<view class="skeleton-info">
					<view class="skeleton-name-wrap">
						<view class="skeleton-name shimmer"></view>
					</view>
					<view class="skeleton-badges">
						<view class="skeleton-badge shimmer">
							<view class="skeleton-icon"></view>
						</view>
						<view class="skeleton-badge shimmer">
							<view class="skeleton-icon"></view>
						</view>
					</view>
				</view>
			</view>
			<view class="skeleton-stats">
				<view class="skeleton-stat">
					<view class="skeleton-stat-label shimmer"></view>
					<view class="skeleton-stat-value shimmer"></view>
				</view>
			</view>
		</view>
		<view class="skeleton-activity shimmer"></view>
		<view class="skeleton-menu">
			<view v-for="i in 6" :key="i" class="skeleton-menu-item shimmer">
				<view class="skeleton-menu-icon"></view>
				<view class="skeleton-menu-text"></view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'MineSkeleton'
}
</script>

<style lang="less" scoped>
.skeleton-container {
	width: 100%;
	min-height: 100vh;
	background-image: url("../../static/img/mine.png");
	background-position: top center;
	background-size: 100%;
	background-repeat: no-repeat;
	background-color: #F5F5F9;
	
	.skeleton-top {
		height: 103rpx;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding: 0 30rpx;
		
		.skeleton-set {
			width: 54rpx;
			height: 50rpx;
			border-radius: 8rpx;
			background: rgba(255, 255, 255, 0.1);
		}
	}
	
	.skeleton-box {
		margin: 0 36rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 20rpx;
		padding: 30rpx;
		backdrop-filter: blur(10px);
		
		.skeleton-user {
			display: flex;
			align-items: flex-start;
			padding-top: 20rpx;
			
			.skeleton-avatar {
				height: 140rpx;
				width: 140rpx;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.1);
				margin-right: 37rpx;
				flex-shrink: 0;
			}
			
			.skeleton-info {
				flex: 1;
				
				.skeleton-name-wrap {
					display: flex;
					justify-content: flex-start;
					margin-bottom: 20rpx;
					min-height: 56rpx;
					
					.skeleton-name {
						width: 240rpx;
						height: 56rpx;
						background: rgba(255, 255, 255, 0.1);
						border-radius: 8rpx;
					}
				}
				
				.skeleton-badges {
					display: flex;
					flex-direction: column;
					gap: 16rpx;
					
					.skeleton-badge {
						width: 280rpx;
						height: 48rpx;
						background: rgba(255, 255, 255, 0.1);
						border-radius: 24rpx;
						display: flex;
						align-items: center;
						padding: 0 20rpx;
						
						.skeleton-icon {
							width: 32rpx;
							height: 32rpx;
							border-radius: 50%;
							background: rgba(255, 255, 255, 0.2);
							margin-right: 12rpx;
						}
					}
				}
			}
		}
		
		.skeleton-stats {
			margin-top: 40rpx;
			padding: 20rpx 0;
			border-top: 2rpx solid rgba(255, 255, 255, 0.1);
			
			.skeleton-stat {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 12rpx;
				
				.skeleton-stat-label {
					width: 160rpx;
					height: 32rpx;
					background: rgba(255, 255, 255, 0.1);
					border-radius: 4rpx;
				}
				
				.skeleton-stat-value {
					width: 200rpx;
					height: 48rpx;
					background: rgba(255, 255, 255, 0.1);
					border-radius: 4rpx;
				}
			}
		}
	}
	
	.skeleton-activity {
		height: 80rpx;
		margin: 20rpx 36rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 12rpx;
	}
	
	.skeleton-menu {
		margin: 40rpx 36rpx;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;
		
		.skeleton-menu-item {
			height: 160rpx;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 20rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			gap: 16rpx;
			
			.skeleton-menu-icon {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.2);
			}
			
			.skeleton-menu-text {
				width: 120rpx;
				height: 32rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 4rpx;
			}
		}
	}
}

// 骨架屏动画
.shimmer {
	background: linear-gradient(
		90deg,
		rgba(255, 255, 255, 0.1) 25%,
		rgba(255, 255, 255, 0.2) 37%,
		rgba(255, 255, 255, 0.1) 63%
	);
	background-size: 400% 100%;
	animation: shimmer 1.4s ease infinite;
}

@keyframes shimmer {
	0% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0 50%;
	}
}
</style> 