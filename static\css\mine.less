page{
	background-image: url("../../static/img/mine.png");
	background-position: top center;
	background-size: 100%;
	background-repeat: no-repeat;
	background-color: #F5F5F9 !important;
	padding-bottom: 200rpx;
}
.top{
	height: 103rpx;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding: 0 30rpx;
	.top-set{
		width: 54rpx;
		height: 50rpx;
	}
}
.topBox{
	margin: 0 36rpx;
	.topBox-top{
		display: flex;
		align-items: center;
	}
	.topBox-head{
		height: 140rpx;
		width: 140rpx;
		border-radius: 50%;
		background: RGBA(237, 246, 255, 1);
		overflow: hidden;
		margin-right: 37rpx;
		image{
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	.topBox-det{
		display: flex;
		justify-content: center;
		flex-direction: column;
	}
	.topBox-name{
		font-size: 40rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #95E32E;
	}
	.topBox-con{
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	.topBox-vip{
		height: 64rpx;
		background: #3D67AD;
		border-radius: 32rpx;
		padding: 0 17rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFD800;
		display: flex;
		align-items: center;
		margin-right: 20rpx;
		margin-top: 20rpx;
		.vip{
			width: 56rpx;
			height: 42rpx;
			margin-right: 9rpx;
		}
		.hron{
			width: 66rpx;
			height: 44rpx;
			margin-right: 9rpx;
		}
	}
	.topBox-foot{
		height: 180rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 52rpx;
	}
	.topBox-item{
		width: 80%;
		height: 180rpx;
		border: 1rpx solid #7CBF31;
		border-radius: 30rpx;
		overflow: hidden;
		.topBox-item-top{
			height: 80rpx;
			background: #7CBF31;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
		}
		.topBox-item-foot{
			height: 100rpx;
			background: #3D67AD;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 40rpx;
			font-family: Euclid Circular A;
			font-weight: 500;
			color: #95E32E;
		}
	}
	.box1{
		background: #F8F8FE 0 0 no-repeat padding-box;
	}
	.box2{
		background: #FEFCEE 0 0 no-repeat padding-box;
	}
	.box3{
		background: #F1F8FE 0 0 no-repeat padding-box;
	}
}
.colorLine{
	height: 180rpx;
	background: url("../../static/img/mine7.png");
	background-repeat: no-repeat;
	background-size: 100% 100%;
	font-size: 36rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 55rpx 30rpx 0 30rpx;
	padding-left: 100rpx;
	box-sizing: border-box;
}
.menu{
	background: #FFFFFF;
	border-radius: 20rpx;
	margin: 30rpx 32rpx 0 32rpx;
	display: flex;
	flex-wrap: wrap;
	padding-top: 30rpx;
	.menu-list{
		width: calc(100% / 3);
		height: 240rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #1E3863;
		text-align: center;
	}
	.menu-icon{
		width: 114rpx;
		height: 114rpx;
		margin-bottom: 22rpx;
	}
}
.out{
	height: 100rpx;
	background: #C0DAA7;
	border-radius: 10rpx;
	font-size: 32rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: #5F9E26;
	margin: 30rpx 105rpx 0 105rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
