import Vue from 'vue'
import App from './App'
import * as filters from './static/js/utils/filters'
import Tools from './static/js/utils/Tools.js'
import Queue from './static/js/utils/queue.js'
import config_ from './static/js/utils/config.js'
import uView from "uview-ui";

Vue.use(uView);
Vue.config.productionTip = false

// 注册过滤器
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

// 挂载工具类
Vue.prototype.$tools = Tools
Vue.prototype.$queue = Queue
Vue.prototype.CONFIGAPI = config_
Vue.prototype.$url = Vue.prototype.$tools.config.apiUrl

// 数组类型判断
Vue.prototype.isArray = Array.isArray || function (obj) {
  return obj instanceof Array;
}

// 多语言配置
import VueI18n from 'vue-i18n'
Vue.use(VueI18n)

// 初始化语言设置
let lang = 'EN' // 恢复默认语言为大写
if(typeof uni !== 'undefined') {
  const storedLang = uni.getStorageSync('lang')
  if(storedLang){
    lang = storedLang
  } else{
    uni.setStorageSync('lang', lang)
  }
}

const i18n = new VueI18n({
  locale: lang, // 保持原始大小写
  messages: {
    'CN': require('./static/lang/cn.json'),    // 简体中文
    'TW': require('./static/lang/tw.json'),    // 繁体中文
    'EN': require('./static/lang/en.json'),    // 英语
    'VN': require('./static/lang/vn.json'),    // 越南语
    'IDN': require('./static/lang/yn.json'),   // 印尼语(使用IDN代码但加载yn.json文件)
    'PT': require('./static/lang/pt.json'),     // 葡萄牙语
    'ES': require('./static/lang/es.json'),     // 西班牙语
    'DE': require('./static/lang/de.json')      // 德语
  }
})

// 挂载i18n实例
Vue.prototype._i18n = i18n

// 全局错误处理
Vue.config.errorHandler = function(err, vm, info) {
  console.error('Vue错误:', err);
  console.error('错误信息:', info);
  
  // 向用户显示友好的错误提示
  if(typeof uni !== 'undefined') {
    uni.showToast({
      title: vm.$t('tool').errMsg,
      icon: 'none',
      duration: 2000
    });
  }
};

// 网络状态监听优化
if(typeof uni !== 'undefined') {
  let networkRetryTimer = null;
  
  uni.onNetworkStatusChange(function(res) {
    // 只在网络状态真正改变时处理
    if (!res.isConnected) {
      // 网络断开时，清除定时器
      if (networkRetryTimer) {
        clearTimeout(networkRetryTimer);
        networkRetryTimer = null;
      }
    } else {
      // 网络恢复时，延迟执行刷新
      if (networkRetryTimer) clearTimeout(networkRetryTimer);
      networkRetryTimer = setTimeout(() => {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.$vm) {
          // 静默刷新数据
          if (typeof currentPage.$vm.loadData === 'function') {
            currentPage.$vm.loadData();
          } else if (typeof currentPage.$vm.refreshData === 'function') {
            currentPage.$vm.refreshData();
          }
        }
      }, 1500);
    }
  });
}

// 添加会话管理
const SESSION_DURATION = 30 * 60 * 1000; // 会话有效期30分钟
const SESSION_CHECK_INTERVAL = 60 * 1000; // 检查间隔1分钟

// 会话管理
const sessionManager = {
    lastActiveTime: Date.now(),
    isRefreshing: false,
    
    // 更新活动时间
    updateActivity() {
        this.lastActiveTime = Date.now();
        uni.setStorageSync('lastActiveTime', this.lastActiveTime);
    },
    
    // 检查会话是否有效
    isSessionValid() {
        const now = Date.now();
        const lastActive = uni.getStorageSync('lastActiveTime') || this.lastActiveTime;
        return (now - lastActive) < SESSION_DURATION;
    },
    
    // 刷新会话
    refreshSession() {
        if (this.isRefreshing) return;
        this.isRefreshing = true;
        
        return new Promise((resolve, reject) => {
            const token = uni.getStorageSync('token');
            if (!token) {
                this.redirectToLogin();
                reject(new Error('No token'));
                return;
            }

            uni.request({
                url: Vue.prototype.$url + 'api/user/refresh',
                method: 'POST',
                data: {
                    api_token: token,
                    language: uni.getStorageSync('lang') || 'EN'
                },
                success: (res) => {
                    if (res.data.status === 200) {
                        uni.setStorageSync('token', res.data.data.token);
                        this.updateActivity();
                        resolve(true);
                    } else {
                        this.redirectToLogin();
                        reject(new Error('Refresh failed'));
                    }
                },
                fail: () => {
                    this.redirectToLogin();
                    reject(new Error('Refresh request failed'));
                },
                complete: () => {
                    this.isRefreshing = false;
                }
            });
        });
    },
    
    // 重定向到登录页
    redirectToLogin() {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const currentRoute = currentPage ? currentPage.route : '';
        
        // 如果当前不在登录页，则重定向
        if (currentRoute !== 'pages/public/login') {
            uni.reLaunch({
                url: '/pages/public/login'
            });
        }
    }
};

// 全局请求拦截器优化
Vue.prototype.$http = function(options) {
    // 检查会话状态
    if (!sessionManager.isSessionValid()) {
        return sessionManager.refreshSession().then(() => {
            // 会话刷新成功，重试请求
            return Vue.prototype.$http(options);
        }).catch(() => {
            // 会话刷新失败，已经重定向到登录页
            return Promise.reject(new Error('Session expired'));
        });
    }

    const token = uni.getStorageSync('token');
    const language = uni.getStorageSync('lang') || 'EN';
    
    options.header = {
        ...options.header,
        'Authorization': token ? `Bearer ${token}` : '',
        'Accept-Language': language,
        'Content-Type': 'application/json'
    };
    
    return new Promise((resolve, reject) => {
        uni.request({
            ...options,
            success: (res) => {
                // 更新活动时间
                sessionManager.updateActivity();
                
                if (res.data.status === 401) {
                    // 尝试刷新会话
                    sessionManager.refreshSession().then(() => {
                        // 重试当前请求
                        Vue.prototype.$http(options).then(resolve).catch(reject);
                    }).catch(() => {
                        reject(new Error('Authentication failed'));
                    });
                } else {
                    resolve(res);
                }
            },
            fail: (err) => {
                console.error('Request failed:', err);
                reject(err);
            }
        });
    });
};

// 启动定时检查会话状态
setInterval(() => {
    if (!sessionManager.isSessionValid() && !sessionManager.isRefreshing) {
        sessionManager.refreshSession().catch(() => {
            // 静默处理刷新失败
            console.log('Session refresh failed in background');
        });
    }
}, SESSION_CHECK_INTERVAL);

// 监听页面点击等用户活动
document.addEventListener('touchstart', () => {
    sessionManager.updateActivity();
});

document.addEventListener('mousemove', () => {
    sessionManager.updateActivity();
});

// 全局刷新定时器优化
let globalRefreshTimer; // 添加全局变量
let lastRefreshTime = Date.now();

Vue.prototype.globalRefresh = function(){
  if (globalRefreshTimer == undefined) {
    globalRefreshTimer = setInterval(() => {
      if(typeof uni !== 'undefined') {
        let arr = uni.getStorageSync('less');
        if(arr) {
          try {
            // 限制刷新频率，至少间隔5秒
            const now = Date.now();
            if (now - lastRefreshTime < 5000) {
              return;
            }
            lastRefreshTime = now;
            
            // 检查当前页面是否需要刷新
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            if (currentPage && currentPage.$vm && typeof currentPage.$vm.refreshData === 'function') {
              currentPage.$vm.refreshData();
            }
          } catch(e) {
            console.error('定时刷新错误:', e);
            // 发生错误时清理定时器
            if(globalRefreshTimer) {
              clearInterval(globalRefreshTimer);
              globalRefreshTimer = undefined;
            }
          }
        } else {
          // 如果不需要刷新，清理定时器
          if(globalRefreshTimer) {
            clearInterval(globalRefreshTimer);
            globalRefreshTimer = undefined;
          }
        }
      }
    }, 5000); // 增加间隔到5秒
  }
  
  // 清理定时器的方法
  return function clearGlobalRefresh() {
    if(globalRefreshTimer) {
      clearInterval(globalRefreshTimer);
      globalRefreshTimer = undefined;
    }
  }
};

// 配置小程序实例
App.mpType = 'app'

const app = new Vue({
  i18n,
  ...App
})

app.$mount()
