(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-public-register"],{"1cff":function(t,n,e){var a=e("c86c"),i=e("2ec5"),o=e("dcd8");n=a(!1);var s=i(o);n.push([t.i,"uni-page-body .loginWrap[data-v-5d308408]{background:url("+s+") no-repeat top;background-size:100% 100%;min-height:100vh}uni-page-body .loginWrap .loginWrap_cont_title[data-v-5d308408]{height:%?360?%;display:flex;align-items:center;justify-content:center}uni-page-body .loginWrap .loginWrap_cont_title uni-image[data-v-5d308408]{width:%?236?%;height:%?236?%}uni-page-body .loginWrap .cont_textCon_item[data-v-5d308408]{display:flex;margin:%?30?% %?55?% 0 %?55?%;flex-direction:column;justify-content:center;font-size:%?32?%;font-family:PingFang SC;font-weight:500;color:#fff}uni-page-body .loginWrap .cont_textCon_item .inputStyle[data-v-5d308408]{margin-top:%?11?%;height:%?88?%;background:#fff;border-radius:%?20?%;padding:0 %?19?%;border:0;font-size:%?32?%;font-family:PingFang SC;font-weight:500;color:#333}uni-page-body .loginWrap .cont_textCon_item .inpuyh[data-v-5d308408]{color:#b3b3b3}uni-page-body .loginWrap .loginWrap_cont_tip[data-v-5d308408]{padding:%?63?% %?65?% 0 %?65?%;font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#fff}uni-page-body .loginWrap .loginWrap_cont_tip span[data-v-5d308408]{color:#96e727}uni-page-body .loginWrap .cont_textCon_but[data-v-5d308408]{height:%?88?%;background:#65b11d;border-radius:%?20?%;font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#fff;display:flex;align-items:center;justify-content:center;margin:%?60?% %?55?% 0 %?55?%}uni-page-body[data-v-5d308408] :-moz-placeholder{\n  /* Mozilla Firefox 4 to 18 */color:#b3b3b3}uni-page-body[data-v-5d308408] ::-moz-placeholder{\n  /* Mozilla Firefox 19+ */color:#b3b3b3}uni-page-body uni-input[data-v-5d308408]:-ms-input-placeholder{color:#b3b3b3}uni-page-body uni-input[data-v-5d308408]::-webkit-input-placeholder{color:#b3b3b3}",""]),t.exports=n},"1d82":function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("23f4"),e("7d2f"),e("5c47"),e("9c4e"),e("ab80"),e("0506");var a={data:function(){return{formData:{username:"",password:"",new_password:"",staffId:"",bname:""}}},onLoad:function(){uni.setNavigationBarTitle({title:this.$t("regis.title")})},methods:{goLogin:function(){uni.navigateTo({url:"/pages/public/login"})},submit:function(){if(this.formData.username.length<6)return uni.showToast({title:this.$t("regis.tip1"),icon:"none"}),!1;var t=new RegExp(/[^\w\.\/]/gi);if(t.test(this.formData.username))return uni.showToast({title:this.$t("regis.tip2"),icon:"none"}),!1;if(0==this.formData.password.length)return uni.showToast({title:this.$t("regis.txt4"),icon:"none"}),!1;if(0==this.formData.new_password.length)return uni.showToast({title:this.$t("regis.txt5"),icon:"none"}),!1;if(this.formData.password!=this.formData.new_password)return uni.showToast({title:this.$t("regis.tip3"),icon:"none"}),!1;if(0==this.formData.staffId.length)return uni.showToast({title:this.$t("regis.txt1"),icon:"none"}),!1;var n={uname:this.formData.username,password:this.formData.password,code:this.formData.staffId,phone:"",language:uni.getStorageSync("lang")},e=this;this.$tools.Post("api/user/register",n).then((function(t){200==t.status?(uni.showToast({title:t.msg,duration:1500,icon:"none"}),setTimeout((function(){e.$tools.Get("api/user/login",{uname:e.formData.username,password:e.formData.password,language:uni.getStorageSync("lang")}).then((function(t){200===t.status?(uni.setStorageSync("loginInfo",{uname:e.formData.username,password:e.formData.password}),uni.setStorageSync("less",!0),uni.hideLoading(),uni.setStorage({key:"token",data:t.data,success:function(){uni.reLaunch({url:"../home/<USER>"})}})):uni.showToast({title:t.msg,duration:1500,icon:"none"})}))}),2e3)):uni.showToast({title:t.msg,duration:1500,icon:"none"})}))}}};n.default=a},"297d":function(t,n,e){var a=e("1cff");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=e("967d").default;i("6be2c4c4",a,!0,{sourceMap:!1,shadowMode:!1})},"2ec5":function(t,n,e){"use strict";t.exports=function(t,n){return n||(n={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),n.hash&&(t+=n.hash),/["'() \t\n]/.test(t)||n.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},3421:function(t,n,e){"use strict";var a=e("297d"),i=e.n(a);i.a},"4d38":function(t,n,e){"use strict";e.r(n);var a=e("54fb"),i=e("6f09");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("3421");var s=e("828b"),r=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"5d308408",null,!1,a["a"],void 0);n["default"]=r.exports},"54fb":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var a=function(){var t=this,n=t.$createElement,a=t._self._c||n;return a("v-uni-view",{staticClass:"loginWrap"},[a("v-uni-view",{staticClass:"loginWrap_cont_title"},[a("v-uni-image",{attrs:{src:e("7429")}})],1),a("v-uni-view",{staticClass:"cont_textCon_item"},[t._v(t._s(t.$t("regis.tit2"))),a("v-uni-input",{staticClass:"inputStyle",attrs:{type:"text","placeholder-class":"inpuyh",placeholder:t.$t("regis.txt2")},model:{value:t.formData.username,callback:function(n){t.$set(t.formData,"username",n)},expression:"formData.username"}})],1),a("v-uni-view",{staticClass:"cont_textCon_item"},[t._v(t._s(t.$t("regis.tit4"))),a("v-uni-input",{staticClass:"inputStyle",attrs:{type:"password","placeholder-class":"inpuyh",placeholder:t.$t("regis.txt4")},model:{value:t.formData.password,callback:function(n){t.$set(t.formData,"password",n)},expression:"formData.password"}})],1),a("v-uni-view",{staticClass:"cont_textCon_item"},[t._v(t._s(t.$t("regis.tit4-1"))),a("v-uni-input",{staticClass:"inputStyle",attrs:{type:"password","placeholder-class":"inpuyh",placeholder:t.$t("regis.txt5")},model:{value:t.formData.new_password,callback:function(n){t.$set(t.formData,"new_password",n)},expression:"formData.new_password"}})],1),a("v-uni-view",{staticClass:"cont_textCon_item"},[t._v(t._s(t.$t("regis.tit1"))),a("v-uni-input",{staticClass:"inputStyle",attrs:{type:"number",maxlength:"6","placeholder-class":"inpuyh",placeholder:t.$t("regis.txt1")},model:{value:t.formData.staffId,callback:function(n){t.$set(t.formData,"staffId",n)},expression:"formData.staffId"}})],1),a("v-uni-view",{staticClass:"cont_textCon_but",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.submit()}}},[t._v(t._s(t.$t("regis.btn")))]),a("v-uni-view",{staticClass:"loginWrap_cont_tip",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goLogin()}}},[t._v(t._s(t.$t("regis.txt6"))),a("span",[t._v(t._s(t.$t("regis.txt7")))])])],1)},i=[]},"6f09":function(t,n,e){"use strict";e.r(n);var a=e("1d82"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a},7429:function(t,n,e){t.exports=e.p+"static/img/logo.png"},dcd8:function(t,n,e){t.exports=e.p+"static/img/login.jpg"}}]);