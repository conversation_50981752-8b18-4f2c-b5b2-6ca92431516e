<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Http\Controllers\Tools;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\Hash;
use App\Models\UsersDm;
use App\Admin\Renderable\BankTable;
use App\Admin\Renderable\BillTable;
use App\Admin\Forms\MemberDmForms;
use App\Admin\Forms\MemberAmountForms;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Auth;
class UsersDemoController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new User(), function (Grid $grid) {

            Tools::viewroles($grid);
            $grid->withBorder();
            $grid->model()->where('user_type', '=', 2); //陪玩用户
            $grid->column('uname','账号');
       
            $grid->column('status','状态')->display(function () {
                if($this->status==1){
                    return '正常';
                }
                else if($this->status==2){
                    return '禁止登录';
                }
                else if($this->status==3){
                    return '禁止下单';
                }
                else{
                    return '未知';
                }
                
            });
            $grid->column('msg','系统备注');
            $grid->column('price');
            $grid->column('ip');
            $grid->enableDialogCreate(); //开启 弹窗新增

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                
                Tools::Toolsroles($actions);

                // append一个操作
                $bill = Modal::make()
                ->xl()
               
                ->title('账变记录信息')
                ->body(BillTable::make(['id' => $this->id,'uid' =>$this->uid ])) // Modal 组件支持直接传递 渲染类实例
                ->button('<i class="fa fa-file-text-o"></i>');
                $actions->append($bill);
        
                // prepend一个操作
                $bank = Modal::make()
                ->xl()
                ->title('银行卡信息')
                ->body(BankTable::make(['id' => $this->id,'uid' =>$this->uid ])) // Modal 组件支持直接传递 渲染类实例
                ->button('<i class="fa fa-credit-card" style="margin-right: 10px;"></i>');

                 $actions->prepend($bank);
            });
           

            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('uname','用户名')->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new User(), function (Show $show) {
            $show->field('id');
            $show->field('uname');
            $show->field('bname');
            $show->field('status');
            $show->field('password');
            $show->field('remember_token');
            $show->field('staff_id');
            $show->field('staff_code');
            $show->field('msg');
            $show->field('price');
            $show->field('ip');
            $show->field('phone');
            $show->field('online_status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new User(), function (Form $form) {

                $id = $form->getKey();
                $form->text('uname','账号')->required();
         
                $form->text('phone','手机号');
            
                if ($id) {
                    $form->password('password', trans('admin.password'))
                        ->minLength(5)
                        ->maxLength(20)
                        ->customFormat(function () {
                            return '';
                        });
                } else {
                    $form->password('password', trans('admin.password'))
                        ->required()
                        ->minLength(5)
                        ->maxLength(20);
                }

                $form->hidden('msg','备注');
                $form->radio('status','账号状态') ->options([
                    1 => '正常',
                    2 => '禁止登录',
                    3 => '禁止下单',
                ])->default($form->model()->status,true);//设置默认状态  $form->model()->status 获取字段值

              
    
                if($form->isCreating()){  //新增普通会员

                    $form->hidden('uid');
                    $form->hidden('staff_id');
                    $form->hidden('team_id');
                    $form->hidden('staff_code');
                    $form->hidden('bname');
                    $form->hidden('user_type');
                    $roles = Admin::user()->roles; //获取权限分组

                    $form->uid = 'uid_'.time();  //会员UID
                    $form->bname = '不可提款用户';
                    $form->user_type = 2; //陪玩用户
                    $admin  = Auth::guard('admin')->user(); 

                    if($roles[0]->slug == 'staff'){
                        $form->staff_code = Tools::staffCode($admin->id,1);//注意 只有员工角色有该邀请码 如果后期 团长，超级管理员也能新增普通用户的话需要更新这个
                    }
                  

                    $form->staff_id = $admin->id;
                    $form->team_id  = $admin->team_id;

                    $form->saved(function (Form $form) { //新增时候除了 注册用户 还需要初始化用户的 打码量表 
                        $username = $form->uname;
                        UsersDm::create(['uname'=> $username, 'uid'=>'uid_'.time()]);
                       
                    });

                }
                if($form->isEditing()){  //编辑

                    // $form->saving(function (Form $form) { //回调前

                    //     $form->password = Hash::make('123456');

                    // });

                }
           
   
        })->saving(function (Form $form) {
            if ($form->password && $form->model()->get('password') != $form->password) {
                $form->password = bcrypt($form->password);
            }

            if (! $form->password) {
                $form->deleteInput('password');
            }
        });
    }
}
