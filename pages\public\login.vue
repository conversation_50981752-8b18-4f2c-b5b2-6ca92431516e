<template>
    <view class="loginWrap">
        <view class="lang-switch" @tap="goLang">
            <text class="lang-icon">🌐</text>
            <text>{{currentLang}}</text>
        </view>
        <view class="loginWrap_cont_title"><image mode="aspectFit" src="../../static/img/logo.png" /></view>
        <view class="cont_textCon_item">
            <text>{{$t('login.tit1')}}</text>
            <input type="text"  v-model="username" class="inputStyle" placeholder-class="inpuyh" :placeholder="$t('login.txt1')" />
        </view>
        <view class="cont_textCon_item">
            <text>{{$t('login.tit2')}}</text>
            <input type="password"  v-model="password" class="inputStyle" placeholder-class="inpuyh" :placeholder="$t('login.txt2')" />
        </view>
        <view class="remember-password">
            <checkbox :checked="rememberMe" @tap="rememberMe = !rememberMe" />
            <text>Remember Password</text>
        </view>
        <view class="cont_textCon_but" @tap="signIn()">{{$t('login.btn')}}</view>
        <view class="loginWrap_cont_tip" @click="gotoRegister()">{{$t('login.txt3')}}<span>{{$t('login.txt4')}}</span></view>
    </view>

</template>

<script>
    export default {
        data() {
            return {
                username: "",
                password: "",
                headTitle:'',
                systemInfo:{},
                langage:'',
                rememberMe: false,
                currentLang: 'English',
                isLoading: false,
                loginTimeout: null
            }
        },
        onLoad() {
            // 预加载语言配置
            this.preloadLanguage();
            uni.setNavigationBarTitle({
                title: this.$t('login.title')
            });
        },
		mounted() {
			const username = uni.getStorageSync('username');
			      const password = uni.getStorageSync('password');
			      if (username && password) {
			        this.username = username;
			        this.password = password;
			        this.rememberMe = true;
			      } else {
			        this.username = '';
			        this.password = '';
			      }
		},
        methods: {
            // 预加载语言配置
            preloadLanguage() {
                try {
                    const systemLang = uni.getSystemInfoSync().language;
                    let defaultLang = 'EN';
                    
                    // 使用简单的映射对象代替多个if判断
                    const langMap = {
                        zh: 'CN',
                        es: 'ES',
                        pt: 'PT',
                        id: 'IDN',
                        vi: 'VN'
                    };
                    
                    // 获取语言代码的前两个字符
                    const langPrefix = systemLang.slice(0, 2).toLowerCase();
                    defaultLang = langMap[langPrefix] || 'EN';
                    
                    const storedLang = uni.getStorageSync('lang') || defaultLang;
                    uni.setStorageSync('lang', storedLang);
                    this.$i18n.locale = storedLang;
                    
                    const displayLangMap = {
                        'CN': '简体中文',
                        'EN': 'English',
                        'ES': 'Español',
                        'PT': 'Português',
                        'IDN': 'Bahasa Indonesia',
                        'VN': 'Tiếng Việt',
                        'TW': '繁體中文'
                    };
                    this.currentLang = displayLangMap[storedLang] || 'English';
                } catch (error) {
                    console.error('Language initialization error:', error);
                    // 使用默认语言作为后备
                    this.currentLang = 'English';
                }
            },
            goLang() {
                uni.navigateTo({
                    url: '/pages/public/lang'
                });
            },
            // 登录
            async signIn() {
                // 防止重复点击
                if(this.isLoading) return;
                
                // 表单验证
                if(!this.username) {
                    uni.showToast({
                        title: this.$t('login.txt1'),
                        duration: 1500,
                        icon: 'none'
                    });
                    return;
                }
                if(!this.password) {
                    uni.showToast({
                        title: this.$t('login.txt2'),
                        duration: 1500,
                        icon: 'none'
                    });
                    return;
                }

                this.isLoading = true;
                uni.showLoading({title: 'loading...'});

                // 设置登录超时
                this.loginTimeout = setTimeout(() => {
                    if(this.isLoading) {
                        this.isLoading = false;
                        uni.hideLoading();
                        uni.showToast({
                            title: this.$t('tool.errMsg'),
                            duration: 1500,
                            icon: 'none'
                        });
                    }
                }, 15000); // 15秒超时

                try {
                    const params = {
                        uname: this.username,
                        password: this.password,
                        language: uni.getStorageSync('lang')
                    };

                    const res = await this.$tools.Get("api/user/login", params);
                    
                    // 清除超时定时器
                    if(this.loginTimeout) {
                        clearTimeout(this.loginTimeout);
                        this.loginTimeout = null;
                    }

                    if(res.status === 200) {
                        // 保存登录信息
                        if (this.rememberMe) {
                            uni.setStorageSync('username', this.username);
                            uni.setStorageSync('password', this.password);
                        } else {
                            uni.removeStorageSync('username');
                            uni.removeStorageSync('password');
                        }
                        
                        // 设置token和登录状态
                        this.$tools.setLoginStatus(res.data, this.rememberMe);
                        uni.setStorageSync('loginInfo', params);
                        uni.setStorageSync('less', true);
                        
                        // 预加载首页数据
                        this.preloadHomeData();
                        
                        // 跳转到首页
                        uni.reLaunch({
                            url: '../home/<USER>'
                        });
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon: 'none'
                        });
                    }
                } catch (err) {
                    console.error('Login error:', err);
                    uni.showToast({
                        title: this.$t('tool.errMsg'),
                        duration: 1500,
                        icon: 'none'
                    });
                } finally {
                    this.isLoading = false;
                    uni.hideLoading();
                }
            },

            // 预加载首页数据
            async preloadHomeData() {
                try {
                    // 在后台预加载首页需要的基本数据
                    this.$tools.Get("api/user/info", {
                        api_token: uni.getStorageSync('token'),
                        language: uni.getStorageSync('lang')
                    });
                } catch (err) {
                    console.error('Preload home data error:', err);
                }
            },

            // 注册账号
            gotoRegister() {
                if(this.isLoading) return;
                uni.navigateTo({
                    url: '/pages/public/register'
                });
            }
        },
        // 组件销毁时清理资源
        beforeDestroy() {
            if(this.loginTimeout) {
                clearTimeout(this.loginTimeout);
                this.loginTimeout = null;
            }
        }
    }
</script>

<style scoped lang="less">
    page{
        .loginWrap{
            background: url(~@/static/img/login.jpg) no-repeat top center;
            background-size: 100% 100%;
            min-height: 100vh;
            .loginWrap_cont_title{
                height: 500rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                image{
                    width: 230rpx;
                    height: 230rpx;
                }
            }
            .cont_textCon_item{
                display: flex;
                margin: 50rpx 55rpx 0 55rpx;
                flex-direction: column;
                justify-content: center;
                font-size: 32rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
                .inputStyle{
                    margin-top: 11rpx;
                    height: 88rpx;
                    background: #FFFFFF;
                    border-radius: 20rpx;
                    padding: 0 19rpx;
                    border: 0;
                    font-size: 32rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    color: #333;
                }
                .inpuyh{
                    color: rgba(179, 179, 179, 1);
                }

            }
            .loginWrap_cont_tip{
                padding: 63rpx 65rpx 0 65rpx;
                font-size: 28rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
                span{
                    color: rgba(150, 231, 39, 1);
                }
            }
            .cont_textCon_but{
                height: 88rpx;
                background: #65B11D;
                border-radius: 20rpx;
                font-size: 32rpx;
                font-family: PingFang SC;
                font-weight: bold;
                color: #FFFFFF;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 60rpx 55rpx 0 55rpx;
            }
            .remember-password {
                margin: 20rpx 55rpx;
                display: flex;
                align-items: center;
                color: #FFFFFF;
                
                checkbox {
                    margin-right: 10rpx;
                    transform: scale(0.8);
                }
                
                text {
                    font-size: 28rpx;
                }
            }
            .lang-switch {
                position: absolute;
                top: 40rpx;
                right: 40rpx;
                display: flex;
                align-items: center;
                padding: 10rpx 20rpx;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 30rpx;
                z-index: 999;
                
                .lang-icon {
                    font-size: 32rpx;
                    margin-right: 10rpx;
                }
                
                text {
                    color: #FFFFFF;
                    font-size: 28rpx;
                }
            }
        }
        :-moz-placeholder { /* Mozilla Firefox 4 to 18 */
            color: rgba(179, 179, 179, 1);
        }
        ::-moz-placeholder {  /* Mozilla Firefox 19+ */
            color: rgba(179, 179, 179, 1);
        }
        input:-ms-input-placeholder{
            color: rgba(179, 179, 179, 1);
        }
        input::-webkit-input-placeholder{
            color: rgba(179, 179, 179, 1);
        }
    }
</style>
