<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\User;
use App\Models\UsersDm;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use App\Admin\Renderable\BankTable;
use App\Admin\Renderable\BillTable;
use Dcat\Admin\Widgets\Modal;
use App\Admin\Forms\MemberDmForms;
use App\Admin\Forms\MemberAmountForms;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;

class UserController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new User(), function (Grid $grid) {



            Tools::viewroles($grid);  //视图权限筛选
            $grid->model()->orderBy('id','desc'); //正式用户
            $grid->model()->where('user_type', '=', 1); //正式用户
            $grid->withBorder();
            $grid->column('uname','账号');
            $grid->column('bname','持卡人姓名');
     
            $grid->column('staff_id','上级')->display(function ($res) {

                $where = [
                    ['id',$this->staff_id]
                 ];
                $res = DB::table('admin_users')->where($where)->first();
                $name = ($res) ? $res->username : '暂无';
                return $name;
            });

            $grid->column('status','状态')->display(function () {
                if($this->status==1){
                    return '正常';
                }
                else if($this->status==2){
                    return '禁止登录';
                }
                else if($this->status==3){
                    return '禁止下单';
                }
                else{
                    return '未知';
                }
                
            });
          
            $grid->column('price','金额');
            $grid->column('credit','信用分')->editable(true);
            
            //开启行内 编辑成功后刷新页面  form表单也必须拥有 msg 字段
            $grid->column('msg','系统备注')->editable(true);
            $grid->column('ip','IP');
           
            $grid->enableDialogCreate(); //开启 弹窗新增

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();

                // append一个操作
                $bill = Modal::make()
                ->xl()
               
                ->title('账变记录信息')
                ->body(BillTable::make(['id' => $this->id,'uid' =>$this->uid ])) // Modal 组件支持直接传递 渲染类实例
                ->button('<i class="fa fa-file-text-o" style="margin-right: 10px;"></i>');
                $actions->prepend($bill);
        
                // prepend一个操作
                $bank = Modal::make()
                ->xl()
                ->title('银行卡信息')
                ->body(BankTable::make(['id' => $this->id,'uid' =>$this->uid ])) // Modal 组件支持直接传递 渲染类实例
                ->button('<i class="fa fa-credit-card" style="margin-right: 10px;"></i>');

                 $actions->prepend($bank);
            });
           

            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('uname','用户名')->width(3);
            });
        });
    }

   
    
    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new User(), function (Show $show) {
            $show->field('id');
            $show->field('uid');
            $show->field('uname');
            $show->field('bname');
            $show->field('status');
            $show->field('password');
            $show->field('staff_id');
            $show->field('staff_code');
            $show->field('created_at');
            $show->field('updated_at');
           
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new User(), function (Form $form) {


                    $form->hidden('credit');
                if($form->isCreating()){  //新增普通会员

                    $form->text('uname','账号')->required();
                   
                    
                    $form->hidden('uid');
                    $form->hidden('staff_id');
                    $form->hidden('team_id');
                    $form->hidden('staff_code');
                    $roles = Admin::user()->roles; //获取权限分组
                    $system = DB::table('system')->find(1);
                    $form->uid = 'uid_'.time();  //会员UID
                    $form->credit =$system->credit;   //默认信用分数
                    $admin  = Auth::guard('admin')->user(); 

                    if($roles[0]->slug == 'staff'){
                        $form->staff_code = Tools::staffCode($admin->id,1);//注意 只有员工角色有该邀请码 如果后期 团长，超级管理员也能新增普通用户的话需要更新这个
                    }
                  

                    $form->staff_id = $admin->id;
                    $form->team_id  = $admin->team_id;

                    $form->saved(function (Form $form) { //新增时候除了 注册用户 还需要初始化用户的 打码量表 
                        $username = $form->uname;
                        UsersDm::create(['uname'=> $username, 'uid'=>'uid_'.time()]);
                       
                    });

                }
                $form->text('bname','持卡人姓名')->required();
                $form->text('phone','手机号');
                $id = $form->getKey();
                
               
                if ($id) {
                    $form->password('password', trans('admin.password'))
                        ->minLength(5)
                        ->maxLength(20)
                        ->customFormat(function () {
                            return '';
                        });
                } else {
                    $form->password('password', trans('admin.password'))
                        ->required()
                        ->minLength(5)
                        ->maxLength(20);
                }

                $form->hidden('msg','备注');
                $form->radio('status','账号状态') ->options([
                    1 => '正常',
                    2 => '禁止登录',
                    3 => '禁止下单',
                ])->default($form->model()->status,true);//设置默认状态  $form->model()->status 获取字段值

                if($form->isEditing()){  //编辑

                    $form->text('uname','账号')->disable();
                }
       
   
        })->saving(function (Form $form) {
            if ($form->password && $form->model()->get('password') != $form->password) {
                $form->password =Hash::make($form->password);
            }

            if (!$form->password) {
                $form->deleteInput('password');
            }
        });
    }


    /**
     * 会员加减打码量
     *
     * @return Form
     */
    
    public function memberdm(Content $content){  //会员加减打码量

        $form = new Card(new MemberDmForms());
        return $content
        ->title('会员加减打码量')
        ->body(view('MemberDm').$form);
    }


       /**
     * 会员在线列表.
     *
     * @return Grid
     */
    public function online(Content $content)
    {
           $online_table = Grid::make(new User(), function (Grid $grid) {

            

          
            $grid->disableCreateButton();
            Tools::viewroles($grid);
            $grid->model()->where('api_token', '!=', null);
    
            $grid->withBorder();
            $grid->column('uname','账号');
            $grid->column('bname','持卡人姓名');

            // 编辑成功后刷新页面
            $grid->column('msg','备注');

          
            $grid->column('price','金额');
            
        
            $grid->column('ip','IP');
           
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->disableView();
             
                $ac = '<button uid='."'$this->uid'". ' onclick=onlineout("'.$this->uid.'")    class="btn btn-danger out_onlin">强制下线</button>';
                $actions->append($ac);
                // Admin::script(
                //     <<<JS

                //     Dcat.ready(function () {

                //         setTimeout(() => {
                //             onlineout();
                //         }, 1000);
                      
                //     });
         
                //     JS
                // );
                
            });
           
          
            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('uname','用户名')->width(3);

                // //需要刷一新一次页面点击强制下线时间才有用 所以进入表格页面就自动刷新一次
                // <<<JS
                //         Dcat.ready(function () {
                //             Dcat.reload();
                //         });
               
                // JS ;
            });
        });


        return $content
        ->title('在线会员列表')
        ->body($online_table);

    }


     /**
     * 会员加扣款 & 彩金
     *
     * @return Form
     */
    
     public function member_amount(Content $content,$action){  //会员加减打码量


        $title = ($action==1) ? '会员加扣款' : '彩金赠送';
       
        $form =  new Card(MemberAmountForms::make()->payload(['action' => $action]));
        return $content
        ->title($title)
        ->body(view('MemberAmount').$form);
    }

      /**
     * 强制会员下线
     *
     * @return Form
     */
    
     public function outline(Request $request){  

        $user = DB::table('users');
        $user->where('uid',$request->get('uid'))->update(['status' => 0,'api_token' => null]);

        return ['status'=>200,'msg'=>'下线成功!'];
            
    }

}
