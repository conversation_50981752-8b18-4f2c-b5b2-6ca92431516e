<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Product;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use usIlluminate\Http\Request;
use App\Http\Controllers\Tools;
use App\Models\Product as poject;
use Illuminate\Http\UploadedFile;
use App\Http\Controllers\GeneratorController;

class ProductController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Product(), function (Grid $grid) {

            $grid->withBorder();
        
         
            $grid->column('id')->sortable();
            $grid->column('byid');
            $grid->column('images')->display(function () {
              
                return '<img src="'.env('APP_URL').'/upload'.'/'.$this->images.'" style="width: 50px;"';
                
            });
            $grid->column('pname');
            $grid->column('class');
            $grid->column('desc');
           
    
            $grid->column('qishu')->display(function () {
              
                $qishu = 60*60*24 / $this->period;
                return $qishu;
                
            });
            $grid->column('created_at');
          
            $grid->enableDialogCreate(); //开启 弹窗新增

            $grid->actions(function (Grid\Displayers\Actions $actions) {

                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();

            });

            $grid->filter(function (Grid\Filter $filter) {
                                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('pname','产品名')->width(3);
        
            });

   
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Product(), function (Show $show) {
            $show->field('id');
            $show->field('pid');
            $show->field('byid');
            $show->field('pname');
            $show->field('class');
            $show->field('desc');
            $show->field('status');
            $show->field('period');
            $show->field('images');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Product(), function (Form $form) {

         
            $form->text('byid')->type('number');
            $form->text('pname')->required();
            $form->keyValue('language','产品名翻译')->default(Tools::product_language())->setKeyLabel('语言')->setValueLabel('翻译')->required();
            $form->text('pname_abb')->required();
           
            $form->text('desc')->required();

            $form->text('period')->required()->type('number')->rules(function (Form $form) {

                if ($form->period < 60) {
                    return '每期不能小于60秒';
                }
                else if($form->period%60 != 0){
                    return '只能是60秒的倍数';
                }
                else{

                }
            
            });

            $form->text('closed_time')->required()->type('number')->rules(function (Form $form) {

                if ($form->period < $form->closed_time) {
                    return '封单时间不能大于每期时间';
                }
               
            });



            $form->image('images')->name(function ($file) {
                return 'test.'.$file->guessExtension();
            });
          
            $form->hidden('pid');
            if($form->isCreating()){  //新增
                $form->pid= 'pid_'.time();

                
                $form->saved(function (Form $form) { //保存完毕后的回调

                    $obj =  poject::where('pid',$form->pid)->first();

                    $gener = new GeneratorController();
                    
                    $gener->run_gener($obj);
                
                });

            }
            
            $form->saving(function (Form $form) { //保存之前的回调
    
            });
    

            // $form->submitted(function (Form $form) {
            //     if ($form->period < 60) { // 你的验证逻辑

            //         $form->responseValidationMessages('title', 'title格式错误');
            
            //     }
            // });

        });
    }
}
