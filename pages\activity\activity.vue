<template>
	<view class="activityWrap">
		<view class="youhui">{{i18n.title}}</view>
		<view class="listItem" v-for="(item, index) in tableList" :key="index" @click="gotoDetail(item)">
			<image class="listItem-img" :src="$tools.setImgUrl(item.image)" />
			<view class="listItem-top">
				<view class="listItem-txt">
					<span class="txt">{{item.title}}</span>
					<span class="date">{{item.updated_at}}</span>
				</view>
				<view class="listItem-det">{{item.body.replace(/<.*?>/g,"")}}</view>
			</view>
		</view>
		<bottom :num='4'></bottom>
		<a-tc></a-tc>
	</view>
</template>

<script>
    import uniLoadMore from "@/components/uni-load-more/uni-load-more.vue"
	import bottom from '../home/<USER>'
	export default {
        name: "activityWrap",
        components: {
            uniLoadMore,
			bottom
        },
		data() {
			return {
				total: 0,
                pageIndex: 1,
                pageSize: 1000000,
                hasMore: false,
                tableList: [],
                status: 'more',
                contentText: {
                    contentdown: '查看更多',
                    contentrefresh: '加载中',
                    contentnomore: '没有更多了'
                },
				levelArr:[],
				staffId: 0
			}
		},
		computed: {
		    i18n () {
		       return this.$t("activity")
		    },
			more() {
				return this.$t("contentText")
			}
		},
		onShow() {
			this.contentText = {
				contentdown: this.more.contentdown,
				contentrefresh: this.more.contentrefresh,
				contentnomore: this.more.contentnomore
			}
		},
        onLoad() {
            this.queryByInput()
        },

        //上拉加载
        onReachBottom(){
            if (this.status == 'noMore'){
                return;
            }
            this.pageIndex ++;
            this.getUserInfo(); // 获取活动列表
        },

        //下拉刷新
        onPullDownRefresh(){
            uni.stopPullDownRefresh();
            this.tableList = [];
            this.queryByInput()
        },
		methods: {
			// 获取用户信息
			getUserInfo(){
			    this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
			        if(res.status == 200){
						this.staffId = res.data.staff_id
						this.getActivityList() // 获取公告列表
			        } else {
			            uni.showToast({
			                title: res.msg,
			                duration: 1500,
			                icon:'none'
			            });
			        }
			    })
			},
			// 初始数据
            queryByInput(){
                this.pageIndex = 1;
                this.getUserInfo(); //获取活动列表
            },

            //获取活动列表
            getActivityList: function() {
                var that = this;
                this.$tools.Get('api/user/centerinfo/action',{
                	staff_id: this.staffId,
					language: uni.getStorageSync('lang')
				 }).then(function(res) {
                    //这里只会在接口是成功状态返回
                    that.tableList = res.data
                }).catch(function(error) {
                    //这里只会在接口是失败状态返回，不需要去处理错误提示
                    console.log(error);
                });
            },

            // 详情页
            gotoDetail(item){
            	uni.navigateTo({
            	    url: '/pages/activity/activityDetail?strTitle='+ item.title + '&strings=' + item.body + '&strDate=' + item.created_at + '&strImage=' + item.image
            	});
            }
		}
	}
</script>

<style scoped lang="less">
    page{
		padding-bottom: 200rpx;
        .activityWrap{
			background: rgba(245, 245, 245, 1);
			height: 100vh;
			overflow: scroll;
			.youhui{
				height:88rpx;
				text-align: center;
				font-family: PingFang SC;
				font-weight: bold;
				color:#fff;
				font-size:32rpx;
				line-height: 88rpx;
				background-color: #172D52;
			}
            .listItem{
				margin: 20rpx 20rpx 0 20rpx;
				background: #FFFFFF;
				border-radius: 20rpx;
				padding: 10rpx;
				.listItem-img{
					width: 100%;
					height: 320rpx;
					border-radius: 14rpx;
					object-fit: cover;
				}
				.listItem-top{
					height: 140rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					padding: 0 12rpx;
				}
				.listItem-txt{
					font-size: 32rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #333333;
					height: 44rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					.txt{
						height: 44rpx;
						font-size: 32rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #333333;
						width: calc(100% - 320rpx);
						text-overflow: ellipsis;
						-o-text-overflow: ellipsis;
						overflow: hidden;
						white-space: nowrap;
					}
					.date{
						width: 320rpx;
						height: 44rpx;
						display: flex;
						align-items: center;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #999999;
						justify-content: flex-end;
					}
				}
				.listItem-det{
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #999999;
					margin-top: 11rpx;
					height: 44rpx;
					text-overflow: ellipsis;
					-o-text-overflow: ellipsis;
					overflow: hidden;
					white-space: nowrap;
				}
            }
        }
    }
</style>
