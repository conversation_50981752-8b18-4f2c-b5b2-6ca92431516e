/*!
  * vue-i18n v9.2.2
  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";const n="undefined"!=typeof window,r="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,a=e=>r?Symbol(e):e,l=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),o=e=>"number"==typeof e&&isFinite(e),s=e=>"[object RegExp]"===h(e),c=e=>L(e)&&0===Object.keys(e).length;function u(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const i=Object.assign;function f(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const m=Object.prototype.hasOwnProperty;function p(e,t){return m.call(e,t)}const g=Array.isArray,d=e=>"function"==typeof e,_=e=>"string"==typeof e,v=e=>"boolean"==typeof e,b=e=>null!==e&&"object"==typeof e,k=Object.prototype.toString,h=e=>k.call(e),L=e=>"[object Object]"===h(e),y=15;function T(e,t,n={}){const{domain:r,messages:a,args:l}=n,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=r,o}function E(e){throw e}function F(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const N=" ",I="\n",O=String.fromCharCode(8232),R=String.fromCharCode(8233);function C(e){const t=e;let n=0,r=1,a=1,l=0;const o=e=>"\r"===t[e]&&t[e+1]===I,s=e=>t[e]===R,c=e=>t[e]===O,u=e=>o(e)||(e=>t[e]===I)(e)||s(e)||c(e),i=e=>o(e)||s(e)||c(e)?I:t[e];function f(){return l=0,u(n)&&(r++,a=0),o(n)&&n++,n++,a++,t[n]}return{index:()=>n,line:()=>r,column:()=>a,peekOffset:()=>l,charAt:i,currentChar:()=>i(n),currentPeek:()=>i(n+l),next:f,peek:function(){return o(n+l)&&l++,l++,t[n+l]},reset:function(){n=0,r=1,a=1,l=0},resetPeek:function(e=0){l=e},skipToPeek:function(){const e=n+l;for(;e!==n;)f();l=0}}}const P=void 0;function w(e,t={}){const n=!1!==t.location,r=C(e),a=()=>r.index(),l=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},o=l(),s=a(),c={currentType:14,offset:s,startLoc:o,endLoc:o,lastType:14,lastOffset:s,lastStartLoc:o,lastEndLoc:o,braceNest:0,inLinked:!1,text:""},u=()=>c,{onError:i}=t;function f(e,t,r){e.endLoc=l(),e.currentType=t;const a={type:t};return n&&(a.loc=F(e.startLoc,e.endLoc)),null!=r&&(a.value=r),a}const m=e=>f(e,14);function p(e,t){return e.currentChar()===t?(e.next(),t):(l(),"")}function g(e){let t="";for(;e.currentPeek()===N||e.currentPeek()===I;)t+=e.currentPeek(),e.peek();return t}function d(e){const t=g(e);return e.skipToPeek(),t}function _(e){if(e===P)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function v(e,t){const{currentType:n}=t;if(2!==n)return!1;g(e);const r=function(e){if(e===P)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function b(e){g(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function k(e,t=!0){const n=(t=!1,r="",a=!1)=>{const l=e.currentPeek();return"{"===l?"%"!==r&&t:"@"!==l&&l?"%"===l?(e.peek(),n(t,"%",!0)):"|"===l?!("%"!==r&&!a)||!(r===N||r===I):l===N?(e.peek(),n(!0,N,a)):l!==I||(e.peek(),n(!0,I,a)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function h(e,t){const n=e.currentChar();return n===P?P:t(n)?(e.next(),n):null}function L(e){return h(e,(e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}))}function y(e){return h(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57}))}function T(e){return h(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}))}function E(e){let t="",n="";for(;t=y(e);)n+=t;return n}function O(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!k(e))break;t+=n,e.next()}else if(n===N||n===I)if(k(e))t+=n,e.next();else{if(b(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function R(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return w(e,t,4);case"U":return w(e,t,6);default:return l(),""}}function w(e,t,n){p(e,t);let r="";for(let t=0;t<n;t++){const t=T(e);if(!t){l(),e.currentChar();break}r+=t}return`\\${t}${r}`}function M(e){d(e);const t=p(e,"|");return d(e),t}function W(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&l(),e.next(),n=f(t,2,"{"),d(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&l(),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&d(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&l(),n=S(e,t)||m(t),t.braceNest=0,n;default:let r=!0,a=!0,o=!0;if(b(e))return t.braceNest>0&&l(),n=f(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return l(),t.braceNest=0,x(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;g(e);const r=_(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){d(e);let t="",n="";for(;t=L(e);)n+=t;return e.currentChar()===P&&l(),n}(e)),d(e),n;if(a=v(e,t))return n=f(t,6,function(e){d(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${E(e)}`):t+=E(e),e.currentChar()===P&&l(),t}(e)),d(e),n;if(o=function(e,t){const{currentType:n}=t;if(2!==n)return!1;g(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){d(e),p(e,"'");let t="",n="";const r=e=>"'"!==e&&e!==I;for(;t=h(e,r);)n+="\\"===t?R(e):t;const a=e.currentChar();return a===I||a===P?(l(),a===I&&(e.next(),p(e,"'")),n):(p(e,"'"),n)}(e)),d(e),n;if(!r&&!a&&!o)return n=f(t,13,function(e){d(e);let t="",n="";const r=e=>"{"!==e&&"}"!==e&&e!==N&&e!==I;for(;t=h(e,r);)n+=t;return n}(e)),l(),n.value,d(e),n}return n}function S(e,t){const{currentType:n}=t;let r=null;const a=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||a!==I&&a!==N||l(),a){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return d(e),e.next(),f(t,9,".");case":":return d(e),e.next(),f(t,10,":");default:return b(e)?(r=f(t,1,M(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;g(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;g(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(d(e),S(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;g(e);const r=_(e.currentPeek());return e.resetPeek(),r}(e,t)?(d(e),f(t,12,function(e){let t="",n="";for(;t=L(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?_(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===N||!t)&&(t===I?(e.peek(),r()):_(t))},a=r();return e.resetPeek(),a}(e,t)?(d(e),"{"===a?W(e,t)||r:f(t,11,function(e){const t=(n=!1,r)=>{const a=e.currentChar();return"{"!==a&&"%"!==a&&"@"!==a&&"|"!==a&&a?a===N?r:a===I?(r+=a,e.next(),t(n,r)):(r+=a,e.next(),t(!0,r)):r};return t(!1,"")}(e))):(8===n&&l(),t.braceNest=0,t.inLinked=!1,x(e,t))}}function x(e,t){let n={type:14};if(t.braceNest>0)return W(e,t)||m(t);if(t.inLinked)return S(e,t)||m(t);switch(e.currentChar()){case"{":return W(e,t)||m(t);case"}":return l(),e.next(),f(t,3,"}");case"@":return S(e,t)||m(t);default:if(b(e))return n=f(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:a}=function(e){const t=g(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return a?f(t,0,O(e)):f(t,4,function(e){d(e);return"%"!==e.currentChar()&&l(),e.next(),"%"}(e));if(k(e))return f(t,0,O(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:o}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=n,c.lastEndLoc=o,c.offset=a(),c.startLoc=l(),r.currentChar()===P?f(c,14):x(r,c)},currentOffset:a,currentPosition:l,context:u}}const M=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function W(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function S(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const a={type:e,start:n,end:n};return t&&(a.loc={start:r,end:r}),a}function a(e,n,r,a){e.end=n,a&&(e.type=a),t&&e.loc&&(e.loc.end=r)}function l(e,t){const n=e.context(),l=r(3,n.offset,n.startLoc);return l.value=t,a(l,e.currentOffset(),e.currentPosition()),l}function o(e,t){const n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(5,l,o);return s.index=parseInt(t,10),e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function s(e,t){const n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(4,l,o);return s.key=t,e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function c(e,t){const n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(9,l,o);return s.value=t.replace(M,W),e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function u(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let l=e.nextToken();if(9===l.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(8,l,o);return 12!==t.type?(n.lastStartLoc,s.value="",a(s,l,o),{nextConsumeToken:t,node:s}):(null==t.value&&(n.lastStartLoc,x(t)),s.value=t.value||"",a(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);n.modifier=t.node,l=t.nextConsumeToken||e.nextToken()}switch(10!==l.type&&(t.lastStartLoc,x(l)),l=e.nextToken(),2===l.type&&(l=e.nextToken()),l.type){case 11:null==l.value&&(t.lastStartLoc,x(l)),n.key=function(e,t){const n=e.context(),l=r(7,n.offset,n.startLoc);return l.value=t,a(l,e.currentOffset(),e.currentPosition()),l}(e,l.value||"");break;case 5:null==l.value&&(t.lastStartLoc,x(l)),n.key=s(e,l.value||"");break;case 6:null==l.value&&(t.lastStartLoc,x(l)),n.key=o(e,l.value||"");break;case 7:null==l.value&&(t.lastStartLoc,x(l)),n.key=c(e,l.value||"");break;default:t.lastStartLoc;const u=e.context(),i=r(7,u.offset,u.startLoc);return i.value="",a(i,u.offset,u.startLoc),n.key=i,a(n,u.offset,u.startLoc),{nextConsumeToken:l,node:n}}return a(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let i=null;do{const r=i||e.nextToken();switch(i=null,r.type){case 0:null==r.value&&(t.lastStartLoc,x(r)),n.items.push(l(e,r.value||""));break;case 6:null==r.value&&(t.lastStartLoc,x(r)),n.items.push(o(e,r.value||""));break;case 5:null==r.value&&(t.lastStartLoc,x(r)),n.items.push(s(e,r.value||""));break;case 7:null==r.value&&(t.lastStartLoc,x(r)),n.items.push(c(e,r.value||""));break;case 8:const a=u(e);n.items.push(a.node),i=a.nextConsumeToken||null}}while(14!==t.currentType&&1!==t.currentType);return a(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function m(e){const t=e.context(),{offset:n,startLoc:l}=t,o=f(e);return 14===t.currentType?o:function(e,t,n,l){const o=e.context();let s=0===l.items.length;const c=r(1,t,n);c.cases=[],c.cases.push(l);do{const t=f(e);s||(s=0===t.items.length),c.cases.push(t)}while(14!==o.currentType);return a(c,e.currentOffset(),e.currentPosition()),c}(e,n,l,o)}return{parse:function(n){const l=w(n,i({},e)),o=l.context(),s=r(0,o.offset,o.startLoc);return t&&s.loc&&(s.loc.source=n),s.body=m(l),14!==o.currentType&&(o.lastStartLoc,n[o.offset]),a(s,l.currentOffset(),l.currentPosition()),s}}}function x(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function D(e,t){for(let n=0;n<e.length;n++)A(e[n],t)}function A(e,t){switch(e.type){case 1:D(e.cases,t),t.helper("plural");break;case 2:D(e.items,t);break;case 6:A(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function $(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&A(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function U(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?U(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const a=t.cases.length;for(let n=0;n<a&&(U(e,t.cases[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const a=t.items.length;for(let n=0;n<a&&(U(e,t.items[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),U(e,t.key),t.modifier?(e.push(", "),U(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function H(e,t={}){const n=i({},t),r=S(n).parse(e);return $(r,n),((e,t={})=>{const n=_(t.mode)?t.mode:"normal",r=_(t.filename)?t.filename:"message.intl",a=!!t.sourceMap,l=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",o=t.needIndent?t.needIndent:"arrow"!==n,s=e.helpers||[],c=function(e,t){const{sourceMap:n,filename:r,breakLineCode:a,needIndent:l}=t,o={source:e.loc.source,filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:a,needIndent:l,indentLevel:0};function s(e,t){o.code+=e}function c(e,t=!0){const n=t?a:"";s(l?n+"  ".repeat(e):n)}return{context:()=>o,push:s,indent:function(e=!0){const t=++o.indentLevel;e&&c(t)},deindent:function(e=!0){const t=--o.indentLevel;e&&c(t)},newline:function(){c(o.indentLevel)},helper:e=>`_${e}`,needIndent:()=>o.needIndent}}(e,{mode:n,filename:r,sourceMap:a,breakLineCode:l,needIndent:o});c.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(o),s.length>0&&(c.push(`const { ${s.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),c.newline()),c.push("return "),U(c,e),c.deindent(o),c.push("}");const{code:u,map:i}=c.context();return{ast:e,code:u,map:i?i.toJSON():void 0}})(r,n)}const j=[];j[0]={w:[0],i:[3,0],"[":[4],o:[7]},j[1]={w:[1],".":[2],"[":[4],o:[7]},j[2]={w:[2],i:[3,0],0:[3,0]},j[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},j[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},j[5]={"'":[4,0],o:8,l:[5,0]},j[6]={'"':[4,0],o:8,l:[6,0]};const V=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function G(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function B(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,V.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const Y=new Map;function X(e,t){return b(e)?e[t]:null}const z=e=>e,J=e=>"",q=e=>0===e.length?"":e.join(""),K=e=>null==e?"":g(e)||L(e)&&e.toString===k?JSON.stringify(e,null,2):String(e);function Z(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Q(e={}){const t=e.locale,n=function(e){const t=o(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(o(e.named.count)||o(e.named.n))?o(e.named.count)?e.named.count:o(e.named.n)?e.named.n:t:t}(e),r=b(e.pluralRules)&&_(t)&&d(e.pluralRules[t])?e.pluralRules[t]:Z,a=b(e.pluralRules)&&_(t)&&d(e.pluralRules[t])?Z:void 0,l=e.list||[],s=e.named||{};o(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,s);function c(t){const n=d(e.messages)?e.messages(t):!!b(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):J)}const u=L(e.processor)&&d(e.processor.normalize)?e.processor.normalize:q,i=L(e.processor)&&d(e.processor.interpolate)?e.processor.interpolate:K,f={list:e=>l[e],named:e=>s[e],plural:e=>e[r(n,e.length,a)],linked:(t,...n)=>{const[r,a]=n;let l="text",o="";1===n.length?b(r)?(o=r.modifier||o,l=r.type||l):_(r)&&(o=r||o):2===n.length&&(_(r)&&(o=r||o),_(a)&&(l=a||l));let s=c(t)(f);return"vnode"===l&&g(s)&&o&&(s=s[0]),o?(u=o,e.modifiers?e.modifiers[u]:z)(s,l):s;var u},message:c,type:L(e.processor)&&_(e.processor.type)?e.processor.type:"text",interpolate:i,normalize:u};return f}function ee(e,t,n){return[...new Set([n,...g(t)?t:b(t)?Object.keys(t):_(t)?[t]:[n]])]}function te(e,t,n){const r=_(n)?n:le,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let l=a.__localeChainCache.get(r);if(!l){l=[];let e=[n];for(;g(e);)e=ne(l,e,t);const o=g(t)||!L(t)?t:t.default?t.default:null;e=_(o)?[o]:o,g(e)&&ne(l,e,!1),a.__localeChainCache.set(r,l)}return l}function ne(e,t,n){let r=!0;for(let a=0;a<t.length&&v(r);a++){const l=t[a];_(l)&&(r=re(e,t[a],n))}return r}function re(e,t,n){let r;const a=t.split("-");do{r=ae(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function ae(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(g(n)||L(n))&&n[a]&&(r=n[a])}return r}const le="en-US",oe=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let se,ce,ue;let ie=0;function fe(e={}){const t=_(e.version)?e.version:"9.2.2",n=_(e.locale)?e.locale:le,r=g(e.fallbackLocale)||L(e.fallbackLocale)||_(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,a=L(e.messages)?e.messages:{[n]:{}},l=L(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},o=L(e.numberFormats)?e.numberFormats:{[n]:{}},c=i({},e.modifiers||{},{upper:(e,t)=>"text"===t&&_(e)?e.toUpperCase():"vnode"===t&&b(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&_(e)?e.toLowerCase():"vnode"===t&&b(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&_(e)?oe(e):"vnode"===t&&b(e)&&"__v_isVNode"in e?oe(e.children):e}),f=e.pluralRules||{},m=d(e.missing)?e.missing:null,p=!v(e.missingWarn)&&!s(e.missingWarn)||e.missingWarn,k=!v(e.fallbackWarn)&&!s(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,y=!!e.unresolving,T=d(e.postTranslation)?e.postTranslation:null,E=L(e.processor)?e.processor:null,F=!v(e.warnHtmlMessage)||e.warnHtmlMessage,N=!!e.escapeParameter,I=d(e.messageCompiler)?e.messageCompiler:se,O=d(e.messageResolver)?e.messageResolver:ce||X,R=d(e.localeFallbacker)?e.localeFallbacker:ue||ee,C=b(e.fallbackContext)?e.fallbackContext:void 0,P=d(e.onWarn)?e.onWarn:u,w=e,M=b(w.__datetimeFormatters)?w.__datetimeFormatters:new Map,W=b(w.__numberFormatters)?w.__numberFormatters:new Map,S=b(w.__meta)?w.__meta:{};ie++;const x={version:t,cid:ie,locale:n,fallbackLocale:r,messages:a,modifiers:c,pluralRules:f,missing:m,missingWarn:p,fallbackWarn:k,fallbackFormat:h,unresolving:y,postTranslation:T,processor:E,warnHtmlMessage:F,escapeParameter:N,messageCompiler:I,messageResolver:O,localeFallbacker:R,fallbackContext:C,onWarn:P,__meta:S};return x.datetimeFormats=l,x.numberFormats=o,x.__datetimeFormatters=M,x.__numberFormatters=W,x}function me(e,t,n,r,a){const{missing:l,onWarn:o}=e;if(null!==l){const r=l(e,n,t,a);return _(r)?r:t}return t}function pe(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}const ge=e=>e;let de=Object.create(null);let _e=y;const ve=()=>++_e,be={INVALID_ARGUMENT:_e,INVALID_DATE_ARGUMENT:ve(),INVALID_ISO_DATE_ARGUMENT:ve(),__EXTEND_POINT__:ve()},ke=()=>"",he=e=>d(e);function Le(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:a,messageCompiler:l,fallbackLocale:s,messages:c}=e,[u,i]=Ee(...t),m=v(i.missingWarn)?i.missingWarn:e.missingWarn,p=v(i.fallbackWarn)?i.fallbackWarn:e.fallbackWarn,d=v(i.escapeParameter)?i.escapeParameter:e.escapeParameter,k=!!i.resolvedMessage,h=_(i.default)||v(i.default)?v(i.default)?l?u:()=>u:i.default:n?l?u:()=>u:"",L=n||""!==h,y=_(i.locale)?i.locale:e.locale;d&&function(e){g(e.list)?e.list=e.list.map((e=>_(e)?f(e):e)):b(e.named)&&Object.keys(e.named).forEach((t=>{_(e.named[t])&&(e.named[t]=f(e.named[t]))}))}(i);let[T,E,F]=k?[u,y,c[y]||{}]:ye(e,u,y,s,p,m),N=T,I=u;if(k||_(N)||he(N)||L&&(N=h,I=N),!(k||(_(N)||he(N))&&_(E)))return a?-1:u;let O=!1;const R=he(N)?N:Te(e,u,E,N,I,(()=>{O=!0}));if(O)return N;const C=function(e,t,n,r){const{modifiers:a,pluralRules:l,messageResolver:s,fallbackLocale:c,fallbackWarn:u,missingWarn:i,fallbackContext:f}=e,m=r=>{let a=s(n,r);if(null==a&&f){const[,,e]=ye(f,r,t,c,u,i);a=s(e,r)}if(_(a)){let n=!1;const l=Te(e,r,t,a,r,(()=>{n=!0}));return n?ke:l}return he(a)?a:ke},p={locale:t,modifiers:a,pluralRules:l,messages:m};e.processor&&(p.processor=e.processor);r.list&&(p.list=r.list);r.named&&(p.named=r.named);o(r.plural)&&(p.pluralIndex=r.plural);return p}(e,E,F,i),P=function(e,t,n){return t(n)}(0,R,Q(C));return r?r(P,u):P}function ye(e,t,n,r,a,l){const{messages:o,onWarn:s,messageResolver:c,localeFallbacker:u}=e,i=u(e,r,n);let f,m={},p=null;for(let n=0;n<i.length&&(f=i[n],m=o[f]||{},null===(p=c(m,t))&&(p=m[t]),!_(p)&&!d(p));n++){const n=me(e,t,f,0,"translate");n!==t&&(p=n)}return[p,f,m]}function Te(e,t,n,r,a,o){const{messageCompiler:s,warnHtmlMessage:c}=e;if(he(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==s){const e=()=>r;return e.locale=n,e.key=t,e}const u=s(r,function(e,t,n,r,a,o){return{warnHtmlMessage:a,onError:e=>{throw o&&o(e),e},onCacheKey:e=>((e,t,n)=>l({l:e,k:t,s:n}))(t,n,e)}}(0,n,a,0,c,o));return u.locale=n,u.key=t,u.source=r,u}function Ee(...e){const[t,n,r]=e,a={};if(!_(t)&&!o(t)&&!he(t))throw Error(be.INVALID_ARGUMENT);const l=o(t)?String(t):(he(t),t);return o(n)?a.plural=n:_(n)?a.default=n:L(n)&&!c(n)?a.named=n:g(n)&&(a.list=n),o(r)?a.plural=r:_(r)?a.default=r:L(r)&&i(a,r),[l,a]}function Fe(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a,onWarn:l,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[u,f,m,p]=Ie(...t);v(m.missingWarn)?m.missingWarn:e.missingWarn;v(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const g=!!m.part,d=_(m.locale)?m.locale:e.locale,b=o(e,a,d);if(!_(u)||""===u)return new Intl.DateTimeFormat(d,p).format(f);let k,h={},y=null;for(let t=0;t<b.length&&(k=b[t],h=n[k]||{},y=h[u],!L(y));t++)me(e,u,k,0,"datetime format");if(!L(y)||!_(k))return r?-1:u;let T=`${k}__${u}`;c(p)||(T=`${T}__${JSON.stringify(p)}`);let E=s.get(T);return E||(E=new Intl.DateTimeFormat(k,i({},y,p)),s.set(T,E)),g?E.formatToParts(f):E.format(f)}const Ne=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Ie(...e){const[t,n,r,a]=e,l={};let s,c={};if(_(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(be.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();s=new Date(n);try{s.toISOString()}catch(e){throw Error(be.INVALID_ISO_DATE_ARGUMENT)}}else if("[object Date]"===h(t)){if(isNaN(t.getTime()))throw Error(be.INVALID_DATE_ARGUMENT);s=t}else{if(!o(t))throw Error(be.INVALID_ARGUMENT);s=t}return _(n)?l.key=n:L(n)&&Object.keys(n).forEach((e=>{Ne.includes(e)?c[e]=n[e]:l[e]=n[e]})),_(r)?l.locale=r:L(r)&&(c=r),L(a)&&(c=a),[l.key||"",s,l,c]}function Oe(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__datetimeFormatters.has(n)&&r.__datetimeFormatters.delete(n)}}function Re(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a,onWarn:l,localeFallbacker:o}=e,{__numberFormatters:s}=e,[u,f,m,p]=Pe(...t);v(m.missingWarn)?m.missingWarn:e.missingWarn;v(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const g=!!m.part,d=_(m.locale)?m.locale:e.locale,b=o(e,a,d);if(!_(u)||""===u)return new Intl.NumberFormat(d,p).format(f);let k,h={},y=null;for(let t=0;t<b.length&&(k=b[t],h=n[k]||{},y=h[u],!L(y));t++)me(e,u,k,0,"number format");if(!L(y)||!_(k))return r?-1:u;let T=`${k}__${u}`;c(p)||(T=`${T}__${JSON.stringify(p)}`);let E=s.get(T);return E||(E=new Intl.NumberFormat(k,i({},y,p)),s.set(T,E)),g?E.formatToParts(f):E.format(f)}const Ce=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Pe(...e){const[t,n,r,a]=e,l={};let s={};if(!o(t))throw Error(be.INVALID_ARGUMENT);const c=t;return _(n)?l.key=n:L(n)&&Object.keys(n).forEach((e=>{Ce.includes(e)?s[e]=n[e]:l[e]=n[e]})),_(r)?l.locale=r:L(r)&&(s=r),L(a)&&(s=a),[l.key||"",c,l,s]}function we(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__numberFormatters.has(n)&&r.__numberFormatters.delete(n)}}const Me="9.2.2";let We=y;const Se=()=>++We,xe={UNEXPECTED_RETURN_TYPE:We,INVALID_ARGUMENT:Se(),MUST_BE_CALL_SETUP_TOP:Se(),NOT_INSLALLED:Se(),NOT_AVAILABLE_IN_LEGACY_MODE:Se(),REQUIRED_VALUE:Se(),INVALID_VALUE:Se(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Se(),NOT_INSLALLED_WITH_PROVIDE:Se(),UNEXPECTED_ERROR:Se(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Se(),BRIDGE_SUPPORT_VUE_2_ONLY:Se(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Se(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Se(),__EXTEND_POINT__:Se()};const De=a("__transrateVNode"),Ae=a("__datetimeParts"),$e=a("__numberParts"),Ue=a("__setPluralRules"),He=a("__injectWithOption");function je(e){if(!b(e))return e;for(const t in e)if(p(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let a=e;for(let e=0;e<r;e++)n[e]in a||(a[n[e]]={}),a=a[n[e]];a[n[r]]=e[t],delete e[t],b(a[n[r]])&&je(a[n[r]])}else b(e[t])&&je(e[t]);return e}function Ve(e,t){const{messages:n,__i18n:r,messageResolver:a,flatJson:l}=t,o=L(n)?n:g(r)?{}:{[e]:{}};if(g(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(o[t]=o[t]||{},Be(n,o[t])):Be(n,o)}else _(e)&&Be(JSON.parse(e),o)})),null==a&&l)for(const e in o)p(o,e)&&je(o[e]);return o}const Ge=e=>!b(e)||g(e);function Be(e,t){if(Ge(e)||Ge(t))throw Error(xe.INVALID_VALUE);for(const n in e)p(e,n)&&(Ge(e[n])||Ge(t[n])?t[n]=e[n]:Be(e[n],t[n]))}function Ye(e,t,n){let r=b(t.messages)?t.messages:{};"__i18nGlobal"in n&&(r=Ve(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const a=Object.keys(r);if(a.length&&a.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),b(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(b(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function Xe(e){return t.createVNode(t.Text,null,e,0)}let ze=0;function Je(e){return(n,r,a,l)=>e(r,a,t.getCurrentInstance()||void 0,l)}function qe(e={},r){const{__root:a}=e,l=void 0===a;let c=!v(e.inheritLocale)||e.inheritLocale;const u=t.ref(a&&c?a.locale.value:_(e.locale)?e.locale:le),f=t.ref(a&&c?a.fallbackLocale.value:_(e.fallbackLocale)||g(e.fallbackLocale)||L(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:u.value),m=t.ref(Ve(u.value,e)),p=t.ref(L(e.datetimeFormats)?e.datetimeFormats:{[u.value]:{}}),k=t.ref(L(e.numberFormats)?e.numberFormats:{[u.value]:{}});let h=a?a.missingWarn:!v(e.missingWarn)&&!s(e.missingWarn)||e.missingWarn,y=a?a.fallbackWarn:!v(e.fallbackWarn)&&!s(e.fallbackWarn)||e.fallbackWarn,T=a?a.fallbackRoot:!v(e.fallbackRoot)||e.fallbackRoot,E=!!e.fallbackFormat,F=d(e.missing)?e.missing:null,N=d(e.missing)?Je(e.missing):null,I=d(e.postTranslation)?e.postTranslation:null,O=a?a.warnHtmlMessage:!v(e.warnHtmlMessage)||e.warnHtmlMessage,R=!!e.escapeParameter;const C=a?a.modifiers:L(e.modifiers)?e.modifiers:{};let P,w=e.pluralRules||a&&a.pluralRules;P=(()=>{const t={version:Me,locale:u.value,fallbackLocale:f.value,messages:m.value,modifiers:C,pluralRules:w,missing:null===N?void 0:N,missingWarn:h,fallbackWarn:y,fallbackFormat:E,unresolving:!0,postTranslation:null===I?void 0:I,warnHtmlMessage:O,escapeParameter:R,messageResolver:e.messageResolver,__meta:{framework:"vue"}};t.datetimeFormats=p.value,t.numberFormats=k.value,t.__datetimeFormatters=L(P)?P.__datetimeFormatters:void 0,t.__numberFormatters=L(P)?P.__numberFormatters:void 0;return fe(t)})(),pe(P,u.value,f.value);const M=t.computed({get:()=>u.value,set:e=>{u.value=e,P.locale=u.value}}),W=t.computed({get:()=>f.value,set:e=>{f.value=e,P.fallbackLocale=f.value,pe(P,u.value,e)}}),S=t.computed((()=>m.value)),x=t.computed((()=>p.value)),D=t.computed((()=>k.value));const A=(e,t,n,r,l,s)=>{let c;if(u.value,f.value,m.value,p.value,k.value,c=e(P),o(c)&&-1===c){const[e,n]=t();return a&&T?r(a):l(e)}if(s(c))return c;throw Error(xe.UNEXPECTED_RETURN_TYPE)};function $(...e){return A((t=>Reflect.apply(Le,null,[t,...e])),(()=>Ee(...e)),0,(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>_(e)))}const U={normalize:function(e){return e.map((e=>_(e)||o(e)||v(e)?Xe(String(e)):e))},interpolate:e=>e,type:"vnode"};function H(e){return m.value[e]||{}}ze++,a&&n&&(t.watch(a.locale,(e=>{c&&(u.value=e,P.locale=e,pe(P,u.value,f.value))})),t.watch(a.fallbackLocale,(e=>{c&&(f.value=e,P.fallbackLocale=e,pe(P,u.value,f.value))})));const j={id:ze,locale:M,fallbackLocale:W,get inheritLocale(){return c},set inheritLocale(e){c=e,e&&a&&(u.value=a.locale.value,f.value=a.fallbackLocale.value,pe(P,u.value,f.value))},get availableLocales(){return Object.keys(m.value).sort()},messages:S,get modifiers(){return C},get pluralRules(){return w||{}},get isGlobal(){return l},get missingWarn(){return h},set missingWarn(e){h=e,P.missingWarn=h},get fallbackWarn(){return y},set fallbackWarn(e){y=e,P.fallbackWarn=y},get fallbackRoot(){return T},set fallbackRoot(e){T=e},get fallbackFormat(){return E},set fallbackFormat(e){E=e,P.fallbackFormat=E},get warnHtmlMessage(){return O},set warnHtmlMessage(e){O=e,P.warnHtmlMessage=e},get escapeParameter(){return R},set escapeParameter(e){R=e,P.escapeParameter=e},t:$,getLocaleMessage:H,setLocaleMessage:function(e,t){m.value[e]=t,P.messages=m.value},mergeLocaleMessage:function(e,t){m.value[e]=m.value[e]||{},Be(t,m.value[e]),P.messages=m.value},getPostTranslationHandler:function(){return d(I)?I:null},setPostTranslationHandler:function(e){I=e,P.postTranslation=e},getMissingHandler:function(){return F},setMissingHandler:function(e){null!==e&&(N=Je(e)),F=e,P.missing=N},[Ue]:function(e){w=e,P.pluralRules=w}};return j.datetimeFormats=x,j.numberFormats=D,j.rt=function(...e){const[t,n,r]=e;if(r&&!b(r))throw Error(xe.INVALID_ARGUMENT);return $(t,n,i({resolvedMessage:!0},r||{}))},j.te=function(e,t){const n=H(_(t)?t:u.value);return null!==P.messageResolver(n,e)},j.tm=function(e){const t=function(e){let t=null;const n=te(P,f.value,u.value);for(let r=0;r<n.length;r++){const a=m.value[n[r]]||{},l=P.messageResolver(a,e);if(null!=l){t=l;break}}return t}(e);return null!=t?t:a&&a.tm(e)||{}},j.d=function(...e){return A((t=>Reflect.apply(Fe,null,[t,...e])),(()=>Ie(...e)),0,(t=>Reflect.apply(t.d,t,[...e])),(()=>""),(e=>_(e)))},j.n=function(...e){return A((t=>Reflect.apply(Re,null,[t,...e])),(()=>Pe(...e)),0,(t=>Reflect.apply(t.n,t,[...e])),(()=>""),(e=>_(e)))},j.getDateTimeFormat=function(e){return p.value[e]||{}},j.setDateTimeFormat=function(e,t){p.value[e]=t,P.datetimeFormats=p.value,Oe(P,e,t)},j.mergeDateTimeFormat=function(e,t){p.value[e]=i(p.value[e]||{},t),P.datetimeFormats=p.value,Oe(P,e,t)},j.getNumberFormat=function(e){return k.value[e]||{}},j.setNumberFormat=function(e,t){k.value[e]=t,P.numberFormats=k.value,we(P,e,t)},j.mergeNumberFormat=function(e,t){k.value[e]=i(k.value[e]||{},t),P.numberFormats=k.value,we(P,e,t)},j[He]=e.__injectWithOption,j[De]=function(...e){return A((t=>{let n;const r=t;try{r.processor=U,n=Reflect.apply(Le,null,[r,...e])}finally{r.processor=null}return n}),(()=>Ee(...e)),0,(t=>t[De](...e)),(e=>[Xe(e)]),(e=>g(e)))},j[Ae]=function(...e){return A((t=>Reflect.apply(Fe,null,[t,...e])),(()=>Ie(...e)),0,(t=>t[Ae](...e)),(()=>[]),(e=>_(e)||g(e)))},j[$e]=function(...e){return A((t=>Reflect.apply(Re,null,[t,...e])),(()=>Pe(...e)),0,(t=>t[$e](...e)),(()=>[]),(e=>_(e)||g(e)))},j}function Ke(e={},t){{const t=qe(function(e){const t=_(e.locale)?e.locale:le,n=_(e.fallbackLocale)||g(e.fallbackLocale)||L(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=d(e.missing)?e.missing:void 0,a=!v(e.silentTranslationWarn)&&!s(e.silentTranslationWarn)||!e.silentTranslationWarn,l=!v(e.silentFallbackWarn)&&!s(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!v(e.fallbackRoot)||e.fallbackRoot,c=!!e.formatFallbackMessages,u=L(e.modifiers)?e.modifiers:{},f=e.pluralizationRules,m=d(e.postTranslation)?e.postTranslation:void 0,p=!_(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,b=!!e.escapeParameterHtml,k=!v(e.sync)||e.sync;let h=e.messages;if(L(e.sharedMessages)){const t=e.sharedMessages;h=Object.keys(t).reduce(((e,n)=>{const r=e[n]||(e[n]={});return i(r,t[n]),e}),h||{})}const{__i18n:y,__root:T,__injectWithOption:E}=e,F=e.datetimeFormats,N=e.numberFormats;return{locale:t,fallbackLocale:n,messages:h,flatJson:e.flatJson,datetimeFormats:F,numberFormats:N,missing:r,missingWarn:a,fallbackWarn:l,fallbackRoot:o,fallbackFormat:c,modifiers:u,pluralRules:f,postTranslation:m,warnHtmlMessage:p,escapeParameter:b,messageResolver:e.messageResolver,inheritLocale:k,__i18n:y,__root:T,__injectWithOption:E}}(e)),n={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return v(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=v(e)?!e:e},get silentFallbackWarn(){return v(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=v(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[n,r,a]=e,l={};let o=null,s=null;if(!_(n))throw Error(xe.INVALID_ARGUMENT);const c=n;return _(r)?l.locale=r:g(r)?o=r:L(r)&&(s=r),g(a)?o=a:L(a)&&(s=a),Reflect.apply(t.t,t,[c,o||s||{},l])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[n,r,a]=e,l={plural:1};let s=null,c=null;if(!_(n))throw Error(xe.INVALID_ARGUMENT);const u=n;return _(r)?l.locale=r:o(r)?l.plural=r:g(r)?s=r:L(r)&&(c=r),_(a)?l.locale=a:g(a)?s=a:L(a)&&(c=a),Reflect.apply(t.t,t,[u,s||c||{},l])},te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)},getChoiceIndex:(e,t)=>-1,__onComponentInstanceCreated(t){const{componentInstanceCreatedListener:r}=e;r&&r(t,n)}};return n}}const Ze={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Qe(e){return t.Fragment}const et={name:"i18n-t",props:i({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>o(e)||!isNaN(e)}},Ze),setup(e,n){const{slots:r,attrs:a}=n,l=e.i18n||ut({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(r).filter((e=>"_"!==e)),s={};e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=_(e.plural)?+e.plural:e.plural);const c=function({slots:e},t){if(1===t.length&&"default"===t[0])return(e.default?e.default():[]).reduce(((e,t)=>[...e,...g(t.children)?t.children:[t]]),[]);return t.reduce(((t,n)=>{const r=e[n];return r&&(t[n]=r()),t}),{})}(n,o),u=l[De](e.keypath,c,s),f=i({},a),m=_(e.tag)||b(e.tag)?e.tag:Qe();return t.h(m,f,u)}}};function tt(e,n,r,a){const{slots:l,attrs:o}=n;return()=>{const n={part:!0};let s={};e.locale&&(n.locale=e.locale),_(e.format)?n.key=e.format:b(e.format)&&(_(e.format.key)&&(n.key=e.format.key),s=Object.keys(e.format).reduce(((t,n)=>r.includes(n)?i({},t,{[n]:e.format[n]}):t),{}));const c=a(e.value,n,s);let u=[n.key];g(c)?u=c.map(((e,t)=>{const n=l[e.type],r=n?n({[e.type]:e.value,index:t,parts:c}):[e.value];var a;return g(a=r)&&!_(a[0])&&(r[0].key=`${e.type}-${t}`),r})):_(c)&&(u=[c]);const f=i({},o),m=_(e.tag)||b(e.tag)?e.tag:Qe();return t.h(m,f,u)}}const nt={name:"i18n-n",props:i({value:{type:Number,required:!0},format:{type:[String,Object]}},Ze),setup(e,t){const n=e.i18n||ut({useScope:"parent",__useComponent:!0});return tt(e,t,Ce,((...e)=>n[$e](...e)))}},rt={name:"i18n-d",props:i({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Ze),setup(e,t){const n=e.i18n||ut({useScope:"parent",__useComponent:!0});return tt(e,t,Ne,((...e)=>n[Ae](...e)))}};function at(e){const r=t=>{const{instance:n,modifiers:r,value:a}=t;if(!n||!n.$)throw Error(xe.UNEXPECTED_ERROR);const l=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),o=lt(a);return[Reflect.apply(l.t,l,[...ot(o)]),l]};return{created:(a,l)=>{const[o,s]=r(l);n&&e.global===s&&(a.__i18nWatcher=t.watch(s.locale,(()=>{l.instance&&l.instance.$forceUpdate()}))),a.__composer=s,a.textContent=o},unmounted:e=>{n&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=lt(t);e.textContent=Reflect.apply(n.t,n,[...ot(r)])}},getSSRProps:e=>{const[t]=r(e);return{textContent:t}}}}function lt(e){if(_(e))return{path:e};if(L(e)){if(!("path"in e))throw Error(xe.REQUIRED_VALUE,"path");return e}throw Error(xe.INVALID_VALUE)}function ot(e){const{path:t,locale:n,args:r,choice:a,plural:l}=e,s={},c=r||{};return _(n)&&(s.locale=n),o(a)&&(s.plural=a),o(l)&&(s.plural=l),[t,c,s]}function st(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Ue](t.pluralizationRules||e.pluralizationRules);const n=Ve(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const ct=a("global-vue-i18n");function ut(e={}){const n=t.getCurrentInstance();if(null==n)throw Error(xe.MUST_BE_CALL_SETUP_TOP);if(!n.isCE&&null!=n.appContext.app&&!n.appContext.app.__VUE_I18N_SYMBOL__)throw Error(xe.NOT_INSLALLED);const r=function(e){{const n=t.inject(e.isCE?ct:e.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw function(e,...t){return T(e,null,void 0)}(e.isCE?xe.NOT_INSLALLED_WITH_PROVIDE:xe.UNEXPECTED_ERROR);return n}}(n),a=function(e){return"composition"===e.mode?e.global:e.global.__composer}(r),l=function(e){return e.type}(n),o=function(e,t){return c(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,l);if("legacy"===r.mode&&!e.__useComponent){if(!r.allowComposition)throw Error(xe.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,n,r,a={}){const l="local"===n,o=t.shallowRef(null);if(l&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(xe.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const c=!v(a.inheritLocale)||a.inheritLocale,u=t.ref(l&&c?r.locale.value:_(a.locale)?a.locale:le),i=t.ref(l&&c?r.fallbackLocale.value:_(a.fallbackLocale)||g(a.fallbackLocale)||L(a.fallbackLocale)||!1===a.fallbackLocale?a.fallbackLocale:u.value),f=t.ref(Ve(u.value,a)),m=t.ref(L(a.datetimeFormats)?a.datetimeFormats:{[u.value]:{}}),p=t.ref(L(a.numberFormats)?a.numberFormats:{[u.value]:{}}),b=l?r.missingWarn:!v(a.missingWarn)&&!s(a.missingWarn)||a.missingWarn,k=l?r.fallbackWarn:!v(a.fallbackWarn)&&!s(a.fallbackWarn)||a.fallbackWarn,h=l?r.fallbackRoot:!v(a.fallbackRoot)||a.fallbackRoot,y=!!a.fallbackFormat,T=d(a.missing)?a.missing:null,E=d(a.postTranslation)?a.postTranslation:null,F=l?r.warnHtmlMessage:!v(a.warnHtmlMessage)||a.warnHtmlMessage,N=!!a.escapeParameter,I=l?r.modifiers:L(a.modifiers)?a.modifiers:{},O=a.pluralRules||l&&r.pluralRules;function R(){return[u.value,i.value,f.value,m.value,p.value]}const C=t.computed({get:()=>o.value?o.value.locale.value:u.value,set:e=>{o.value&&(o.value.locale.value=e),u.value=e}}),P=t.computed({get:()=>o.value?o.value.fallbackLocale.value:i.value,set:e=>{o.value&&(o.value.fallbackLocale.value=e),i.value=e}}),w=t.computed((()=>o.value?o.value.messages.value:f.value)),M=t.computed((()=>m.value)),W=t.computed((()=>p.value));function S(){return o.value?o.value.getPostTranslationHandler():E}function x(e){o.value&&o.value.setPostTranslationHandler(e)}function D(){return o.value?o.value.getMissingHandler():T}function A(e){o.value&&o.value.setMissingHandler(e)}function $(e){return R(),e()}function U(...e){return o.value?$((()=>Reflect.apply(o.value.t,null,[...e]))):$((()=>""))}function H(...e){return o.value?Reflect.apply(o.value.rt,null,[...e]):""}function j(...e){return o.value?$((()=>Reflect.apply(o.value.d,null,[...e]))):$((()=>""))}function V(...e){return o.value?$((()=>Reflect.apply(o.value.n,null,[...e]))):$((()=>""))}function G(e){return o.value?o.value.tm(e):{}}function B(e,t){return!!o.value&&o.value.te(e,t)}function Y(e){return o.value?o.value.getLocaleMessage(e):{}}function X(e,t){o.value&&(o.value.setLocaleMessage(e,t),f.value[e]=t)}function z(e,t){o.value&&o.value.mergeLocaleMessage(e,t)}function J(e){return o.value?o.value.getDateTimeFormat(e):{}}function q(e,t){o.value&&(o.value.setDateTimeFormat(e,t),m.value[e]=t)}function K(e,t){o.value&&o.value.mergeDateTimeFormat(e,t)}function Z(e){return o.value?o.value.getNumberFormat(e):{}}function Q(e,t){o.value&&(o.value.setNumberFormat(e,t),p.value[e]=t)}function ee(e,t){o.value&&o.value.mergeNumberFormat(e,t)}const te={get id(){return o.value?o.value.id:-1},locale:C,fallbackLocale:P,messages:w,datetimeFormats:M,numberFormats:W,get inheritLocale(){return o.value?o.value.inheritLocale:c},set inheritLocale(e){o.value&&(o.value.inheritLocale=e)},get availableLocales(){return o.value?o.value.availableLocales:Object.keys(f.value)},get modifiers(){return o.value?o.value.modifiers:I},get pluralRules(){return o.value?o.value.pluralRules:O},get isGlobal(){return!!o.value&&o.value.isGlobal},get missingWarn(){return o.value?o.value.missingWarn:b},set missingWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackWarn(){return o.value?o.value.fallbackWarn:k},set fallbackWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackRoot(){return o.value?o.value.fallbackRoot:h},set fallbackRoot(e){o.value&&(o.value.fallbackRoot=e)},get fallbackFormat(){return o.value?o.value.fallbackFormat:y},set fallbackFormat(e){o.value&&(o.value.fallbackFormat=e)},get warnHtmlMessage(){return o.value?o.value.warnHtmlMessage:F},set warnHtmlMessage(e){o.value&&(o.value.warnHtmlMessage=e)},get escapeParameter(){return o.value?o.value.escapeParameter:N},set escapeParameter(e){o.value&&(o.value.escapeParameter=e)},t:U,getPostTranslationHandler:S,setPostTranslationHandler:x,getMissingHandler:D,setMissingHandler:A,rt:H,d:j,n:V,tm:G,te:B,getLocaleMessage:Y,setLocaleMessage:X,mergeLocaleMessage:z,getDateTimeFormat:J,setDateTimeFormat:q,mergeDateTimeFormat:K,getNumberFormat:Z,setNumberFormat:Q,mergeNumberFormat:ee};function ne(e){e.locale.value=u.value,e.fallbackLocale.value=i.value,Object.keys(f.value).forEach((t=>{e.mergeLocaleMessage(t,f.value[t])})),Object.keys(m.value).forEach((t=>{e.mergeDateTimeFormat(t,m.value[t])})),Object.keys(p.value).forEach((t=>{e.mergeNumberFormat(t,p.value[t])})),e.escapeParameter=N,e.fallbackFormat=y,e.fallbackRoot=h,e.fallbackWarn=k,e.missingWarn=b,e.warnHtmlMessage=F}return t.onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(xe.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const t=o.value=e.proxy.$i18n.__composer;"global"===n?(u.value=t.locale.value,i.value=t.fallbackLocale.value,f.value=t.messages.value,m.value=t.datetimeFormats.value,p.value=t.numberFormats.value):l&&ne(t)})),te}(n,o,a,e)}if("global"===o)return Ye(a,e,l),a;if("parent"===o){let t=function(e,t,n=!1){let r=null;const a=t.root;let l=t.parent;for(;null!=l;){const t=e;if("composition"===e.mode)r=t.__getInstance(l);else{const e=t.__getInstance(l);null!=e&&(r=e.__composer,n&&r&&!r[He]&&(r=null))}if(null!=r)break;if(a===l)break;l=l.parent}return r}(r,n,e.__useComponent);return null==t&&(t=a),t}const u=r;let f=u.__getInstance(n);if(null==f){const r=i({},e);"__i18n"in l&&(r.__i18n=l.__i18n),a&&(r.__root=a),f=qe(r),function(e,n,r){t.onMounted((()=>{}),n),t.onUnmounted((()=>{e.__deleteInstance(n)}),n)}(u,n),u.__setInstance(n,f)}return f}const it=["locale","fallbackLocale","availableLocales"],ft=["t","rt","d","n","tm"];var mt;return mt=function(e,t={}){{const n=(t.onCacheKey||ge)(e),r=de[n];if(r)return r;let a=!1;const l=t.onError||E;t.onError=e=>{a=!0,l(e)};const{code:o}=H(e,t),s=new Function(`return ${o}`)();return a?s:de[n]=s}},se=mt,ce=function(e,t){if(!b(e))return null;let n=Y.get(t);if(n||(n=function(e){const t=[];let n,r,a,l,o,s,c,u=-1,i=0,f=0;const m=[];function p(){const t=e[u+1];if(5===i&&"'"===t||6===i&&'"'===t)return u++,a="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=a:r+=a},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,i=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=B(r),!1===r)return!1;m[1]()}};null!==i;)if(u++,n=e[u],"\\"!==n||!p()){if(l=G(n),c=j[i],o=c[l]||c.l||8,8===o)return;if(i=o[0],void 0!==o[1]&&(s=m[o[1]],s&&(a=n,!1===s())))return;if(7===i)return t}}(t),n&&Y.set(t,n)),!n)return null;const r=n.length;let a=e,l=0;for(;l<r;){const e=a[n[l]];if(void 0===e)return null;a=e,l++}return a},ue=te,e.DatetimeFormat=rt,e.I18nInjectionKey=ct,e.NumberFormat=nt,e.Translation=et,e.VERSION=Me,e.castToVueI18n=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(xe.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e},e.createI18n=function(e={},n){const r=!v(e.legacy)||e.legacy,l=!v(e.globalInjection)||e.globalInjection,o=!r||!!e.allowComposition,s=new Map,[c,u]=function(e,n,r){const a=t.effectScope();{const t=n?a.run((()=>Ke(e))):a.run((()=>qe(e)));if(null==t)throw Error(xe.UNEXPECTED_ERROR);return[a,t]}}(e,r),i=a("");{const e={get mode(){return r?"legacy":"composition"},get allowComposition(){return o},async install(n,...a){n.__VUE_I18N_SYMBOL__=i,n.provide(n.__VUE_I18N_SYMBOL__,e),!r&&l&&function(e,n){const r=Object.create(null);it.forEach((e=>{const a=Object.getOwnPropertyDescriptor(n,e);if(!a)throw Error(xe.UNEXPECTED_ERROR);const l=t.isRef(a.value)?{get:()=>a.value.value,set(e){a.value.value=e}}:{get:()=>a.get&&a.get()};Object.defineProperty(r,e,l)})),e.config.globalProperties.$i18n=r,ft.forEach((t=>{const r=Object.getOwnPropertyDescriptor(n,t);if(!r||!r.value)throw Error(xe.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,r)}))}(n,e.global),function(e,t,...n){const r=L(n[0])?n[0]:{},a=!!r.useI18nComponentName;(!v(r.globalInstall)||r.globalInstall)&&(e.component(a?"i18n":et.name,et),e.component(nt.name,nt),e.component(rt.name,rt)),e.directive("t",at(t))}(n,e,...a),r&&n.mixin(function(e,n,r){return{beforeCreate(){const a=t.getCurrentInstance();if(!a)throw Error(xe.UNEXPECTED_ERROR);const l=this.$options;if(l.i18n){const t=l.i18n;l.__i18n&&(t.__i18n=l.__i18n),t.__root=n,this===this.$root?this.$i18n=st(e,t):(t.__injectWithOption=!0,this.$i18n=Ke(t))}else l.__i18n?this===this.$root?this.$i18n=st(e,l):this.$i18n=Ke({__i18n:l.__i18n,__injectWithOption:!0,__root:n}):this.$i18n=e;l.__i18nGlobal&&Ye(n,l,l),e.__onComponentInstanceCreated(this.$i18n),r.__setInstance(a,this.$i18n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(xe.UNEXPECTED_ERROR);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,r.__deleteInstance(e),delete this.$i18n}}}(u,u.__composer,e));const o=n.unmount;n.unmount=()=>{e.dispose(),o()}},get global(){return u},dispose(){c.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}},e.useI18n=ut,e.vTDirective=at,Object.defineProperty(e,"__esModule",{value:!0}),e}({},Vue);
