<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API连接测试工具</h1>
        <p>此工具用于测试前端与后端API的连接状态，验证CORS修复是否有效。</p>
        
        <div class="test-section info">
            <h3>📋 测试说明</h3>
            <p>• 测试API基础连接</p>
            <p>• 验证CORS配置</p>
            <p>• 检查GET方法支持</p>
        </div>

        <div class="test-section">
            <h3>🌐 基础连接测试</h3>
            <button onclick="testBasicConnection()">测试基础连接</button>
            <div id="basicResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔐 用户API测试</h3>
            <button onclick="testUserAPI()">测试用户信息API</button>
            <div id="userResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📝 注册API测试</h3>
            <p><small>注意：这只是测试连接，不会实际注册用户</small></p>
            <button onclick="testRegisterAPI()">测试注册API连接</button>
            <div id="registerResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'https://admin.inspap.com/';

        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent = `[${timestamp}] ${message}`;
            element.className = `result ${type}`;
        }

        async function testBasicConnection() {
            logResult('basicResult', '正在测试基础连接...', 'info');
            
            try {
                const response = await fetch(API_BASE, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                logResult('basicResult', `✅ 基础连接成功！状态码: ${response.status}`, 'success');
            } catch (error) {
                logResult('basicResult', `❌ 基础连接失败: ${error.message}`, 'error');
            }
        }

        async function testUserAPI() {
            logResult('userResult', '正在测试用户API...', 'info');
            
            try {
                const response = await fetch(API_BASE + 'api/user/info?language=EN', {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    }
                });
                
                if (response.ok) {
                    logResult('userResult', `✅ 用户API连接成功！状态码: ${response.status}`, 'success');
                } else {
                    logResult('userResult', `⚠️ 用户API响应: ${response.status} - 这是正常的，因为没有token`, 'info');
                }
            } catch (error) {
                logResult('userResult', `❌ 用户API连接失败: ${error.message}`, 'error');
            }
        }

        async function testRegisterAPI() {
            logResult('registerResult', '正在测试注册API连接...', 'info');
            
            try {
                // 只测试连接，不发送真实数据
                const testParams = new URLSearchParams({
                    uname: 'test_connection_only',
                    password: 'test123',
                    code: 'test',
                    phone: '',
                    language: 'EN'
                });
                
                const response = await fetch(API_BASE + 'api/user/register?' + testParams, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    }
                });
                
                logResult('registerResult', `✅ 注册API连接成功！状态码: ${response.status}\n响应可能包含验证错误，这是正常的。`, 'success');
            } catch (error) {
                if (error.message.includes('CORS')) {
                    logResult('registerResult', `❌ CORS错误仍然存在: ${error.message}`, 'error');
                } else {
                    logResult('registerResult', `⚠️ 连接成功但有其他错误: ${error.message}`, 'info');
                }
            }
        }

        // 页面加载时自动运行基础测试
        window.onload = function() {
            setTimeout(testBasicConnection, 1000);
        };
    </script>
</body>
</html>
