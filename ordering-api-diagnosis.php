<?php
/**
 * 订单API诊断工具
 * 诊断405和500错误的原因
 */

echo "<h1>🔍 订单API诊断工具</h1>\n";
echo "<p>诊断时间: " . date('Y-m-d H:i:s') . "</p>\n";

// 1. 检查路由配置
echo "<h2>1. 路由配置检查</h2>\n";

$routeFile = 'admin.inspap.com/routes/api.php';
if (file_exists($routeFile)) {
    $content = file_get_contents($routeFile);
    
    // 检查订单路由
    $getRouteExists = strpos($content, "Route::middleware('auth:api')->get('user/ordering/{action}','ApiController@ordering')") !== false;
    $postRouteExists = strpos($content, "Route::middleware('auth:api')->post('user/ordering/{action}','ApiController@ordering')") !== false;
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4>订单路由状态：</h4>\n";
    echo "<p>GET路由: " . ($getRouteExists ? "✅ 已配置" : "❌ 未配置") . "</p>\n";
    echo "<p>POST路由: " . ($postRouteExists ? "✅ 已配置" : "❌ 未配置") . "</p>\n";
    echo "</div>\n";
} else {
    echo "<p style='color: red;'>❌ 路由文件不存在</p>\n";
}

// 2. 检查控制器文件
echo "<h2>2. 控制器检查</h2>\n";

$controllerFile = 'admin.inspap.com/app/Http/Controllers/ApiController.php';
if (file_exists($controllerFile)) {
    $controllerContent = file_get_contents($controllerFile);
    
    // 检查ordering方法
    $orderingExists = strpos($controllerContent, 'public function ordering(Request $request,$action)') !== false;
    $getActionExists = strpos($controllerContent, "else if(\$action=='get')") !== false;
    $addActionExists = strpos($controllerContent, "if(\$action=='add')") !== false;
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4>控制器方法状态：</h4>\n";
    echo "<p>ordering方法: " . ($orderingExists ? "✅ 存在" : "❌ 不存在") . "</p>\n";
    echo "<p>get action: " . ($getActionExists ? "✅ 存在" : "❌ 不存在") . "</p>\n";
    echo "<p>add action: " . ($addActionExists ? "✅ 存在" : "❌ 不存在") . "</p>\n";
    echo "</div>\n";
    
    // 检查可能的语法错误
    echo "<h3>2.1 语法检查</h3>\n";
    $syntaxCheck = shell_exec("php -l $controllerFile 2>&1");
    if (strpos($syntaxCheck, 'No syntax errors') !== false) {
        echo "<p style='color: green;'>✅ 控制器语法正确</p>\n";
    } else {
        echo "<p style='color: red;'>❌ 控制器语法错误:</p>\n";
        echo "<pre style='background: #f8d7da; padding: 10px;'>$syntaxCheck</pre>\n";
    }
} else {
    echo "<p style='color: red;'>❌ 控制器文件不存在</p>\n";
}

// 3. 检查前端工具类
echo "<h2>3. 前端工具类检查</h2>\n";

$toolsFile = 'static/js/utils/Tools.js';
if (file_exists($toolsFile)) {
    $toolsContent = file_get_contents($toolsFile);
    
    $defaultGet = strpos($toolsContent, "method: options.method || 'GET'") !== false;
    $postMethodGet = strpos($toolsContent, "options['method'] = 'GET'; // 改为GET方法确保稳定性") !== false;
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4>前端工具类状态：</h4>\n";
    echo "<p>默认GET方法: " . ($defaultGet ? "✅ 已配置" : "❌ 未配置") . "</p>\n";
    echo "<p>Post方法改为GET: " . ($postMethodGet ? "✅ 已配置" : "❌ 未配置") . "</p>\n";
    echo "</div>\n";
} else {
    echo "<p style='color: red;'>❌ 前端工具类文件不存在</p>\n";
}

// 4. 检查数据库连接和模型
echo "<h2>4. 数据库和模型检查</h2>\n";

// 检查必要的模型文件
$models = [
    'admin.inspap.com/app/Models/order.php' => '订单模型',
    'admin.inspap.com/app/Models/User.php' => '用户模型',
    'admin.inspap.com/app/Models/Product.php' => '产品模型',
    'admin.inspap.com/app/Models/ProductTmp.php' => '产品临时模型'
];

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>\n";
echo "<h4>模型文件状态：</h4>\n";
foreach ($models as $file => $name) {
    $exists = file_exists($file);
    echo "<p>{$name}: " . ($exists ? "✅ 存在" : "❌ 不存在") . "</p>\n";
}
echo "</div>\n";

// 5. 检查认证配置
echo "<h2>5. 认证配置检查</h2>\n";

$authConfigFile = 'admin.inspap.com/config/auth.php';
if (file_exists($authConfigFile)) {
    echo "<p style='color: green;'>✅ 认证配置文件存在</p>\n";
} else {
    echo "<p style='color: red;'>❌ 认证配置文件不存在</p>\n";
}

// 6. 可能的问题和解决方案
echo "<h2>6. 可能的问题和解决方案</h2>\n";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; border-left: 4px solid #ffc107;'>\n";
echo "<h3>🔧 常见问题排查：</h3>\n";

echo "<h4>A. 405错误 (Method Not Allowed):</h4>\n";
echo "<ul>\n";
echo "<li><strong>原因：</strong> 路由不支持GET方法</li>\n";
echo "<li><strong>解决：</strong> 确认路由文件中已添加GET支持</li>\n";
echo "<li><strong>验证：</strong> 检查上面的路由配置状态</li>\n";
echo "</ul>\n";

echo "<h4>B. 500错误 (Internal Server Error):</h4>\n";
echo "<ul>\n";
echo "<li><strong>可能原因1：</strong> 控制器语法错误</li>\n";
echo "<li><strong>可能原因2：</strong> 数据库连接问题</li>\n";
echo "<li><strong>可能原因3：</strong> 模型文件缺失</li>\n";
echo "<li><strong>可能原因4：</strong> 认证中间件问题</li>\n";
echo "</ul>\n";

echo "<h4>C. 认证问题：</h4>\n";
echo "<ul>\n";
echo "<li><strong>检查：</strong> API调用是否包含有效token</li>\n";
echo "<li><strong>检查：</strong> 用户是否已登录</li>\n";
echo "<li><strong>检查：</strong> token格式是否正确</li>\n";
echo "</ul>\n";
echo "</div>\n";

// 7. 建议的修复步骤
echo "<h2>7. 建议的修复步骤</h2>\n";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 5px; border-left: 4px solid #007bff;'>\n";
echo "<h3>🚀 修复步骤：</h3>\n";
echo "<ol>\n";
echo "<li><strong>清除Laravel缓存：</strong><br>\n";
echo "<code>cd admin.inspap.com && php artisan route:clear && php artisan config:clear && php artisan cache:clear</code></li>\n";

echo "<li><strong>检查Laravel日志：</strong><br>\n";
echo "<code>tail -f admin.inspap.com/storage/logs/laravel.log</code></li>\n";

echo "<li><strong>测试简单API：</strong><br>\n";
echo "先测试不需要认证的API，如：<code>/api/health</code></li>\n";

echo "<li><strong>测试认证API：</strong><br>\n";
echo "确认token有效后测试：<code>/api/user/ordering/get</code></li>\n";

echo "<li><strong>检查数据库：</strong><br>\n";
echo "确认数据库连接正常，相关表存在</li>\n";
echo "</ol>\n";
echo "</div>\n";

// 8. 测试URL生成
echo "<h2>8. 测试URL</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>\n";
echo "<h4>🧪 测试用URL：</h4>\n";
echo "<p><strong>订单获取 (GET):</strong><br>\n";
echo "<code>http://localhost:8080/api/user/ordering/get?api_token=YOUR_TOKEN&uid=USER_ID&limit=10</code></p>\n";

echo "<p><strong>健康检查 (无需认证):</strong><br>\n";
echo "<code>http://localhost:8080/api/health</code></p>\n";

echo "<p><strong>系统信息 (无需认证):</strong><br>\n";
echo "<code>http://localhost:8080/api/system/info</code></p>\n";
echo "</div>\n";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: 'Courier New', monospace; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
ol li, ul li { margin: 8px 0; }
</style>\n";

echo "<p><strong>诊断完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
