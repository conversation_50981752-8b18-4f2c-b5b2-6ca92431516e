/*!
  * core-base v9.2.2
  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
const e=/\{([0-9a-zA-Z]+)\}/g;const t=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),n=e=>"number"==typeof e&&isFinite(e),r=e=>"[object RegExp]"===d(e),o=e=>_(e)&&0===Object.keys(e).length;function c(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const a=Object.assign;function s(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const l=Array.isArray,u=e=>"function"==typeof e,i=e=>"string"==typeof e,f=e=>"boolean"==typeof e,m=e=>null!==e&&"object"==typeof e,p=Object.prototype.toString,d=e=>p.call(e),_=e=>"[object Object]"===d(e),k={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,__EXTEND_POINT__:15};function h(e,t,n={}){const{domain:r,messages:o,args:c}=n,a=new SyntaxError(String(e));return a.code=e,t&&(a.location=t),a.domain=r,a}function g(e){throw e}function b(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const T=String.fromCharCode(8232),y=String.fromCharCode(8233);function L(e){const t=e;let n=0,r=1,o=1,c=0;const a=e=>"\r"===t[e]&&"\n"===t[e+1],s=e=>t[e]===y,l=e=>t[e]===T,u=e=>a(e)||(e=>"\n"===t[e])(e)||s(e)||l(e),i=e=>a(e)||s(e)||l(e)?"\n":t[e];function f(){return c=0,u(n)&&(r++,o=0),a(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>c,charAt:i,currentChar:()=>i(n),currentPeek:()=>i(n+c),next:f,peek:function(){return a(n+c)&&c++,c++,t[n+c]},reset:function(){n=0,r=1,o=1,c=0},resetPeek:function(e=0){c=e},skipToPeek:function(){const e=n+c;for(;e!==n;)f();c=0}}}const N=void 0;function E(e,t={}){const n=!1!==t.location,r=L(e),o=()=>r.index(),c=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},a=c(),s=o(),l={currentType:14,offset:s,startLoc:a,endLoc:a,lastType:14,lastOffset:s,lastStartLoc:a,lastEndLoc:a,braceNest:0,inLinked:!1,text:""},u=()=>l,{onError:i}=t;function f(e,t,r){e.endLoc=c(),e.currentType=t;const o={type:t};return n&&(o.loc=b(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const m=e=>f(e,14);function p(e,t){return e.currentChar()===t?(e.next(),t):(c(),"")}function d(e){let t="";for(;" "===e.currentPeek()||"\n"===e.currentPeek();)t+=e.currentPeek(),e.peek();return t}function _(e){const t=d(e);return e.skipToPeek(),t}function k(e){if(e===N)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function h(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=function(e){if(e===N)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function g(e){d(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function T(e,t=!0){const n=(t=!1,r="",o=!1)=>{const c=e.currentPeek();return"{"===c?"%"!==r&&t:"@"!==c&&c?"%"===c?(e.peek(),n(t,"%",!0)):"|"===c?!("%"!==r&&!o)||!(" "===r||"\n"===r):" "===c?(e.peek(),n(!0," ",o)):"\n"!==c||(e.peek(),n(!0,"\n",o)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function y(e,t){const n=e.currentChar();return n===N?N:t(n)?(e.next(),n):null}function E(e){return y(e,(e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}))}function C(e){return y(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57}))}function A(e){return y(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}))}function O(e){let t="",n="";for(;t=C(e);)n+=t;return n}function x(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!T(e))break;t+=n,e.next()}else if(" "===n||"\n"===n)if(T(e))t+=n,e.next();else{if(g(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function I(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return v(e,t,4);case"U":return v(e,t,6);default:return c(),""}}function v(e,t,n){p(e,t);let r="";for(let t=0;t<n;t++){const t=A(e);if(!t){c(),e.currentChar();break}r+=t}return`\\${t}${r}`}function P(e){_(e);const t=p(e,"|");return _(e),t}function S(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&c(),e.next(),n=f(t,2,"{"),_(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&c(),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&_(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&c(),n=F(e,t)||m(t),t.braceNest=0,n;default:let r=!0,o=!0,a=!0;if(g(e))return t.braceNest>0&&c(),n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return c(),t.braceNest=0,w(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=k(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){_(e);let t="",n="";for(;t=E(e);)n+=t;return e.currentChar()===N&&c(),n}(e)),_(e),n;if(o=h(e,t))return n=f(t,6,function(e){_(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${O(e)}`):t+=O(e),e.currentChar()===N&&c(),t}(e)),_(e),n;if(a=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){_(e),p(e,"'");let t="",n="";const r=e=>"'"!==e&&"\n"!==e;for(;t=y(e,r);)n+="\\"===t?I(e):t;const o=e.currentChar();return"\n"===o||o===N?(c(),"\n"===o&&(e.next(),p(e,"'")),n):(p(e,"'"),n)}(e)),_(e),n;if(!r&&!o&&!a)return n=f(t,13,function(e){_(e);let t="",n="";const r=e=>"{"!==e&&"}"!==e&&" "!==e&&"\n"!==e;for(;t=y(e,r);)n+=t;return n}(e)),c(),n.value,_(e),n}return n}function F(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||"\n"!==o&&" "!==o||c(),o){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return _(e),e.next(),f(t,9,".");case":":return _(e),e.next(),f(t,10,":");default:return g(e)?(r=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;d(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;d(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(_(e),F(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;d(e);const r=k(e.currentPeek());return e.resetPeek(),r}(e,t)?(_(e),f(t,12,function(e){let t="",n="";for(;t=E(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?k(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||" "===t||!t)&&("\n"===t?(e.peek(),r()):k(t))},o=r();return e.resetPeek(),o}(e,t)?(_(e),"{"===o?S(e,t)||r:f(t,11,function(e){const t=(n=!1,r)=>{const o=e.currentChar();return"{"!==o&&"%"!==o&&"@"!==o&&"|"!==o&&o?" "===o?r:"\n"===o?(r+=o,e.next(),t(n,r)):(r+=o,e.next(),t(!0,r)):r};return t(!1,"")}(e))):(8===n&&c(),t.braceNest=0,t.inLinked=!1,w(e,t))}}function w(e,t){let n={type:14};if(t.braceNest>0)return S(e,t)||m(t);if(t.inLinked)return F(e,t)||m(t);switch(e.currentChar()){case"{":return S(e,t)||m(t);case"}":return c(),e.next(),f(t,3,"}");case"@":return F(e,t)||m(t);default:if(g(e))return n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:o}=function(e){const t=d(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return o?f(t,0,x(e)):f(t,4,function(e){_(e);return"%"!==e.currentChar()&&c(),e.next(),"%"}(e));if(T(e))return f(t,0,x(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:a}=l;return l.lastType=e,l.lastOffset=t,l.lastStartLoc=n,l.lastEndLoc=a,l.offset=o(),l.startLoc=c(),r.currentChar()===N?f(l,14):w(r,l)},currentOffset:o,currentPosition:c,context:u}}const C=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function A(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function O(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const o={type:e,start:n,end:n};return t&&(o.loc={start:r,end:r}),o}function o(e,n,r,o){e.end=n,o&&(e.type=o),t&&e.loc&&(e.loc.end=r)}function c(e,t){const n=e.context(),c=r(3,n.offset,n.startLoc);return c.value=t,o(c,e.currentOffset(),e.currentPosition()),c}function s(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:a}=n,s=r(5,c,a);return s.index=parseInt(t,10),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function l(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:a}=n,s=r(4,c,a);return s.key=t,e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function u(e,t){const n=e.context(),{lastOffset:c,lastStartLoc:a}=n,s=r(9,c,a);return s.value=t.replace(C,A),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function i(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let c=e.nextToken();if(9===c.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:c,lastStartLoc:a}=n,s=r(8,c,a);return 12!==t.type?(n.lastStartLoc,s.value="",o(s,c,a),{nextConsumeToken:t,node:s}):(null==t.value&&(n.lastStartLoc,x(t)),s.value=t.value||"",o(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);n.modifier=t.node,c=t.nextConsumeToken||e.nextToken()}switch(10!==c.type&&(t.lastStartLoc,x(c)),c=e.nextToken(),2===c.type&&(c=e.nextToken()),c.type){case 11:null==c.value&&(t.lastStartLoc,x(c)),n.key=function(e,t){const n=e.context(),c=r(7,n.offset,n.startLoc);return c.value=t,o(c,e.currentOffset(),e.currentPosition()),c}(e,c.value||"");break;case 5:null==c.value&&(t.lastStartLoc,x(c)),n.key=l(e,c.value||"");break;case 6:null==c.value&&(t.lastStartLoc,x(c)),n.key=s(e,c.value||"");break;case 7:null==c.value&&(t.lastStartLoc,x(c)),n.key=u(e,c.value||"");break;default:t.lastStartLoc;const a=e.context(),i=r(7,a.offset,a.startLoc);return i.value="",o(i,a.offset,a.startLoc),n.key=i,o(n,a.offset,a.startLoc),{nextConsumeToken:c,node:n}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let a=null;do{const r=a||e.nextToken();switch(a=null,r.type){case 0:null==r.value&&(t.lastStartLoc,x(r)),n.items.push(c(e,r.value||""));break;case 6:null==r.value&&(t.lastStartLoc,x(r)),n.items.push(s(e,r.value||""));break;case 5:null==r.value&&(t.lastStartLoc,x(r)),n.items.push(l(e,r.value||""));break;case 7:null==r.value&&(t.lastStartLoc,x(r)),n.items.push(u(e,r.value||""));break;case 8:const o=i(e);n.items.push(o.node),a=o.nextConsumeToken||null}}while(14!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function m(e){const t=e.context(),{offset:n,startLoc:c}=t,a=f(e);return 14===t.currentType?a:function(e,t,n,c){const a=e.context();let s=0===c.items.length;const l=r(1,t,n);l.cases=[],l.cases.push(c);do{const t=f(e);s||(s=0===t.items.length),l.cases.push(t)}while(14!==a.currentType);return o(l,e.currentOffset(),e.currentPosition()),l}(e,n,c,a)}return{parse:function(n){const c=E(n,a({},e)),s=c.context(),l=r(0,s.offset,s.startLoc);return t&&l.loc&&(l.loc.source=n),l.body=m(c),14!==s.currentType&&(s.lastStartLoc,n[s.offset]),o(l,c.currentOffset(),c.currentPosition()),l}}}function x(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function I(e,t){for(let n=0;n<e.length;n++)v(e[n],t)}function v(e,t){switch(e.type){case 1:I(e.cases,t),t.helper("plural");break;case 2:I(e.items,t);break;case 6:v(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function P(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&v(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function S(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?S(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(S(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let n=0;n<o&&(S(e,t.items[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),S(e,t.key),t.modifier?(e.push(", "),S(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function F(e,t={}){const n=a({},t),r=O(n).parse(e);return P(r,n),((e,t={})=>{const n=i(t.mode)?t.mode:"normal",r=i(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,c=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",a=t.needIndent?t.needIndent:"arrow"!==n,s=e.helpers||[],l=function(e,t){const{sourceMap:n,filename:r,breakLineCode:o,needIndent:c}=t,a={source:e.loc.source,filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:c,indentLevel:0};function s(e,t){a.code+=e}function l(e,t=!0){const n=t?o:"";s(c?n+"  ".repeat(e):n)}return{context:()=>a,push:s,indent:function(e=!0){const t=++a.indentLevel;e&&l(t)},deindent:function(e=!0){const t=--a.indentLevel;e&&l(t)},newline:function(){l(a.indentLevel)},helper:e=>`_${e}`,needIndent:()=>a.needIndent}}(e,{mode:n,filename:r,sourceMap:o,breakLineCode:c,needIndent:a});l.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(a),s.length>0&&(l.push(`const { ${s.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),l.newline()),l.push("return "),S(l,e),l.deindent(a),l.push("}");const{code:u,map:f}=l.context();return{ast:e,code:u,map:f?f.toJSON():void 0}})(r,n)}const w=[];w[0]={w:[0],i:[3,0],"[":[4],o:[7]},w[1]={w:[1],".":[2],"[":[4],o:[7]},w[2]={w:[2],i:[3,0],0:[3,0]},w[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},w[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},w[5]={"'":[4,0],o:8,l:[5,0]},w[6]={'"':[4,0],o:8,l:[6,0]};const D=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function M(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function R(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,D.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}function W(e){const t=[];let n,r,o,c,a,s,l,u=-1,i=0,f=0;const m=[];function p(){const t=e[u+1];if(5===i&&"'"===t||6===i&&'"'===t)return u++,o="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=o:r+=o},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,i=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=R(r),!1===r)return!1;m[1]()}};null!==i;)if(u++,n=e[u],"\\"!==n||!p()){if(c=M(n),l=w[i],a=l[c]||l.l||8,8===a)return;if(i=a[0],void 0!==a[1]&&(s=m[a[1]],s&&(o=n,!1===s())))return;if(7===i)return t}}const $=new Map;function U(e,t){return m(e)?e[t]:null}function j(e,t){if(!m(e))return null;let n=$.get(t);if(n||(n=W(t),n&&$.set(t,n)),!n)return null;const r=n.length;let o=e,c=0;for(;c<r;){const e=o[n[c]];if(void 0===e)return null;o=e,c++}return o}const K=e=>e,V=e=>"",G="text",B=e=>0===e.length?"":e.join(""),H=e=>null==e?"":l(e)||_(e)&&e.toString===p?JSON.stringify(e,null,2):String(e);function X(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function z(e={}){const t=e.locale,r=function(e){const t=n(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(n(e.named.count)||n(e.named.n))?n(e.named.count)?e.named.count:n(e.named.n)?e.named.n:t:t}(e),o=m(e.pluralRules)&&i(t)&&u(e.pluralRules[t])?e.pluralRules[t]:X,c=m(e.pluralRules)&&i(t)&&u(e.pluralRules[t])?X:void 0,a=e.list||[],s=e.named||{};n(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(r,s);function f(t){const n=u(e.messages)?e.messages(t):!!m(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):V)}const p=_(e.processor)&&u(e.processor.normalize)?e.processor.normalize:B,d=_(e.processor)&&u(e.processor.interpolate)?e.processor.interpolate:H,k={list:e=>a[e],named:e=>s[e],plural:e=>e[o(r,e.length,c)],linked:(t,...n)=>{const[r,o]=n;let c="text",a="";1===n.length?m(r)?(a=r.modifier||a,c=r.type||c):i(r)&&(a=r||a):2===n.length&&(i(r)&&(a=r||a),i(o)&&(c=o||c));let s=f(t)(k);return"vnode"===c&&l(s)&&a&&(s=s[0]),a?(u=a,e.modifiers?e.modifiers[u]:K)(s,c):s;var u},message:f,type:_(e.processor)&&i(e.processor.type)?e.processor.type:"text",interpolate:d,normalize:p};return k}const J="i18n:init";let Y=null;function Z(e){Y=e}function Q(){return Y}function q(e,t,n){Y&&Y.emit(J,{timestamp:Date.now(),i18n:e,version:t,meta:n})}const ee=te("function:translate");function te(e){return t=>Y&&Y.emit(e,t)}const ne={NOT_FOUND_KEY:1,FALLBACK_TO_TRANSLATE:2,CANNOT_FORMAT_NUMBER:3,FALLBACK_TO_NUMBER_FORMAT:4,CANNOT_FORMAT_DATE:5,FALLBACK_TO_DATE_FORMAT:6,__EXTEND_POINT__:7},re={[ne.NOT_FOUND_KEY]:"Not found '{key}' key in '{locale}' locale messages.",[ne.FALLBACK_TO_TRANSLATE]:"Fall back to translate '{key}' key with '{target}' locale.",[ne.CANNOT_FORMAT_NUMBER]:"Cannot format a number value due to not supported Intl.NumberFormat.",[ne.FALLBACK_TO_NUMBER_FORMAT]:"Fall back to number format '{key}' key with '{target}' locale.",[ne.CANNOT_FORMAT_DATE]:"Cannot format a date value due to not supported Intl.DateTimeFormat.",[ne.FALLBACK_TO_DATE_FORMAT]:"Fall back to datetime format '{key}' key with '{target}' locale."};function oe(t,...n){return function(t,...n){return 1===n.length&&m(n[0])&&(n=n[0]),n&&n.hasOwnProperty||(n={}),t.replace(e,((e,t)=>n.hasOwnProperty(t)?n[t]:""))}(re[t],...n)}function ce(e,t,n){return[...new Set([n,...l(t)?t:m(t)?Object.keys(t):i(t)?[t]:[n]])]}function ae(e,t,n){const r=i(n)?n:me,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let c=o.__localeChainCache.get(r);if(!c){c=[];let e=[n];for(;l(e);)e=se(c,e,t);const a=l(t)||!_(t)?t:t.default?t.default:null;e=i(a)?[a]:a,l(e)&&se(c,e,!1),o.__localeChainCache.set(r,c)}return c}function se(e,t,n){let r=!0;for(let o=0;o<t.length&&f(r);o++){const c=t[o];i(c)&&(r=le(e,t[o],n))}return r}function le(e,t,n){let r;const o=t.split("-");do{r=ue(e,o.join("-"),n),o.splice(-1,1)}while(o.length&&!0===r);return r}function ue(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(l(n)||_(n))&&n[o]&&(r=n[o])}return r}const ie="9.2.2",fe=-1,me="en-US",pe="",de=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let _e,ke,he;function ge(e){_e=e}function be(e){ke=e}function Te(e){he=e}let ye=null;const Le=e=>{ye=e},Ne=()=>ye;let Ee=null;const Ce=e=>{Ee=e},Ae=()=>Ee;let Oe=0;function xe(e={}){const t=i(e.version)?e.version:"9.2.2",n=i(e.locale)?e.locale:me,o=l(e.fallbackLocale)||_(e.fallbackLocale)||i(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,s=_(e.messages)?e.messages:{[n]:{}},p=_(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},d=_(e.numberFormats)?e.numberFormats:{[n]:{}},k=a({},e.modifiers||{},{upper:(e,t)=>"text"===t&&i(e)?e.toUpperCase():"vnode"===t&&m(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&i(e)?e.toLowerCase():"vnode"===t&&m(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&i(e)?de(e):"vnode"===t&&m(e)&&"__v_isVNode"in e?de(e.children):e}),h=e.pluralRules||{},g=u(e.missing)?e.missing:null,b=!f(e.missingWarn)&&!r(e.missingWarn)||e.missingWarn,T=!f(e.fallbackWarn)&&!r(e.fallbackWarn)||e.fallbackWarn,y=!!e.fallbackFormat,L=!!e.unresolving,N=u(e.postTranslation)?e.postTranslation:null,E=_(e.processor)?e.processor:null,C=!f(e.warnHtmlMessage)||e.warnHtmlMessage,A=!!e.escapeParameter,O=u(e.messageCompiler)?e.messageCompiler:_e,x=u(e.messageResolver)?e.messageResolver:ke||U,I=u(e.localeFallbacker)?e.localeFallbacker:he||ce,v=m(e.fallbackContext)?e.fallbackContext:void 0,P=u(e.onWarn)?e.onWarn:c,S=e,F=m(S.__datetimeFormatters)?S.__datetimeFormatters:new Map,w=m(S.__numberFormatters)?S.__numberFormatters:new Map,D=m(S.__meta)?S.__meta:{};Oe++;const M={version:t,cid:Oe,locale:n,fallbackLocale:o,messages:s,modifiers:k,pluralRules:h,missing:g,missingWarn:b,fallbackWarn:T,fallbackFormat:y,unresolving:L,postTranslation:N,processor:E,warnHtmlMessage:C,escapeParameter:A,messageCompiler:O,messageResolver:x,localeFallbacker:I,fallbackContext:v,onWarn:P,__meta:D};return M.datetimeFormats=p,M.numberFormats=d,M.__datetimeFormatters=F,M.__numberFormatters=w,M}function Ie(e,t){return e instanceof RegExp?e.test(t):e}function ve(e,t){return e instanceof RegExp?e.test(t):e}function Pe(e,t,n,r,o){const{missing:c,onWarn:a}=e;if(null!==c){const r=c(e,n,t,o);return i(r)?r:t}return t}function Se(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}const Fe=e=>e;let we=Object.create(null);function De(){we=Object.create(null)}function Me(e,t={}){{const n=(t.onCacheKey||Fe)(e),r=we[n];if(r)return r;let o=!1;const c=t.onError||g;t.onError=e=>{o=!0,c(e)};const{code:a}=F(e,t),s=new Function(`return ${a}`)();return o?s:we[n]=s}}let Re=k.__EXTEND_POINT__;const We=()=>++Re,$e={INVALID_ARGUMENT:Re,INVALID_DATE_ARGUMENT:We(),INVALID_ISO_DATE_ARGUMENT:We(),__EXTEND_POINT__:We()};function Ue(e){return h(e,null,void 0)}const je=()=>"",Ke=e=>u(e);function Ve(e,...t){const{fallbackFormat:r,postTranslation:o,unresolving:c,messageCompiler:a,fallbackLocale:u,messages:p}=e,[d,_]=He(...t),k=f(_.missingWarn)?_.missingWarn:e.missingWarn,h=f(_.fallbackWarn)?_.fallbackWarn:e.fallbackWarn,g=f(_.escapeParameter)?_.escapeParameter:e.escapeParameter,b=!!_.resolvedMessage,T=i(_.default)||f(_.default)?f(_.default)?a?d:()=>d:_.default:r?a?d:()=>d:"",y=r||""!==T,L=i(_.locale)?_.locale:e.locale;g&&function(e){l(e.list)?e.list=e.list.map((e=>i(e)?s(e):e)):m(e.named)&&Object.keys(e.named).forEach((t=>{i(e.named[t])&&(e.named[t]=s(e.named[t]))}))}(_);let[N,E,C]=b?[d,L,p[L]||{}]:Ge(e,d,L,u,h,k),A=N,O=d;if(b||i(A)||Ke(A)||y&&(A=T,O=A),!(b||(i(A)||Ke(A))&&i(E)))return c?-1:d;let x=!1;const I=Ke(A)?A:Be(e,d,E,A,O,(()=>{x=!0}));if(x)return A;const v=function(e,t,r,o){const{modifiers:c,pluralRules:a,messageResolver:s,fallbackLocale:l,fallbackWarn:u,missingWarn:f,fallbackContext:m}=e,p=n=>{let o=s(r,n);if(null==o&&m){const[,,e]=Ge(m,n,t,l,u,f);o=s(e,n)}if(i(o)){let r=!1;const c=Be(e,n,t,o,n,(()=>{r=!0}));return r?je:c}return Ke(o)?o:je},d={locale:t,modifiers:c,pluralRules:a,messages:p};e.processor&&(d.processor=e.processor);o.list&&(d.list=o.list);o.named&&(d.named=o.named);n(o.plural)&&(d.pluralIndex=o.plural);return d}(e,E,C,_),P=function(e,t,n){return t(n)}(0,I,z(v));return o?o(P,d):P}function Ge(e,t,n,r,o,c){const{messages:a,onWarn:s,messageResolver:l,localeFallbacker:f}=e,m=f(e,r,n);let p,d={},_=null;for(let n=0;n<m.length&&(p=m[n],d=a[p]||{},null===(_=l(d,t))&&(_=d[t]),!i(_)&&!u(_));n++){const n=Pe(e,t,p,0,"translate");n!==t&&(_=n)}return[_,p,d]}function Be(e,n,r,o,c,a){const{messageCompiler:s,warnHtmlMessage:l}=e;if(Ke(o)){const e=o;return e.locale=e.locale||r,e.key=e.key||n,e}if(null==s){const e=()=>o;return e.locale=r,e.key=n,e}const u=s(o,function(e,n,r,o,c,a){return{warnHtmlMessage:c,onError:e=>{throw a&&a(e),e},onCacheKey:e=>((e,n,r)=>t({l:e,k:n,s:r}))(n,r,e)}}(0,r,c,0,l,a));return u.locale=r,u.key=n,u.source=o,u}function He(...e){const[t,r,c]=e,s={};if(!i(t)&&!n(t)&&!Ke(t))throw Error($e.INVALID_ARGUMENT);const u=n(t)?String(t):(Ke(t),t);return n(r)?s.plural=r:i(r)?s.default=r:_(r)&&!o(r)?s.named=r:l(r)&&(s.list=r),n(c)?s.plural=c:i(c)?s.default=c:_(c)&&a(s,c),[u,s]}function Xe(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:c,onWarn:s,localeFallbacker:l}=e,{__datetimeFormatters:u}=e,[m,p,d,k]=Je(...t);f(d.missingWarn)?d.missingWarn:e.missingWarn;f(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn;const h=!!d.part,g=i(d.locale)?d.locale:e.locale,b=l(e,c,g);if(!i(m)||""===m)return new Intl.DateTimeFormat(g,k).format(p);let T,y={},L=null;for(let t=0;t<b.length&&(T=b[t],y=n[T]||{},L=y[m],!_(L));t++)Pe(e,m,T,0,"datetime format");if(!_(L)||!i(T))return r?-1:m;let N=`${T}__${m}`;o(k)||(N=`${N}__${JSON.stringify(k)}`);let E=u.get(N);return E||(E=new Intl.DateTimeFormat(T,a({},L,k)),u.set(N,E)),h?E.formatToParts(p):E.format(p)}const ze=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Je(...e){const[t,r,o,c]=e,a={};let s,l={};if(i(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error($e.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();s=new Date(n);try{s.toISOString()}catch(e){throw Error($e.INVALID_ISO_DATE_ARGUMENT)}}else if("[object Date]"===d(t)){if(isNaN(t.getTime()))throw Error($e.INVALID_DATE_ARGUMENT);s=t}else{if(!n(t))throw Error($e.INVALID_ARGUMENT);s=t}return i(r)?a.key=r:_(r)&&Object.keys(r).forEach((e=>{ze.includes(e)?l[e]=r[e]:a[e]=r[e]})),i(o)?a.locale=o:_(o)&&(l=o),_(c)&&(l=c),[a.key||"",s,a,l]}function Ye(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__datetimeFormatters.has(n)&&r.__datetimeFormatters.delete(n)}}function Ze(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:c,onWarn:s,localeFallbacker:l}=e,{__numberFormatters:u}=e,[m,p,d,k]=qe(...t);f(d.missingWarn)?d.missingWarn:e.missingWarn;f(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn;const h=!!d.part,g=i(d.locale)?d.locale:e.locale,b=l(e,c,g);if(!i(m)||""===m)return new Intl.NumberFormat(g,k).format(p);let T,y={},L=null;for(let t=0;t<b.length&&(T=b[t],y=n[T]||{},L=y[m],!_(L));t++)Pe(e,m,T,0,"number format");if(!_(L)||!i(T))return r?-1:m;let N=`${T}__${m}`;o(k)||(N=`${N}__${JSON.stringify(k)}`);let E=u.get(N);return E||(E=new Intl.NumberFormat(T,a({},L,k)),u.set(N,E)),h?E.formatToParts(p):E.format(p)}const Qe=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function qe(...e){const[t,r,o,c]=e,a={};let s={};if(!n(t))throw Error($e.INVALID_ARGUMENT);const l=t;return i(r)?a.key=r:_(r)&&Object.keys(r).forEach((e=>{Qe.includes(e)?s[e]=r[e]:a[e]=r[e]})),i(o)?a.locale=o:_(o)&&(s=o),_(c)&&(s=c),[a.key||"",l,a,s]}function et(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__numberFormatters.has(n)&&r.__numberFormatters.delete(n)}}export{k as CompileErrorCodes,$e as CoreErrorCodes,ne as CoreWarnCodes,ze as DATETIME_FORMAT_OPTIONS_KEYS,me as DEFAULT_LOCALE,G as DEFAULT_MESSAGE_DATA_TYPE,pe as MISSING_RESOLVE_VALUE,fe as NOT_REOSLVED,Qe as NUMBER_FORMAT_OPTIONS_KEYS,ie as VERSION,De as clearCompileCache,Ye as clearDateTimeFormat,et as clearNumberFormat,Me as compileToFunction,h as createCompileError,xe as createCoreContext,Ue as createCoreError,z as createMessageContext,Xe as datetime,ae as fallbackWithLocaleChain,ce as fallbackWithSimple,Ne as getAdditionalMeta,Q as getDevToolsHook,Ae as getFallbackContext,oe as getWarnMessage,Pe as handleMissing,q as initI18nDevTools,Ke as isMessageFunction,Ie as isTranslateFallbackWarn,ve as isTranslateMissingWarn,Ze as number,W as parse,Je as parseDateTimeArgs,qe as parseNumberArgs,He as parseTranslateArgs,Te as registerLocaleFallbacker,ge as registerMessageCompiler,be as registerMessageResolver,j as resolveValue,U as resolveWithKeyValue,Le as setAdditionalMeta,Z as setDevToolsHook,Ce as setFallbackContext,Ve as translate,ee as translateDevTools,Se as updateFallbackLocale};
