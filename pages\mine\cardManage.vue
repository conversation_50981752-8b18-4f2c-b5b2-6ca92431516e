<template>
	<view class="container">
		<view class="list_wrap">
			<view class="list" v-for="(item,index) in bankCardList" :key="index">
				<view class="list-top">
					<view class="list-left"><image src="../../static/img/card-icon.png" />{{item.bank_name}}</view>
					<view class="list-right">
						<view class="list-mo" v-if='item.status==1' @click="setBankActive(item)">{{i18n.mo}}</view>
						<view class="list-del" @click="maskTouchend(item)">{{i18n.del}}</view>
					</view>
				</view>
				<view class="list-foot">{{item.bank_id | cardFilter}}</view>
			</view>
		</view>
		<view class="add_card" @click="addCard">{{i18n.add}}</view>
	</view>
</template>

<script>
	export default {
        name: 'cardManage',
		data() {
			return {
				touchNum:0,
				bankCardList:[],
				userInfo: {}
			}
		},

        onLoad(){

        },
        computed: {
            i18n () {
               return this.$t("cardMange")
            }
        },
        onShow(){
			this.getUserInfo();

        },
        onReady(){
        	uni.setNavigationBarTitle({
        		title:this.i18n.head_title
        	})
        },
        filters:{
			cardFilter(num){
				if(num){
					return '**** **** **** ' + num.substring(num.length - 4)
				}
			}
		},
		methods: {
            // 获取用户信息
            getUserInfo(){
                this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
                    if(res.status == 200){
                        this.userInfo = res.data;
						this.getBankCardList();
						uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
            // 获取银行卡列表
            getBankCardList(){
                this.$tools.Post("api/user/bank/get", {
					uid: this.userInfo.uid,
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
                    if(res.status == 200){
                        this.bankCardList = [...res.data]
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
            maskTouchend(item){
				this.touchNum ++
				setTimeout(()=>{
					if(this.touchNum == 1){
						this.showDetaleModal("delete",this.i18n.delCard,item)
					}
					if(this.touchNum >= 2){
						this.showDetaleModal("set",this.i18n.moren,item)
					}
					this.touchNum = 0
				},250)
			},

            showDetaleModal(type,content,item){
				let that = this;
                uni.showModal({
                    content: content,
                    success: function (res) {
                        if (res.confirm) {
                            if(type == "delete"){
								that.deleteBankCard(item)
							}
							else if(type=="set"){
								that.setBankActive(item)
							}
                        } else if (res.cancel) {

                        }
                    }
                });
            },

            //删除银行卡
            deleteBankCard(item){
               let that = this;
                let pamars = {bank_id:item.bank_id, api_token: uni.getStorageSync('token'), language: uni.getStorageSync('lang')}
                this.$tools.Post("api/user/bank/delete",pamars).then((res) =>{
                    if(res.status == 200){
                        uni.showToast({
                            title: this.i18n.succ,
                            duration: 1500,
                            icon:'success',
                            success(){
                                that.getBankCardList()
                            }
                        });
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },

			//设为默认银行卡
			setBankActive(item){
			   let that = this;
			    let pamars = {bank_id:item.bank_id, api_token: uni.getStorageSync('token'), language: uni.getStorageSync('lang')}
			    this.$tools.Post("api/user/bank/default",pamars).then((res) =>{
			        if(res.status == 200){
			            uni.showToast({
			                title: this.i18n.set,
			                duration: 1500,
			                icon:'success',
			                success(){
			                    that.getBankCardList()
			                }
			            });
			        } else {
			            uni.showToast({
			                title: res.msg,
			                duration: 1500,
			                icon:'none'
			            });
			        }
			    })
			},

			addCard(){
				uni.navigateTo({
				    url: '/pages/mine/bankList'
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	page{
		background: #fff;
		.container{
			.list_wrap{
				padding: 0 30rpx;
				.list{
					position: relative;
					display: flex;
					flex-direction: column;
					justify-content: flex-end;
					width: 100%;
					height: 200rpx;
					background: #172D52;
					border-radius: 20rpx;
					margin-top: 20rpx;
					padding: 0 32rpx;
					box-sizing: border-box;
					.list-foot{
						height: 128rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 36rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #FFFFFF;
					}
					.list-top{
						height: 42rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;
					}
					.list-left{
						height: 42rpx;
						font-size: 32rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #FFFFFF;
						display: flex;
						align-items: center;
						image{
							width: 68rpx;
							height: 42rpx;
							margin-right: 11rpx;
						}
					}
					.list-right{
						height: 42rpx;
						display: flex;
						align-items: center;
						justify-content: flex-end;
					}
					.list-mo{
						width: 82rpx;
						height: 38rpx;
						background: #172D52;
						background-size: 100% 100%;
						background-repeat: no-repeat;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #172D52;
					}
					.list-del{
						width: 82rpx;
						height: 38rpx;
						background: #172D52;
						background-size: 100% 100%;
						background-repeat: no-repeat;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #fff;
						margin-left: 17rpx;
					}
				}
			}
			.tips{
				margin: 20rpx 30rpx 114rpx 30rpx;
				padding: 14rpx 24rpx;
				background: #E0E0E0;
				border: 1rpx solid #B3B3B3;
				border-radius: 10rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #999999;
			}
			.add_card{
				height: 120rpx;
				margin: 20rpx 30rpx 0 30rpx;
				background: #172D52;
				background-repeat: no-repeat;
				background-size: 100% 100%;
				font-size: 36rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				padding-right: 76rpx;
				box-sizing: border-box;
			}
		}
	}

        /deep/ .uni-modal{
            width: 66%;
        }

</style>
