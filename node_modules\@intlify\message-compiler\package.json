{"name": "@intlify/message-compiler", "version": "9.2.2", "description": "@intlify/message-compiler", "keywords": ["compiler", "i18n", "internationalization", "intlify", "message-format"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/message-compiler#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/message-compiler"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "index.mjs", "dist"], "main": "index.js", "module": "dist/message-compiler.esm-bundler.js", "unpkg": "dist/message-compiler.global.js", "jsdelivr": "dist/message-compiler.global.js", "types": "dist/message-compiler.d.ts", "dependencies": {"@intlify/shared": "9.2.2", "source-map": "0.6.1"}, "engines": {"node": ">= 14"}, "buildOptions": {"name": "IntlifyMessageCompiler", "formats": ["esm-bundler", "esm-browser", "cjs", "global"], "enableFullBundleForEsmBrowser": true}, "exports": {".": {"import": {"node": "./index.mjs", "default": "./dist/message-compiler.esm-bundler.js"}, "require": "./index.js"}, "./dist/*": "./dist/*", "./index.mjs": "./index.mjs", "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "sideEffects": false}