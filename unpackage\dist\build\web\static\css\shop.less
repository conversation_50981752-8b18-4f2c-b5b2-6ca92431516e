.business_listWrap{
	padding-top: 159rpx;
}
.top{
	height: 260rpx;
	background: #000000;
	border-radius: 40rpx;
	position: relative;
	margin: 0 20rpx 0 20rpx;
	z-index: 1;
	image{
		width: 100%;
		height: 260rpx;
		object-fit: cover;
		border-radius: 40rpx;
	}
}
.boxTop{
	position: absolute;
	width: 100%;
	z-index: 2;
	height: 170rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	top: 60rpx;
	left: 0;
	.boxTop-item{
		width: 140rpx;
		height: 170rpx;
		border-radius: 40rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 0 20rpx;
		margin: 0 24rpx;
		box-sizing: border-box;
		background: RGBA(239, 12, 12, 1);
		span{
			width: 100rpx;
			height: 100rpx;
			border-radius: 30rpx;
			background: #fff;
			font-size: 68rpx;
			font-family: Euclid Circular A;
			font-weight: 500;
			color: #EF0C0C;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 10rpx;
		}
	}
}
.boxFoot{
	width: 670rpx;
	height: 140rpx;
	background: url("../../static/img/business-top.png");
	background-repeat: no-repeat;
	background-size: 100% 100%;
	position: absolute;
	left: calc(50% - 335rpx);
	top: -110rpx;
	.boxFoot-top{
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
	}
	.boxFoot-line{
		height: 70rpx;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #172D52;
		display: flex;
		align-items: center;
		justify-content: center;
		span{
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #fff;
		}
	}
	.red{
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: rgba(255, 240, 0, 1);
	}
	.blue{
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #7DFF25;
		margin-left: 5rpx;
	}
}
.boxSelect{
	height: 114rpx;
	padding: 0 10rpx;
	display: flex;
	align-items: center;
	.boxSelect-item{
		height: 68rpx;
		background: #DFE5EF;
		border-radius: 20rpx;
		margin: 0 10rpx;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #172D52;
		padding: 0 22rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.active{
		background: rgba(101, 177, 29, 1);
		color: #fff;
	}
}
.boxNum{
	font-size: 32rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: #333333;
	padding: 0 23rpx;
}
.boxDown{
	height: 258rpx;
	background: #172D52;
	box-shadow: 0 1rpx 0 0 #EDEFF0;
	border-radius: 40rpx 40rpx 0 0;
	padding: 0 35rpx;
	margin-top: 40rpx;
}
.boxBottom{
	height: 110rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.boxBottom-left{
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		height: 110rpx;
		display: flex;
		align-items: center;
		.boxBottom-box{
			width: 180rpx;
			height: 68rpx;
			background: #DFE5EF;
			border-radius: 20rpx;
			margin-left: 21rpx;
			display: flex;
			align-items: center;
			.numbers{
				width: 100%;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #172D52;
			}
		}
	}
	.boxBottom-right{
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
	}
}
.boxBtn{
	height: 100rpx;
	background: #65B11D;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
}
.boxMiddle{
	margin: 21rpx 8rpx 0 8rpx;
	display: flex;
	flex-wrap: wrap;
	.boxMiddle-item{
		/* 修改此处：将 /3 改为 /4 */
		width: calc(100% / 4 - 24rpx); /* 关键修改 */
		height: 220rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 10rpx 12rpx;
		position: relative;
		background: #fff;
		padding: 20rpx;
		box-sizing: border-box;
		image{
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
		span{
			width: 100%;
			height: 100%;
			display: none;
			background: url("../../static/img/business-on.png");
			background-repeat: no-repeat;
			background-size: 100% 100%;
			position: absolute;
			left: 0;
			top: 0;
		}
	}
	.boxMiddle-item-on{
		span{
			display: block;
		}
	}
}

.popupBox{
	width: 580rpx;
	height: 540rpx;
	background: url("../../static/img/business-box.png");
	background-size: 100% 100%;
	background-repeat: no-repeat;
	padding: 0 35rpx;
	box-sizing: border-box;
	.popupHead{
		height: 155rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #333333;
	}
	.content{
		height: 220rpx;
		background: #FFFFFF;
		box-shadow: 0 4rpx 10rpx 0 rgba(0,0,0,0.25);
		border-left: 10rpx solid rgba(110, 168, 63, 1);
		padding: 0 21rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		.item{
			display: flex;
			padding: 10rpx 0;
			.left{
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #333333;
			}
			.right{
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #333333;
				display: flex;
				align-items: center;
				view{
					margin-right: 10rpx;
				}
			}
		}
	}
	.btnBox{
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 110rpx;
		margin-top: 30rpx;
		.cancelBtn{
			width: 246rpx;
			height: 100rpx;
			background: #FFFFFF;
			border: 1rpx solid #65B11D;
			border-radius: 20rpx;
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #65B11D;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.confirmBtn{
			width: 246rpx;
			height: 100rpx;
			background: #65B11D;
			border-radius: 20rpx;
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
.openCodeBox{
	margin: 30rpx 20rpx 16rpx;
	height: 120rpx;
	background: #FFFFFF;
	border-radius: 5px;
	padding: 0 20rpx;
	position: relative;
	.top{
		position: absolute;
		top: -10rpx;
		min-width: 230rpx;
		height: 64rpx;
		background-size: 100% 100%;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-right: 20rpx;
		.jiang{
			position: absolute;
			top: -18rpx;
			left: 16rpx;
			width:64rpx;
			height: 68rpx;
		}
		font-size: 14px;
		font-family: PingFang SC;
		font-weight: 800;
		color: #FFEFBD;
	}
	.bottom{
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: absolute;
		bottom: 14rpx;
		width: calc(100% - 40rpx);
		.left{
			font-size: 16px;
			font-family: Euclid Circular A;
			font-weight: 500;
			color: #333333;
		}
		.right{
			display: flex;
			align-items: center;
			font-size: 14px;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FF0000;
			image{
				margin-left: 10rpx;
			}
		}
	}
}
