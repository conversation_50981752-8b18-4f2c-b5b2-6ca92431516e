<template>
    <view class="error-boundary">
        <template v-if="hasError && shouldShowError">
            <view class="error-content">
                <view class="error-icon">⚠️</view>
                <view class="error-message">{{ errorMessage }}</view>
                <view class="error-retry" @tap="handleRetry">
                    重试
                </view>
            </view>
        </template>
        <template v-else>
            <slot></slot>
        </template>
    </view>
</template>

<script>
export default {
    name: 'error-boundary',
    data() {
        return {
            hasError: false,
            errorMessage: '',
            shouldShowError: true
        }
    },
    created() {
        // 检查当前页面是否是登录页
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        this.shouldShowError = !(currentPage && currentPage.route && currentPage.route.includes('login'));
    },
    methods: {
        handleError(error) {
            this.hasError = true;
            // 只在非登录页显示错误
            if (this.shouldShowError) {
                this.errorMessage = error.message || '发生错误，请重试';
                console.error('组件错误:', error);
            }
        },
        handleRetry() {
            this.hasError = false;
            this.errorMessage = '';
            this.$emit('retry');
        }
    },
    errorCaptured(err, vm, info) {
        this.handleError(err);
        return false; // 阻止错误继续向上传播
    }
}
</script>

<style lang="scss" scoped>
.error-boundary {
    width: 100%;
    height: 100%;
    
    .error-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        text-align: center;
        
        .error-icon {
            font-size: 40px;
            margin-bottom: 10px;
        }
        
        .error-message {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .error-retry {
            background-color: #2979ff;
            color: #fff;
            padding: 8px 20px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            
            &:active {
                opacity: 0.8;
            }
        }
    }
}
</style> 