<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\StaffCode;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use App\Models\StaffCode as Code;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;
class StaffCodeController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new StaffCode(), function (Grid $grid) {
            $grid->withBorder();
            $grid->disableCreateButton();
            $roles = Admin::user()->roles; //获取权限分组
            $admin = Auth::guard('admin')->user(); 
            if($roles[0]->slug=='staff'){
                $grid->model()->where('uid',$admin->id);
            }
      
            if($roles[0]->slug=='team'){

                $team = DB::table('admin_users')->where('team_id',$admin->team_id)->get();
                $list = [];
                for($i=0;$i<count($team);$i++){
                    if($team[$i]->id != $admin->id){
                        array_push($list,$team[$i]->id);
                    }
                }
                $grid->model()->whereIn('uid',$list);
            }
            

            
            $grid->column('id')->sortable();
            $grid->column('uid','员工')->display(function ($res) {

                $where = [
                    ['id',$this->uid]
                 ];
                $res = DB::table('admin_users')->where($where)->first();
                $name = ($res) ? $res->username : '暂无';
                return $name;

            });

            $grid->column('code');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
           // $grid->enableDialogCreate(); //开启 弹窗新增
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                 $actions->disableDelete();
                 $actions->disableEdit();  //禁用普通编辑 
            
                 $actions->disableView();

                 $roles = Admin::user()->roles; //获取权限分组
                 if($roles[0]->slug=='staff'){
                     $actions->QuickEdit();    // 启用快速编辑（弹窗）
                 }
             });
          


            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new StaffCode(), function (Show $show) {
            $show->field('id');
            $show->field('uid');
            $show->field('code');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new StaffCode(), function (Form $form) {
      
            
            
            $form->hidden('uid');
            if($form->isCreating()){  //新增

                $admin = Auth::guard('admin')->user(); 
                $form->text('code');
                
                $form->uid = $admin->id;
  
            }

            if($form->isEditing()){  //编辑

                $form->text('code');
            }

            $form->submitted(function (Form $form) {
               

                    if (Code::where('code', $form->code)->where('code', '>', '0')->exists()) {
                        $form->responseValidationMessages('code', '邀请码已存在');
                    }
                
            });
 
            $form->confirm('您确定要提交吗？', $form->code);



        });
    }
}
