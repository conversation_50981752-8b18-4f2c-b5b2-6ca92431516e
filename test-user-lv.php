<?php
/**
 * 测试用户等级接口 (user/lv)
 * 验证GET和POST方法都能正常工作
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 测试配置
$baseUrl = 'http://localhost/admin.inspap.com/api/'; // 根据实际情况修改

echo "<h1>用户等级接口测试报告</h1>\n";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";

/**
 * 发送HTTP请求
 */
function makeRequest($url, $method = 'GET', $data = [], $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // 设置请求头
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if (!empty($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    } elseif ($method === 'GET' && !empty($data)) {
        $url .= '?' . http_build_query($data);
        curl_setopt($ch, CURLOPT_URL, $url);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'httpCode' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

/**
 * 测试用户等级接口 - GET方法
 */
function testUserLvGET($baseUrl) {
    echo "<h2>1. 用户等级接口测试 - GET方法</h2>\n";
    
    $testParams = [
        'api_token' => 'test_token_12345',
        'language' => 'CN'
    ];
    
    $result = makeRequest($baseUrl . 'user/lv', 'GET', $testParams);
    
    echo "<p><strong>请求URL:</strong> {$baseUrl}user/lv</p>\n";
    echo "<p><strong>请求方法:</strong> GET</p>\n";
    echo "<p><strong>请求参数:</strong> " . http_build_query($testParams) . "</p>\n";
    echo "<p><strong>HTTP状态码:</strong> {$result['httpCode']}</p>\n";
    
    if ($result['httpCode'] === 200) {
        echo "<p style='color: green;'>✅ GET方法请求成功</p>\n";
        
        $data = json_decode($result['response'], true);
        if ($data) {
            echo "<p><strong>响应数据:</strong></p>\n";
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
            
            if (isset($data['status']) && $data['status'] === 200) {
                echo "<p style='color: green;'>✅ 响应格式正确</p>\n";
                
                if (isset($data['data']) && is_array($data['data'])) {
                    $count = count($data['data']);
                    echo "<p style='color: green;'>✅ 获取到 {$count} 个等级数据</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠️ 等级数据为空或格式异常</p>\n";
                }
            } else {
                echo "<p style='color: red;'>❌ 响应格式错误</p>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ 响应数据解析失败</p>\n";
            echo "<p><strong>原始响应:</strong> " . htmlspecialchars($result['response']) . "</p>\n";
        }
    } elseif ($result['httpCode'] === 405) {
        echo "<p style='color: red;'>❌ 方法不允许 (405) - GET方法不支持</p>\n";
    } elseif ($result['httpCode'] === 404) {
        echo "<p style='color: red;'>❌ 接口不存在 (404)</p>\n";
    } else {
        echo "<p style='color: red;'>❌ HTTP错误: {$result['httpCode']}</p>\n";
        if ($result['error']) {
            echo "<p style='color: red;'>错误信息: {$result['error']}</p>\n";
        }
        echo "<p><strong>响应内容:</strong> " . htmlspecialchars($result['response']) . "</p>\n";
    }
    
    echo "<hr>\n";
}

/**
 * 测试用户等级接口 - POST方法
 */
function testUserLvPOST($baseUrl) {
    echo "<h2>2. 用户等级接口测试 - POST方法</h2>\n";
    
    $testParams = [
        'api_token' => 'test_token_12345',
        'language' => 'CN'
    ];
    
    $result = makeRequest($baseUrl . 'user/lv', 'POST', $testParams);
    
    echo "<p><strong>请求URL:</strong> {$baseUrl}user/lv</p>\n";
    echo "<p><strong>请求方法:</strong> POST</p>\n";
    echo "<p><strong>请求参数:</strong> " . http_build_query($testParams) . "</p>\n";
    echo "<p><strong>HTTP状态码:</strong> {$result['httpCode']}</p>\n";
    
    if ($result['httpCode'] === 200) {
        echo "<p style='color: green;'>✅ POST方法请求成功</p>\n";
        
        $data = json_decode($result['response'], true);
        if ($data) {
            echo "<p><strong>响应数据:</strong></p>\n";
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
            
            if (isset($data['status']) && $data['status'] === 200) {
                echo "<p style='color: green;'>✅ 响应格式正确</p>\n";
                
                if (isset($data['data']) && is_array($data['data'])) {
                    $count = count($data['data']);
                    echo "<p style='color: green;'>✅ 获取到 {$count} 个等级数据</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠️ 等级数据为空或格式异常</p>\n";
                }
            } else {
                echo "<p style='color: red;'>❌ 响应格式错误</p>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ 响应数据解析失败</p>\n";
            echo "<p><strong>原始响应:</strong> " . htmlspecialchars($result['response']) . "</p>\n";
        }
    } elseif ($result['httpCode'] === 405) {
        echo "<p style='color: red;'>❌ 方法不允许 (405) - POST方法不支持</p>\n";
        echo "<p><strong>建议:</strong> 需要在路由中添加POST方法支持</p>\n";
    } elseif ($result['httpCode'] === 404) {
        echo "<p style='color: red;'>❌ 接口不存在 (404)</p>\n";
    } else {
        echo "<p style='color: red;'>❌ HTTP错误: {$result['httpCode']}</p>\n";
        if ($result['error']) {
            echo "<p style='color: red;'>错误信息: {$result['error']}</p>\n";
        }
        echo "<p><strong>响应内容:</strong> " . htmlspecialchars($result['response']) . "</p>\n";
    }
    
    echo "<hr>\n";
}

/**
 * 测试总结
 */
function testSummary() {
    echo "<h2>3. 测试总结</h2>\n";
    
    echo "<h3>✅ 修复的问题:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>405 Method Not Allowed</strong> - 为 /api/user/lv 接口添加了POST方法支持</li>\n";
    echo "</ul>\n";
    
    echo "<h3>🔧 技术实现:</h3>\n";
    echo "<ul>\n";
    echo "<li>在路由文件中添加: <code>Route::post('user/lv','ApiController@get_user_lv');</code></li>\n";
    echo "<li>保持原有GET方法支持，确保向后兼容</li>\n";
    echo "<li>使用相同的控制器方法处理GET和POST请求</li>\n";
    echo "</ul>\n";
    
    echo "<h3>📋 前端调用情况:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>pages/mine/mine.vue</strong> - 使用POST方法调用</li>\n";
    echo "<li><strong>pages/mine/level.vue</strong> - 使用GET方法调用</li>\n";
    echo "<li>现在两种调用方式都应该正常工作</li>\n";
    echo "</ul>\n";
    
    echo "<h3>🚀 下一步建议:</h3>\n";
    echo "<ul>\n";
    echo "<li>在前端测试页面验证接口修复效果</li>\n";
    echo "<li>检查生产环境中的接口调用情况</li>\n";
    echo "<li>考虑统一前端调用方式（建议都使用GET方法）</li>\n";
    echo "</ul>\n";
}

// 运行所有测试
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
hr { margin: 20px 0; border: 1px solid #ddd; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
</style>\n";

testUserLvGET($baseUrl);
testUserLvPOST($baseUrl);
testSummary();

echo "<p><strong>测试完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
