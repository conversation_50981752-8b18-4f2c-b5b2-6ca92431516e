/*!
  * vue-i18n v9.2.2
  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";const a="undefined"!=typeof window,n="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,l=e=>n?Symbol(e):e,r=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),o=e=>"number"==typeof e&&isFinite(e),s=e=>"[object RegExp]"===h(e),c=e=>E(e)&&0===Object.keys(e).length;function i(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const u=Object.assign;function m(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const f=Object.prototype.hasOwnProperty;function g(e,t){return f.call(e,t)}const _=Array.isArray,p=e=>"function"==typeof e,v=e=>"string"==typeof e,b=e=>"boolean"==typeof e,d=e=>null!==e&&"object"==typeof e,k=Object.prototype.toString,h=e=>k.call(e),E=e=>"[object Object]"===h(e),L=15;function F(e,t,a={}){const{domain:n,messages:l,args:r}=a,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=n,o}const y=[];y[0]={w:[0],i:[3,0],"[":[4],o:[7]},y[1]={w:[1],".":[2],"[":[4],o:[7]},y[2]={w:[2],i:[3,0],0:[3,0]},y[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},y[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},y[5]={"'":[4,0],o:8,l:[5,0]},y[6]={'"':[4,0],o:8,l:[6,0]};const I=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function T(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function N(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(a=t,I.test(a)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var a}const R=new Map;function W(e,t){return d(e)?e[t]:null}const M=e=>e,O=e=>"",w=e=>0===e.length?"":e.join(""),D=e=>null==e?"":_(e)||E(e)&&e.toString===k?JSON.stringify(e,null,2):String(e);function C(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function P(e={}){const t=e.locale,a=function(e){const t=o(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(o(e.named.count)||o(e.named.n))?o(e.named.count)?e.named.count:o(e.named.n)?e.named.n:t:t}(e),n=d(e.pluralRules)&&v(t)&&p(e.pluralRules[t])?e.pluralRules[t]:C,l=d(e.pluralRules)&&v(t)&&p(e.pluralRules[t])?C:void 0,r=e.list||[],s=e.named||{};o(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(a,s);function c(t){const a=p(e.messages)?e.messages(t):!!d(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):O)}const i=E(e.processor)&&p(e.processor.normalize)?e.processor.normalize:w,u=E(e.processor)&&p(e.processor.interpolate)?e.processor.interpolate:D,m={list:e=>r[e],named:e=>s[e],plural:e=>e[n(a,e.length,l)],linked:(t,...a)=>{const[n,l]=a;let r="text",o="";1===a.length?d(n)?(o=n.modifier||o,r=n.type||r):v(n)&&(o=n||o):2===a.length&&(v(n)&&(o=n||o),v(l)&&(r=l||r));let s=c(t)(m);return"vnode"===r&&_(s)&&o&&(s=s[0]),o?(i=o,e.modifiers?e.modifiers[i]:M)(s,r):s;var i},message:c,type:E(e.processor)&&v(e.processor.type)?e.processor.type:"text",interpolate:u,normalize:i};return m}function A(e,t,a){return[...new Set([a,..._(t)?t:d(t)?Object.keys(t):v(t)?[t]:[a]])]}function S(e,t,a){const n=v(a)?a:j,l=e;l.__localeChainCache||(l.__localeChainCache=new Map);let r=l.__localeChainCache.get(n);if(!r){r=[];let e=[a];for(;_(e);)e=$(r,e,t);const o=_(t)||!E(t)?t:t.default?t.default:null;e=v(o)?[o]:o,_(e)&&$(r,e,!1),l.__localeChainCache.set(n,r)}return r}function $(e,t,a){let n=!0;for(let l=0;l<t.length&&b(n);l++){const r=t[l];v(r)&&(n=U(e,t[l],a))}return n}function U(e,t,a){let n;const l=t.split("-");do{n=H(e,l.join("-"),a),l.splice(-1,1)}while(l.length&&!0===n);return n}function H(e,t,a){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const l=t.replace(/!/g,"");e.push(l),(_(a)||E(a))&&a[l]&&(n=a[l])}return n}const j="en-US",V=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let x,G;let B=0;function Y(e={}){const t=v(e.version)?e.version:"9.2.2",a=v(e.locale)?e.locale:j,n=_(e.fallbackLocale)||E(e.fallbackLocale)||v(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,l=E(e.messages)?e.messages:{[a]:{}},r=E(e.datetimeFormats)?e.datetimeFormats:{[a]:{}},o=E(e.numberFormats)?e.numberFormats:{[a]:{}},c=u({},e.modifiers||{},{upper:(e,t)=>"text"===t&&v(e)?e.toUpperCase():"vnode"===t&&d(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&v(e)?e.toLowerCase():"vnode"===t&&d(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&v(e)?V(e):"vnode"===t&&d(e)&&"__v_isVNode"in e?V(e.children):e}),m=e.pluralRules||{},f=p(e.missing)?e.missing:null,g=!b(e.missingWarn)&&!s(e.missingWarn)||e.missingWarn,k=!b(e.fallbackWarn)&&!s(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,L=!!e.unresolving,F=p(e.postTranslation)?e.postTranslation:null,y=E(e.processor)?e.processor:null,I=!b(e.warnHtmlMessage)||e.warnHtmlMessage,T=!!e.escapeParameter,N=p(e.messageCompiler)?e.messageCompiler:undefined,R=p(e.messageResolver)?e.messageResolver:x||W,M=p(e.localeFallbacker)?e.localeFallbacker:G||A,O=d(e.fallbackContext)?e.fallbackContext:void 0,w=p(e.onWarn)?e.onWarn:i,D=e,C=d(D.__datetimeFormatters)?D.__datetimeFormatters:new Map,P=d(D.__numberFormatters)?D.__numberFormatters:new Map,S=d(D.__meta)?D.__meta:{};B++;const $={version:t,cid:B,locale:a,fallbackLocale:n,messages:l,modifiers:c,pluralRules:m,missing:f,missingWarn:g,fallbackWarn:k,fallbackFormat:h,unresolving:L,postTranslation:F,processor:y,warnHtmlMessage:I,escapeParameter:T,messageCompiler:N,messageResolver:R,localeFallbacker:M,fallbackContext:O,onWarn:w,__meta:S};return $.datetimeFormats=r,$.numberFormats=o,$.__datetimeFormatters=C,$.__numberFormatters=P,$}function X(e,t,a,n,l){const{missing:r,onWarn:o}=e;if(null!==r){const n=r(e,a,t,l);return v(n)?n:t}return t}function z(e,t,a){e.__localeChainCache=new Map,e.localeFallbacker(e,a,t)}let J=L;const q=()=>++J,Z={INVALID_ARGUMENT:J,INVALID_DATE_ARGUMENT:q(),INVALID_ISO_DATE_ARGUMENT:q(),__EXTEND_POINT__:q()},K=()=>"",Q=e=>p(e);function ee(e,...t){const{fallbackFormat:a,postTranslation:n,unresolving:l,messageCompiler:r,fallbackLocale:s,messages:c}=e,[i,u]=ne(...t),f=b(u.missingWarn)?u.missingWarn:e.missingWarn,g=b(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,p=b(u.escapeParameter)?u.escapeParameter:e.escapeParameter,k=!!u.resolvedMessage,h=v(u.default)||b(u.default)?b(u.default)?r?i:()=>i:u.default:a?r?i:()=>i:"",E=a||""!==h,L=v(u.locale)?u.locale:e.locale;p&&function(e){_(e.list)?e.list=e.list.map((e=>v(e)?m(e):e)):d(e.named)&&Object.keys(e.named).forEach((t=>{v(e.named[t])&&(e.named[t]=m(e.named[t]))}))}(u);let[F,y,I]=k?[i,L,c[L]||{}]:te(e,i,L,s,g,f),T=F,N=i;if(k||v(T)||Q(T)||E&&(T=h,N=T),!(k||(v(T)||Q(T))&&v(y)))return l?-1:i;let R=!1;const W=Q(T)?T:ae(e,i,y,T,N,(()=>{R=!0}));if(R)return T;const M=function(e,t,a,n){const{modifiers:l,pluralRules:r,messageResolver:s,fallbackLocale:c,fallbackWarn:i,missingWarn:u,fallbackContext:m}=e,f=n=>{let l=s(a,n);if(null==l&&m){const[,,e]=te(m,n,t,c,i,u);l=s(e,n)}if(v(l)){let a=!1;const r=ae(e,n,t,l,n,(()=>{a=!0}));return a?K:r}return Q(l)?l:K},g={locale:t,modifiers:l,pluralRules:r,messages:f};e.processor&&(g.processor=e.processor);n.list&&(g.list=n.list);n.named&&(g.named=n.named);o(n.plural)&&(g.pluralIndex=n.plural);return g}(e,y,I,u),O=function(e,t,a){return t(a)}(0,W,P(M));return n?n(O,i):O}function te(e,t,a,n,l,r){const{messages:o,onWarn:s,messageResolver:c,localeFallbacker:i}=e,u=i(e,n,a);let m,f={},g=null;for(let a=0;a<u.length&&(m=u[a],f=o[m]||{},null===(g=c(f,t))&&(g=f[t]),!v(g)&&!p(g));a++){const a=X(e,t,m,0,"translate");a!==t&&(g=a)}return[g,m,f]}function ae(e,t,a,n,l,o){const{messageCompiler:s,warnHtmlMessage:c}=e;if(Q(n)){const e=n;return e.locale=e.locale||a,e.key=e.key||t,e}if(null==s){const e=()=>n;return e.locale=a,e.key=t,e}const i=s(n,function(e,t,a,n,l,o){return{warnHtmlMessage:l,onError:e=>{throw o&&o(e),e},onCacheKey:e=>((e,t,a)=>r({l:e,k:t,s:a}))(t,a,e)}}(0,a,l,0,c,o));return i.locale=a,i.key=t,i.source=n,i}function ne(...e){const[t,a,n]=e,l={};if(!v(t)&&!o(t)&&!Q(t))throw Error(Z.INVALID_ARGUMENT);const r=o(t)?String(t):(Q(t),t);return o(a)?l.plural=a:v(a)?l.default=a:E(a)&&!c(a)?l.named=a:_(a)&&(l.list=a),o(n)?l.plural=n:v(n)?l.default=n:E(n)&&u(l,n),[r,l]}function le(e,...t){const{datetimeFormats:a,unresolving:n,fallbackLocale:l,onWarn:r,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[i,m,f,g]=oe(...t);b(f.missingWarn)?f.missingWarn:e.missingWarn;b(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const _=!!f.part,p=v(f.locale)?f.locale:e.locale,d=o(e,l,p);if(!v(i)||""===i)return new Intl.DateTimeFormat(p,g).format(m);let k,h={},L=null;for(let t=0;t<d.length&&(k=d[t],h=a[k]||{},L=h[i],!E(L));t++)X(e,i,k,0,"datetime format");if(!E(L)||!v(k))return n?-1:i;let F=`${k}__${i}`;c(g)||(F=`${F}__${JSON.stringify(g)}`);let y=s.get(F);return y||(y=new Intl.DateTimeFormat(k,u({},L,g)),s.set(F,y)),_?y.formatToParts(m):y.format(m)}const re=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function oe(...e){const[t,a,n,l]=e,r={};let s,c={};if(v(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(Z.INVALID_ISO_DATE_ARGUMENT);const a=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();s=new Date(a);try{s.toISOString()}catch(e){throw Error(Z.INVALID_ISO_DATE_ARGUMENT)}}else if("[object Date]"===h(t)){if(isNaN(t.getTime()))throw Error(Z.INVALID_DATE_ARGUMENT);s=t}else{if(!o(t))throw Error(Z.INVALID_ARGUMENT);s=t}return v(a)?r.key=a:E(a)&&Object.keys(a).forEach((e=>{re.includes(e)?c[e]=a[e]:r[e]=a[e]})),v(n)?r.locale=n:E(n)&&(c=n),E(l)&&(c=l),[r.key||"",s,r,c]}function se(e,t,a){const n=e;for(const e in a){const a=`${t}__${e}`;n.__datetimeFormatters.has(a)&&n.__datetimeFormatters.delete(a)}}function ce(e,...t){const{numberFormats:a,unresolving:n,fallbackLocale:l,onWarn:r,localeFallbacker:o}=e,{__numberFormatters:s}=e,[i,m,f,g]=ue(...t);b(f.missingWarn)?f.missingWarn:e.missingWarn;b(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const _=!!f.part,p=v(f.locale)?f.locale:e.locale,d=o(e,l,p);if(!v(i)||""===i)return new Intl.NumberFormat(p,g).format(m);let k,h={},L=null;for(let t=0;t<d.length&&(k=d[t],h=a[k]||{},L=h[i],!E(L));t++)X(e,i,k,0,"number format");if(!E(L)||!v(k))return n?-1:i;let F=`${k}__${i}`;c(g)||(F=`${F}__${JSON.stringify(g)}`);let y=s.get(F);return y||(y=new Intl.NumberFormat(k,u({},L,g)),s.set(F,y)),_?y.formatToParts(m):y.format(m)}const ie=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function ue(...e){const[t,a,n,l]=e,r={};let s={};if(!o(t))throw Error(Z.INVALID_ARGUMENT);const c=t;return v(a)?r.key=a:E(a)&&Object.keys(a).forEach((e=>{ie.includes(e)?s[e]=a[e]:r[e]=a[e]})),v(n)?r.locale=n:E(n)&&(s=n),E(l)&&(s=l),[r.key||"",c,r,s]}function me(e,t,a){const n=e;for(const e in a){const a=`${t}__${e}`;n.__numberFormatters.has(a)&&n.__numberFormatters.delete(a)}}const fe="9.2.2";let ge=L;const _e=()=>++ge,pe={UNEXPECTED_RETURN_TYPE:ge,INVALID_ARGUMENT:_e(),MUST_BE_CALL_SETUP_TOP:_e(),NOT_INSLALLED:_e(),NOT_AVAILABLE_IN_LEGACY_MODE:_e(),REQUIRED_VALUE:_e(),INVALID_VALUE:_e(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:_e(),NOT_INSLALLED_WITH_PROVIDE:_e(),UNEXPECTED_ERROR:_e(),NOT_COMPATIBLE_LEGACY_VUE_I18N:_e(),BRIDGE_SUPPORT_VUE_2_ONLY:_e(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:_e(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:_e(),__EXTEND_POINT__:_e()};const ve=l("__transrateVNode"),be=l("__datetimeParts"),de=l("__numberParts"),ke=l("__setPluralRules"),he=l("__injectWithOption");function Ee(e){if(!d(e))return e;for(const t in e)if(g(e,t))if(t.includes(".")){const a=t.split("."),n=a.length-1;let l=e;for(let e=0;e<n;e++)a[e]in l||(l[a[e]]={}),l=l[a[e]];l[a[n]]=e[t],delete e[t],d(l[a[n]])&&Ee(l[a[n]])}else d(e[t])&&Ee(e[t]);return e}function Le(e,t){const{messages:a,__i18n:n,messageResolver:l,flatJson:r}=t,o=E(a)?a:_(n)?{}:{[e]:{}};if(_(n)&&n.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:a}=e;t?(o[t]=o[t]||{},ye(a,o[t])):ye(a,o)}else v(e)&&ye(JSON.parse(e),o)})),null==l&&r)for(const e in o)g(o,e)&&Ee(o[e]);return o}const Fe=e=>!d(e)||_(e);function ye(e,t){if(Fe(e)||Fe(t))throw Error(pe.INVALID_VALUE);for(const a in e)g(e,a)&&(Fe(e[a])||Fe(t[a])?t[a]=e[a]:ye(e[a],t[a]))}function Ie(e,t,a){let n=d(t.messages)?t.messages:{};"__i18nGlobal"in a&&(n=Le(e.locale.value,{messages:n,__i18n:a.__i18nGlobal}));const l=Object.keys(n);if(l.length&&l.forEach((t=>{e.mergeLocaleMessage(t,n[t])})),d(t.datetimeFormats)){const a=Object.keys(t.datetimeFormats);a.length&&a.forEach((a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])}))}if(d(t.numberFormats)){const a=Object.keys(t.numberFormats);a.length&&a.forEach((a=>{e.mergeNumberFormat(a,t.numberFormats[a])}))}}function Te(e){return t.createVNode(t.Text,null,e,0)}let Ne=0;function Re(e){return(a,n,l,r)=>e(n,l,t.getCurrentInstance()||void 0,r)}function We(e={},n){const{__root:l}=e,r=void 0===l;let c=!b(e.inheritLocale)||e.inheritLocale;const i=t.ref(l&&c?l.locale.value:v(e.locale)?e.locale:j),m=t.ref(l&&c?l.fallbackLocale.value:v(e.fallbackLocale)||_(e.fallbackLocale)||E(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:i.value),f=t.ref(Le(i.value,e)),g=t.ref(E(e.datetimeFormats)?e.datetimeFormats:{[i.value]:{}}),k=t.ref(E(e.numberFormats)?e.numberFormats:{[i.value]:{}});let h=l?l.missingWarn:!b(e.missingWarn)&&!s(e.missingWarn)||e.missingWarn,L=l?l.fallbackWarn:!b(e.fallbackWarn)&&!s(e.fallbackWarn)||e.fallbackWarn,F=l?l.fallbackRoot:!b(e.fallbackRoot)||e.fallbackRoot,y=!!e.fallbackFormat,I=p(e.missing)?e.missing:null,T=p(e.missing)?Re(e.missing):null,N=p(e.postTranslation)?e.postTranslation:null,R=l?l.warnHtmlMessage:!b(e.warnHtmlMessage)||e.warnHtmlMessage,W=!!e.escapeParameter;const M=l?l.modifiers:E(e.modifiers)?e.modifiers:{};let O,w=e.pluralRules||l&&l.pluralRules;O=(()=>{const t={version:fe,locale:i.value,fallbackLocale:m.value,messages:f.value,modifiers:M,pluralRules:w,missing:null===T?void 0:T,missingWarn:h,fallbackWarn:L,fallbackFormat:y,unresolving:!0,postTranslation:null===N?void 0:N,warnHtmlMessage:R,escapeParameter:W,messageResolver:e.messageResolver,__meta:{framework:"vue"}};t.datetimeFormats=g.value,t.numberFormats=k.value,t.__datetimeFormatters=E(O)?O.__datetimeFormatters:void 0,t.__numberFormatters=E(O)?O.__numberFormatters:void 0;return Y(t)})(),z(O,i.value,m.value);const D=t.computed({get:()=>i.value,set:e=>{i.value=e,O.locale=i.value}}),C=t.computed({get:()=>m.value,set:e=>{m.value=e,O.fallbackLocale=m.value,z(O,i.value,e)}}),P=t.computed((()=>f.value)),A=t.computed((()=>g.value)),$=t.computed((()=>k.value));const U=(e,t,a,n,r,s)=>{let c;if(i.value,m.value,f.value,g.value,k.value,c=e(O),o(c)&&-1===c){const[e,a]=t();return l&&F?n(l):r(e)}if(s(c))return c;throw Error(pe.UNEXPECTED_RETURN_TYPE)};function H(...e){return U((t=>Reflect.apply(ee,null,[t,...e])),(()=>ne(...e)),0,(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>v(e)))}const V={normalize:function(e){return e.map((e=>v(e)||o(e)||b(e)?Te(String(e)):e))},interpolate:e=>e,type:"vnode"};function x(e){return f.value[e]||{}}Ne++,l&&a&&(t.watch(l.locale,(e=>{c&&(i.value=e,O.locale=e,z(O,i.value,m.value))})),t.watch(l.fallbackLocale,(e=>{c&&(m.value=e,O.fallbackLocale=e,z(O,i.value,m.value))})));const G={id:Ne,locale:D,fallbackLocale:C,get inheritLocale(){return c},set inheritLocale(e){c=e,e&&l&&(i.value=l.locale.value,m.value=l.fallbackLocale.value,z(O,i.value,m.value))},get availableLocales(){return Object.keys(f.value).sort()},messages:P,get modifiers(){return M},get pluralRules(){return w||{}},get isGlobal(){return r},get missingWarn(){return h},set missingWarn(e){h=e,O.missingWarn=h},get fallbackWarn(){return L},set fallbackWarn(e){L=e,O.fallbackWarn=L},get fallbackRoot(){return F},set fallbackRoot(e){F=e},get fallbackFormat(){return y},set fallbackFormat(e){y=e,O.fallbackFormat=y},get warnHtmlMessage(){return R},set warnHtmlMessage(e){R=e,O.warnHtmlMessage=e},get escapeParameter(){return W},set escapeParameter(e){W=e,O.escapeParameter=e},t:H,getLocaleMessage:x,setLocaleMessage:function(e,t){f.value[e]=t,O.messages=f.value},mergeLocaleMessage:function(e,t){f.value[e]=f.value[e]||{},ye(t,f.value[e]),O.messages=f.value},getPostTranslationHandler:function(){return p(N)?N:null},setPostTranslationHandler:function(e){N=e,O.postTranslation=e},getMissingHandler:function(){return I},setMissingHandler:function(e){null!==e&&(T=Re(e)),I=e,O.missing=T},[ke]:function(e){w=e,O.pluralRules=w}};return G.datetimeFormats=A,G.numberFormats=$,G.rt=function(...e){const[t,a,n]=e;if(n&&!d(n))throw Error(pe.INVALID_ARGUMENT);return H(t,a,u({resolvedMessage:!0},n||{}))},G.te=function(e,t){const a=x(v(t)?t:i.value);return null!==O.messageResolver(a,e)},G.tm=function(e){const t=function(e){let t=null;const a=S(O,m.value,i.value);for(let n=0;n<a.length;n++){const l=f.value[a[n]]||{},r=O.messageResolver(l,e);if(null!=r){t=r;break}}return t}(e);return null!=t?t:l&&l.tm(e)||{}},G.d=function(...e){return U((t=>Reflect.apply(le,null,[t,...e])),(()=>oe(...e)),0,(t=>Reflect.apply(t.d,t,[...e])),(()=>""),(e=>v(e)))},G.n=function(...e){return U((t=>Reflect.apply(ce,null,[t,...e])),(()=>ue(...e)),0,(t=>Reflect.apply(t.n,t,[...e])),(()=>""),(e=>v(e)))},G.getDateTimeFormat=function(e){return g.value[e]||{}},G.setDateTimeFormat=function(e,t){g.value[e]=t,O.datetimeFormats=g.value,se(O,e,t)},G.mergeDateTimeFormat=function(e,t){g.value[e]=u(g.value[e]||{},t),O.datetimeFormats=g.value,se(O,e,t)},G.getNumberFormat=function(e){return k.value[e]||{}},G.setNumberFormat=function(e,t){k.value[e]=t,O.numberFormats=k.value,me(O,e,t)},G.mergeNumberFormat=function(e,t){k.value[e]=u(k.value[e]||{},t),O.numberFormats=k.value,me(O,e,t)},G[he]=e.__injectWithOption,G[ve]=function(...e){return U((t=>{let a;const n=t;try{n.processor=V,a=Reflect.apply(ee,null,[n,...e])}finally{n.processor=null}return a}),(()=>ne(...e)),0,(t=>t[ve](...e)),(e=>[Te(e)]),(e=>_(e)))},G[be]=function(...e){return U((t=>Reflect.apply(le,null,[t,...e])),(()=>oe(...e)),0,(t=>t[be](...e)),(()=>[]),(e=>v(e)||_(e)))},G[de]=function(...e){return U((t=>Reflect.apply(ce,null,[t,...e])),(()=>ue(...e)),0,(t=>t[de](...e)),(()=>[]),(e=>v(e)||_(e)))},G}function Me(e={},t){{const t=We(function(e){const t=v(e.locale)?e.locale:j,a=v(e.fallbackLocale)||_(e.fallbackLocale)||E(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,n=p(e.missing)?e.missing:void 0,l=!b(e.silentTranslationWarn)&&!s(e.silentTranslationWarn)||!e.silentTranslationWarn,r=!b(e.silentFallbackWarn)&&!s(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!b(e.fallbackRoot)||e.fallbackRoot,c=!!e.formatFallbackMessages,i=E(e.modifiers)?e.modifiers:{},m=e.pluralizationRules,f=p(e.postTranslation)?e.postTranslation:void 0,g=!v(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,d=!!e.escapeParameterHtml,k=!b(e.sync)||e.sync;let h=e.messages;if(E(e.sharedMessages)){const t=e.sharedMessages;h=Object.keys(t).reduce(((e,a)=>{const n=e[a]||(e[a]={});return u(n,t[a]),e}),h||{})}const{__i18n:L,__root:F,__injectWithOption:y}=e,I=e.datetimeFormats,T=e.numberFormats;return{locale:t,fallbackLocale:a,messages:h,flatJson:e.flatJson,datetimeFormats:I,numberFormats:T,missing:n,missingWarn:l,fallbackWarn:r,fallbackRoot:o,fallbackFormat:c,modifiers:i,pluralRules:m,postTranslation:f,warnHtmlMessage:g,escapeParameter:d,messageResolver:e.messageResolver,inheritLocale:k,__i18n:L,__root:F,__injectWithOption:y}}(e)),a={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return b(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=b(e)?!e:e},get silentFallbackWarn(){return b(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=b(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[a,n,l]=e,r={};let o=null,s=null;if(!v(a))throw Error(pe.INVALID_ARGUMENT);const c=a;return v(n)?r.locale=n:_(n)?o=n:E(n)&&(s=n),_(l)?o=l:E(l)&&(s=l),Reflect.apply(t.t,t,[c,o||s||{},r])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[a,n,l]=e,r={plural:1};let s=null,c=null;if(!v(a))throw Error(pe.INVALID_ARGUMENT);const i=a;return v(n)?r.locale=n:o(n)?r.plural=n:_(n)?s=n:E(n)&&(c=n),v(l)?r.locale=l:_(l)?s=l:E(l)&&(c=l),Reflect.apply(t.t,t,[i,s||c||{},r])},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)},getChoiceIndex:(e,t)=>-1,__onComponentInstanceCreated(t){const{componentInstanceCreatedListener:n}=e;n&&n(t,a)}};return a}}const Oe={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function we(e){return t.Fragment}const De={name:"i18n-t",props:u({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>o(e)||!isNaN(e)}},Oe),setup(e,a){const{slots:n,attrs:l}=a,r=e.i18n||Ve({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter((e=>"_"!==e)),s={};e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=v(e.plural)?+e.plural:e.plural);const c=function({slots:e},t){if(1===t.length&&"default"===t[0])return(e.default?e.default():[]).reduce(((e,t)=>[...e,..._(t.children)?t.children:[t]]),[]);return t.reduce(((t,a)=>{const n=e[a];return n&&(t[a]=n()),t}),{})}(a,o),i=r[ve](e.keypath,c,s),m=u({},l),f=v(e.tag)||d(e.tag)?e.tag:we();return t.h(f,m,i)}}};function Ce(e,a,n,l){const{slots:r,attrs:o}=a;return()=>{const a={part:!0};let s={};e.locale&&(a.locale=e.locale),v(e.format)?a.key=e.format:d(e.format)&&(v(e.format.key)&&(a.key=e.format.key),s=Object.keys(e.format).reduce(((t,a)=>n.includes(a)?u({},t,{[a]:e.format[a]}):t),{}));const c=l(e.value,a,s);let i=[a.key];_(c)?i=c.map(((e,t)=>{const a=r[e.type],n=a?a({[e.type]:e.value,index:t,parts:c}):[e.value];var l;return _(l=n)&&!v(l[0])&&(n[0].key=`${e.type}-${t}`),n})):v(c)&&(i=[c]);const m=u({},o),f=v(e.tag)||d(e.tag)?e.tag:we();return t.h(f,m,i)}}const Pe={name:"i18n-n",props:u({value:{type:Number,required:!0},format:{type:[String,Object]}},Oe),setup(e,t){const a=e.i18n||Ve({useScope:"parent",__useComponent:!0});return Ce(e,t,ie,((...e)=>a[de](...e)))}},Ae={name:"i18n-d",props:u({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Oe),setup(e,t){const a=e.i18n||Ve({useScope:"parent",__useComponent:!0});return Ce(e,t,re,((...e)=>a[be](...e)))}};function Se(e){const n=t=>{const{instance:a,modifiers:n,value:l}=t;if(!a||!a.$)throw Error(pe.UNEXPECTED_ERROR);const r=function(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const n=a.__getInstance(t);return null!=n?n.__composer:e.global.__composer}}(e,a.$),o=$e(l);return[Reflect.apply(r.t,r,[...Ue(o)]),r]};return{created:(l,r)=>{const[o,s]=n(r);a&&e.global===s&&(l.__i18nWatcher=t.watch(s.locale,(()=>{r.instance&&r.instance.$forceUpdate()}))),l.__composer=s,l.textContent=o},unmounted:e=>{a&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const a=e.__composer,n=$e(t);e.textContent=Reflect.apply(a.t,a,[...Ue(n)])}},getSSRProps:e=>{const[t]=n(e);return{textContent:t}}}}function $e(e){if(v(e))return{path:e};if(E(e)){if(!("path"in e))throw Error(pe.REQUIRED_VALUE,"path");return e}throw Error(pe.INVALID_VALUE)}function Ue(e){const{path:t,locale:a,args:n,choice:l,plural:r}=e,s={},c=n||{};return v(a)&&(s.locale=a),o(l)&&(s.plural=l),o(r)&&(s.plural=r),[t,c,s]}function He(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[ke](t.pluralizationRules||e.pluralizationRules);const a=Le(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}const je=l("global-vue-i18n");function Ve(e={}){const a=t.getCurrentInstance();if(null==a)throw Error(pe.MUST_BE_CALL_SETUP_TOP);if(!a.isCE&&null!=a.appContext.app&&!a.appContext.app.__VUE_I18N_SYMBOL__)throw Error(pe.NOT_INSLALLED);const n=function(e){{const a=t.inject(e.isCE?je:e.appContext.app.__VUE_I18N_SYMBOL__);if(!a)throw function(e,...t){return F(e,null,void 0)}(e.isCE?pe.NOT_INSLALLED_WITH_PROVIDE:pe.UNEXPECTED_ERROR);return a}}(a),l=function(e){return"composition"===e.mode?e.global:e.global.__composer}(n),r=function(e){return e.type}(a),o=function(e,t){return c(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,r);if("legacy"===n.mode&&!e.__useComponent){if(!n.allowComposition)throw Error(pe.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,a,n,l={}){const r="local"===a,o=t.shallowRef(null);if(r&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(pe.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const c=!b(l.inheritLocale)||l.inheritLocale,i=t.ref(r&&c?n.locale.value:v(l.locale)?l.locale:j),u=t.ref(r&&c?n.fallbackLocale.value:v(l.fallbackLocale)||_(l.fallbackLocale)||E(l.fallbackLocale)||!1===l.fallbackLocale?l.fallbackLocale:i.value),m=t.ref(Le(i.value,l)),f=t.ref(E(l.datetimeFormats)?l.datetimeFormats:{[i.value]:{}}),g=t.ref(E(l.numberFormats)?l.numberFormats:{[i.value]:{}}),d=r?n.missingWarn:!b(l.missingWarn)&&!s(l.missingWarn)||l.missingWarn,k=r?n.fallbackWarn:!b(l.fallbackWarn)&&!s(l.fallbackWarn)||l.fallbackWarn,h=r?n.fallbackRoot:!b(l.fallbackRoot)||l.fallbackRoot,L=!!l.fallbackFormat,F=p(l.missing)?l.missing:null,y=p(l.postTranslation)?l.postTranslation:null,I=r?n.warnHtmlMessage:!b(l.warnHtmlMessage)||l.warnHtmlMessage,T=!!l.escapeParameter,N=r?n.modifiers:E(l.modifiers)?l.modifiers:{},R=l.pluralRules||r&&n.pluralRules;function W(){return[i.value,u.value,m.value,f.value,g.value]}const M=t.computed({get:()=>o.value?o.value.locale.value:i.value,set:e=>{o.value&&(o.value.locale.value=e),i.value=e}}),O=t.computed({get:()=>o.value?o.value.fallbackLocale.value:u.value,set:e=>{o.value&&(o.value.fallbackLocale.value=e),u.value=e}}),w=t.computed((()=>o.value?o.value.messages.value:m.value)),D=t.computed((()=>f.value)),C=t.computed((()=>g.value));function P(){return o.value?o.value.getPostTranslationHandler():y}function A(e){o.value&&o.value.setPostTranslationHandler(e)}function S(){return o.value?o.value.getMissingHandler():F}function $(e){o.value&&o.value.setMissingHandler(e)}function U(e){return W(),e()}function H(...e){return o.value?U((()=>Reflect.apply(o.value.t,null,[...e]))):U((()=>""))}function V(...e){return o.value?Reflect.apply(o.value.rt,null,[...e]):""}function x(...e){return o.value?U((()=>Reflect.apply(o.value.d,null,[...e]))):U((()=>""))}function G(...e){return o.value?U((()=>Reflect.apply(o.value.n,null,[...e]))):U((()=>""))}function B(e){return o.value?o.value.tm(e):{}}function Y(e,t){return!!o.value&&o.value.te(e,t)}function X(e){return o.value?o.value.getLocaleMessage(e):{}}function z(e,t){o.value&&(o.value.setLocaleMessage(e,t),m.value[e]=t)}function J(e,t){o.value&&o.value.mergeLocaleMessage(e,t)}function q(e){return o.value?o.value.getDateTimeFormat(e):{}}function Z(e,t){o.value&&(o.value.setDateTimeFormat(e,t),f.value[e]=t)}function K(e,t){o.value&&o.value.mergeDateTimeFormat(e,t)}function Q(e){return o.value?o.value.getNumberFormat(e):{}}function ee(e,t){o.value&&(o.value.setNumberFormat(e,t),g.value[e]=t)}function te(e,t){o.value&&o.value.mergeNumberFormat(e,t)}const ae={get id(){return o.value?o.value.id:-1},locale:M,fallbackLocale:O,messages:w,datetimeFormats:D,numberFormats:C,get inheritLocale(){return o.value?o.value.inheritLocale:c},set inheritLocale(e){o.value&&(o.value.inheritLocale=e)},get availableLocales(){return o.value?o.value.availableLocales:Object.keys(m.value)},get modifiers(){return o.value?o.value.modifiers:N},get pluralRules(){return o.value?o.value.pluralRules:R},get isGlobal(){return!!o.value&&o.value.isGlobal},get missingWarn(){return o.value?o.value.missingWarn:d},set missingWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackWarn(){return o.value?o.value.fallbackWarn:k},set fallbackWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackRoot(){return o.value?o.value.fallbackRoot:h},set fallbackRoot(e){o.value&&(o.value.fallbackRoot=e)},get fallbackFormat(){return o.value?o.value.fallbackFormat:L},set fallbackFormat(e){o.value&&(o.value.fallbackFormat=e)},get warnHtmlMessage(){return o.value?o.value.warnHtmlMessage:I},set warnHtmlMessage(e){o.value&&(o.value.warnHtmlMessage=e)},get escapeParameter(){return o.value?o.value.escapeParameter:T},set escapeParameter(e){o.value&&(o.value.escapeParameter=e)},t:H,getPostTranslationHandler:P,setPostTranslationHandler:A,getMissingHandler:S,setMissingHandler:$,rt:V,d:x,n:G,tm:B,te:Y,getLocaleMessage:X,setLocaleMessage:z,mergeLocaleMessage:J,getDateTimeFormat:q,setDateTimeFormat:Z,mergeDateTimeFormat:K,getNumberFormat:Q,setNumberFormat:ee,mergeNumberFormat:te};function ne(e){e.locale.value=i.value,e.fallbackLocale.value=u.value,Object.keys(m.value).forEach((t=>{e.mergeLocaleMessage(t,m.value[t])})),Object.keys(f.value).forEach((t=>{e.mergeDateTimeFormat(t,f.value[t])})),Object.keys(g.value).forEach((t=>{e.mergeNumberFormat(t,g.value[t])})),e.escapeParameter=T,e.fallbackFormat=L,e.fallbackRoot=h,e.fallbackWarn=k,e.missingWarn=d,e.warnHtmlMessage=I}return t.onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(pe.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const t=o.value=e.proxy.$i18n.__composer;"global"===a?(i.value=t.locale.value,u.value=t.fallbackLocale.value,m.value=t.messages.value,f.value=t.datetimeFormats.value,g.value=t.numberFormats.value):r&&ne(t)})),ae}(a,o,l,e)}if("global"===o)return Ie(l,e,r),l;if("parent"===o){let t=function(e,t,a=!1){let n=null;const l=t.root;let r=t.parent;for(;null!=r;){const t=e;if("composition"===e.mode)n=t.__getInstance(r);else{const e=t.__getInstance(r);null!=e&&(n=e.__composer,a&&n&&!n[he]&&(n=null))}if(null!=n)break;if(l===r)break;r=r.parent}return n}(n,a,e.__useComponent);return null==t&&(t=l),t}const i=n;let m=i.__getInstance(a);if(null==m){const n=u({},e);"__i18n"in r&&(n.__i18n=r.__i18n),l&&(n.__root=l),m=We(n),function(e,a,n){t.onMounted((()=>{}),a),t.onUnmounted((()=>{e.__deleteInstance(a)}),a)}(i,a),i.__setInstance(a,m)}return m}const xe=["locale","fallbackLocale","availableLocales"],Ge=["t","rt","d","n","tm"];return x=function(e,t){if(!d(e))return null;let a=R.get(t);if(a||(a=function(e){const t=[];let a,n,l,r,o,s,c,i=-1,u=0,m=0;const f=[];function g(){const t=e[i+1];if(5===u&&"'"===t||6===u&&'"'===t)return i++,l="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===n?n=l:n+=l},f[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===n)return!1;if(n=N(n),!1===n)return!1;f[1]()}};null!==u;)if(i++,a=e[i],"\\"!==a||!g()){if(r=T(a),c=y[u],o=c[r]||c.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(s=f[o[1]],s&&(l=a,!1===s())))return;if(7===u)return t}}(t),a&&R.set(t,a)),!a)return null;const n=a.length;let l=e,r=0;for(;r<n;){const e=l[a[r]];if(void 0===e)return null;l=e,r++}return l},G=S,e.DatetimeFormat=Ae,e.I18nInjectionKey=je,e.NumberFormat=Pe,e.Translation=De,e.VERSION=fe,e.castToVueI18n=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(pe.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e},e.createI18n=function(e={},a){const n=!b(e.legacy)||e.legacy,r=!b(e.globalInjection)||e.globalInjection,o=!n||!!e.allowComposition,s=new Map,[c,i]=function(e,a,n){const l=t.effectScope();{const t=a?l.run((()=>Me(e))):l.run((()=>We(e)));if(null==t)throw Error(pe.UNEXPECTED_ERROR);return[l,t]}}(e,n),u=l("");{const e={get mode(){return n?"legacy":"composition"},get allowComposition(){return o},async install(a,...l){a.__VUE_I18N_SYMBOL__=u,a.provide(a.__VUE_I18N_SYMBOL__,e),!n&&r&&function(e,a){const n=Object.create(null);xe.forEach((e=>{const l=Object.getOwnPropertyDescriptor(a,e);if(!l)throw Error(pe.UNEXPECTED_ERROR);const r=t.isRef(l.value)?{get:()=>l.value.value,set(e){l.value.value=e}}:{get:()=>l.get&&l.get()};Object.defineProperty(n,e,r)})),e.config.globalProperties.$i18n=n,Ge.forEach((t=>{const n=Object.getOwnPropertyDescriptor(a,t);if(!n||!n.value)throw Error(pe.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,n)}))}(a,e.global),function(e,t,...a){const n=E(a[0])?a[0]:{},l=!!n.useI18nComponentName;(!b(n.globalInstall)||n.globalInstall)&&(e.component(l?"i18n":De.name,De),e.component(Pe.name,Pe),e.component(Ae.name,Ae)),e.directive("t",Se(t))}(a,e,...l),n&&a.mixin(function(e,a,n){return{beforeCreate(){const l=t.getCurrentInstance();if(!l)throw Error(pe.UNEXPECTED_ERROR);const r=this.$options;if(r.i18n){const t=r.i18n;r.__i18n&&(t.__i18n=r.__i18n),t.__root=a,this===this.$root?this.$i18n=He(e,t):(t.__injectWithOption=!0,this.$i18n=Me(t))}else r.__i18n?this===this.$root?this.$i18n=He(e,r):this.$i18n=Me({__i18n:r.__i18n,__injectWithOption:!0,__root:a}):this.$i18n=e;r.__i18nGlobal&&Ie(a,r,r),e.__onComponentInstanceCreated(this.$i18n),n.__setInstance(l,this.$i18n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(pe.UNEXPECTED_ERROR);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__deleteInstance(e),delete this.$i18n}}}(i,i.__composer,e));const o=a.unmount;a.unmount=()=>{e.dispose(),o()}},get global(){return i},dispose(){c.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}},e.useI18n=Ve,e.vTDirective=Se,Object.defineProperty(e,"__esModule",{value:!0}),e}({},Vue);
