<template>
	<view class="skeleton-container">
		<view class="skeleton-tabs">
			<view v-for="i in 4" :key="i" class="skeleton-tab shimmer"></view>
		</view>
		
		<view class="skeleton-list">
			<view v-for="i in 3" :key="i" class="skeleton-item">
				<view class="skeleton-item-top">
					<view class="skeleton-name shimmer"></view>
					<view class="skeleton-date shimmer"></view>
				</view>
				<view class="skeleton-item-bottom">
					<view class="skeleton-left">
						<view class="skeleton-order shimmer"></view>
						<view class="skeleton-txt shimmer"></view>
					</view>
					<view class="skeleton-status shimmer"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'OrderSkeleton'
}
</script>

<style lang="less" scoped>
.skeleton-container {
	width: 100%;
	
	.skeleton-tabs {
		display: flex;
		justify-content: space-around;
		padding: 20rpx;
		background: #fff;
		border-bottom: 1rpx solid #EEEEEE;
		
		.skeleton-tab {
			width: 120rpx;
			height: 40rpx;
			border-radius: 8rpx;
			background: #f0f0f0;
		}
	}
	
	.skeleton-list {
		padding: 20rpx;
		
		.skeleton-item {
			height: 240rpx;
			background: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			padding: 20rpx;
			
			.skeleton-item-top {
				height: 100rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid rgba(224, 224, 224, 1);
				
				.skeleton-name {
					width: 200rpx;
					height: 60rpx;
					border-radius: 14rpx;
					background: #f0f0f0;
				}
				
				.skeleton-date {
					width: 160rpx;
					height: 40rpx;
					border-radius: 8rpx;
					background: #f0f0f0;
				}
			}
			
			.skeleton-item-bottom {
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 139rpx;
				
				.skeleton-left {
					.skeleton-order {
						width: 240rpx;
						height: 32rpx;
						border-radius: 6rpx;
						background: #f0f0f0;
						margin-bottom: 18rpx;
					}
					
					.skeleton-txt {
						width: 320rpx;
						height: 32rpx;
						border-radius: 6rpx;
						background: #f0f0f0;
					}
				}
				
				.skeleton-status {
					width: 120rpx;
					height: 32rpx;
					border-radius: 6rpx;
					background: #f0f0f0;
				}
			}
		}
	}
}

// 骨架屏动画
.shimmer {
	background: linear-gradient(
		90deg,
		#f0f0f0 25%,
		#f8f8f8 37%,
		#f0f0f0 63%
	);
	background-size: 400% 100%;
	animation: shimmer 1.4s ease infinite;
}

@keyframes shimmer {
	0% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0 50%;
	}
}
</style> 