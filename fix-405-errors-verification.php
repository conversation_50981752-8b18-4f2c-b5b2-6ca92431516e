<?php
/**
 * 405错误修复验证工具
 * 验证订单API的GET方法支持
 */

echo "<h1>🔧 405错误修复验证</h1>\n";
echo "<p>验证时间: " . date('Y-m-d H:i:s') . "</p>\n";

// 检查路由文件修改
echo "<h2>1. 路由配置检查</h2>\n";

$routeFile = 'admin.inspap.com/routes/api.php';
if (file_exists($routeFile)) {
    $content = file_get_contents($routeFile);
    
    // 检查订单路由是否支持GET方法
    if (strpos($content, "Route::middleware('auth:api')->get('user/ordering/{action}','ApiController@ordering')") !== false) {
        echo "<p style='color: green;'>✅ 订单GET路由已添加</p>\n";
        $getRouteFixed = true;
    } else {
        echo "<p style='color: red;'>❌ 订单GET路由未找到</p>\n";
        $getRouteFixed = false;
    }
    
    // 检查是否仍保留POST方法
    if (strpos($content, "Route::middleware('auth:api')->post('user/ordering/{action}','ApiController@ordering')") !== false) {
        echo "<p style='color: green;'>✅ 订单POST路由已保留</p>\n";
        $postRouteExists = true;
    } else {
        echo "<p style='color: orange;'>⚠️ 订单POST路由未找到</p>\n";
        $postRouteExists = false;
    }
} else {
    echo "<p style='color: red;'>❌ 路由文件不存在</p>\n";
    $getRouteFixed = false;
    $postRouteExists = false;
}

// 检查控制器方法
echo "<h2>2. 控制器方法检查</h2>\n";

$controllerFile = 'admin.inspap.com/app/Http/Controllers/ApiController.php';
if (file_exists($controllerFile)) {
    $controllerContent = file_get_contents($controllerFile);
    
    // 检查ordering方法是否存在
    if (strpos($controllerContent, 'public function ordering(Request $request,$action)') !== false) {
        echo "<p style='color: green;'>✅ ordering方法存在</p>\n";
        $orderingMethodExists = true;
    } else {
        echo "<p style='color: red;'>❌ ordering方法不存在</p>\n";
        $orderingMethodExists = false;
    }
    
    // 检查get action是否存在
    if (strpos($controllerContent, "else if(\$action=='get')") !== false) {
        echo "<p style='color: green;'>✅ get action处理逻辑存在</p>\n";
        $getActionExists = true;
    } else {
        echo "<p style='color: red;'>❌ get action处理逻辑不存在</p>\n";
        $getActionExists = false;
    }
    
    // 检查add action是否存在
    if (strpos($controllerContent, "if(\$action=='add')") !== false) {
        echo "<p style='color: green;'>✅ add action处理逻辑存在</p>\n";
        $addActionExists = true;
    } else {
        echo "<p style='color: red;'>❌ add action处理逻辑不存在</p>\n";
        $addActionExists = false;
    }
} else {
    echo "<p style='color: red;'>❌ 控制器文件不存在</p>\n";
    $orderingMethodExists = false;
    $getActionExists = false;
    $addActionExists = false;
}

// 检查前端工具类修改
echo "<h2>3. 前端工具类检查</h2>\n";

$toolsFile = 'static/js/utils/Tools.js';
if (file_exists($toolsFile)) {
    $toolsContent = file_get_contents($toolsFile);
    
    // 检查默认方法是否改为GET
    if (strpos($toolsContent, "method: options.method || 'GET'") !== false) {
        echo "<p style='color: green;'>✅ 默认HTTP方法已改为GET</p>\n";
        $defaultMethodFixed = true;
    } else {
        echo "<p style='color: red;'>❌ 默认HTTP方法未改为GET</p>\n";
        $defaultMethodFixed = false;
    }
    
    // 检查Post方法是否改为GET
    if (strpos($toolsContent, "options['method'] = 'GET'; // 改为GET方法确保稳定性") !== false) {
        echo "<p style='color: green;'>✅ Post方法已改为GET</p>\n";
        $postMethodFixed = true;
    } else {
        echo "<p style='color: red;'>❌ Post方法未改为GET</p>\n";
        $postMethodFixed = false;
    }
} else {
    echo "<p style='color: red;'>❌ 前端工具类文件不存在</p>\n";
    $defaultMethodFixed = false;
    $postMethodFixed = false;
}

// 总结修复状态
echo "<h2>4. 修复状态总结</h2>\n";

$allFixed = $getRouteFixed && $postRouteExists && $orderingMethodExists && 
           $getActionExists && $addActionExists && $defaultMethodFixed && $postMethodFixed;

if ($allFixed) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; border-left: 4px solid #28a745;'>\n";
    echo "<h3 style='color: green;'>🎉 所有405错误修复完成！</h3>\n";
    echo "<p><strong>修复内容：</strong></p>\n";
    echo "<ul>\n";
    echo "<li>✅ 订单API路由支持GET和POST方法</li>\n";
    echo "<li>✅ 控制器方法支持GET请求处理</li>\n";
    echo "<li>✅ 前端默认使用GET方法</li>\n";
    echo "<li>✅ 前端Post方法实际使用GET</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; border-left: 4px solid #dc3545;'>\n";
    echo "<h3 style='color: red;'>❌ 部分修复未完成</h3>\n";
    echo "<p><strong>需要检查的项目：</strong></p>\n";
    echo "<ul>\n";
    if (!$getRouteFixed) echo "<li style='color: red;'>❌ 订单GET路由未添加</li>\n";
    if (!$postRouteExists) echo "<li style='color: red;'>❌ 订单POST路由缺失</li>\n";
    if (!$orderingMethodExists) echo "<li style='color: red;'>❌ ordering方法不存在</li>\n";
    if (!$getActionExists) echo "<li style='color: red;'>❌ get action处理逻辑缺失</li>\n";
    if (!$addActionExists) echo "<li style='color: red;'>❌ add action处理逻辑缺失</li>\n";
    if (!$defaultMethodFixed) echo "<li style='color: red;'>❌ 默认HTTP方法未改为GET</li>\n";
    if (!$postMethodFixed) echo "<li style='color: red;'>❌ Post方法未改为GET</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

// 测试建议
echo "<h2>5. 测试建议</h2>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007bff;'>\n";
echo "<h4>🧪 建议测试步骤：</h4>\n";
echo "<ol>\n";
echo "<li><strong>清除Laravel缓存：</strong><br>\n";
echo "<code>php artisan route:clear && php artisan config:clear && php artisan cache:clear</code></li>\n";

echo "<li><strong>测试订单获取接口：</strong><br>\n";
echo "GET <code>/api/user/ordering/get?api_token=TOKEN&uid=USER_ID&limit=10</code></li>\n";

echo "<li><strong>测试订单添加接口：</strong><br>\n";
echo "GET <code>/api/user/ordering/add</code> (带相应参数)</li>\n";

echo "<li><strong>检查前端调用：</strong><br>\n";
echo "确认前端页面中的订单API调用不再出现405错误</li>\n";

echo "<li><strong>验证认证：</strong><br>\n";
echo "确认API调用时包含有效的token和用户ID</li>\n";
echo "</ol>\n";
echo "</div>\n";

// 可能的其他问题
echo "<h2>6. 可能的其他问题</h2>\n";

echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107;'>\n";
echo "<h4>⚠️ 如果仍有405错误，请检查：</h4>\n";
echo "<ul>\n";
echo "<li><strong>认证问题：</strong> 确认token有效且用户已登录</li>\n";
echo "<li><strong>中间件问题：</strong> 检查auth:api中间件配置</li>\n";
echo "<li><strong>参数问题：</strong> 确认传递了必需的uid和api_token参数</li>\n";
echo "<li><strong>服务器缓存：</strong> 可能需要重启Web服务器</li>\n";
echo "<li><strong>其他API：</strong> 检查是否还有其他API需要添加GET支持</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: 'Courier New', monospace; }
ol li, ul li { margin: 8px 0; }
</style>\n";

echo "<p><strong>验证完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
