<?php

namespace App\Admin\Controllers;

use App\Admin\Metrics\Examples;
use App\Http\Controllers\Controller;
use Dcat\Admin\Http\Controllers\Dashboard;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Widgets\Card;
use App\Admin\Forms\Setting;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Tools;
use App\Models\UsersRecharge as sRecharge ;

/**首页各类统计 */


class CountController extends Controller
{


    public function __construct(){
        
    }


    public function index(Request $request)
    {
       $this->mthod = 'index';
       $this->id = '';
       $data = [
           'recharge_money'       => $this->get_price($request),
           'recharge_pople'       => $this->get_pople($request),
           'recharge_first_price' => $this->get_first_price($request),
           'recharge_first_pople' => $this->get_first_pople($request),
           'recharge_number'      => $this->get_recharge_number($request),
           'withdraw_money'       => $this->get_withdraw_money($request),
           'withdraw_pople'       => $this->get_withdraw_pople($request),
           'withdraw_number'      => $this->get_withdraw_number($request),
           'order_money'          => $this->get_order_money($request),
           'order_pople'          => $this->get_order_pople($request),
           'order_number'         => $this->get_order_number($request),
           'order_lottery'        => $this->get_order_lottery($request),
           'zhuche_pople'         => $this->get_zhuche_pople($request),
           'sum_zhuche_pople'     => $this->get_zhuche_sum_pople(),      //注册总人数
           'sum_recharge_money'   => $this->get_sum_price(),             //充值总金额
           'sum_withdraw_money'   => $this->get_withdraw_sum_money(),    //提现总金额
           'sum_order_money'      => $this->get_order_sum_money(),       //订单总金额
           'sum_order_number'     => $this->get_order_sum_number(),      //订单总数量
           'sum_order_lottery'    => $this->get_order_sum_lottery(),     //中奖总金额
           
       ];

       $retun_msg = [
           'data' => $data,
           'msg'  =>'数据获取成功！',
           'status' => 200,
       ];
       return $retun_msg;
    }

    public function ex_staff(Request $request,$id){

            $this->mthod = 'staff';
            $this->id = $id;
            $data = [
                'recharge_money'       => $this->get_price($request),
                'recharge_pople'       => $this->get_pople($request),
                'recharge_1_price'     => $this->get_first_price($request),
                'recharge_1_pople'     => $this->get_first_pople($request),
                'recharge_2_price'     => $this->get_first_price($request,2),
                'recharge_2_pople'     => $this->get_first_pople($request,2),
                'recharge_3_price'     => $this->get_first_price($request,3),
                'recharge_3_pople'     => $this->get_first_pople($request,3),
                'recharge_number'      => $this->get_recharge_number($request),
                'withdraw_money'       => $this->get_withdraw_money($request),
                'withdraw_pople'       => $this->get_withdraw_pople($request),
                'withdraw_number'      => $this->get_withdraw_number($request),
                'order_money'          => $this->get_order_money($request),
                'order_pople'          => $this->get_order_pople($request),
                'order_number'         => $this->get_order_number($request),
                'order_lottery'        => $this->get_order_lottery($request),
                'zhuche_pople'         => $this->get_zhuche_pople($request),
                
            ];
 
            return $data;

    }
    
    public function ex_team(Request $request,$id){

        $this->mthod = 'team';
        $this->id = $id;
        $data = [
            'recharge_money'       => $this->get_price($request),
            'recharge_pople'       => $this->get_pople($request),
            'recharge_1_price'     => $this->get_first_price($request),
            'recharge_1_pople'     => $this->get_first_pople($request),

            'recharge_number'      => $this->get_recharge_number($request),
            'withdraw_money'       => $this->get_withdraw_money($request),
            'withdraw_pople'       => $this->get_withdraw_pople($request),
            'withdraw_number'      => $this->get_withdraw_number($request),
            'order_money'          => $this->get_order_money($request),
            'order_pople'          => $this->get_order_pople($request),
            'order_number'         => $this->get_order_number($request),
            'order_lottery'        => $this->get_order_lottery($request),
            'zhuche_pople'         => $this->get_zhuche_pople($request),
            
        ];

        return $data;

    }


    /**获取充值金额 */
    protected function get_price($request){ 

        $where    = Tools::Countroles($this->mthod,$this->id);
        $querTime = Tools::get_date_to_date($request);

        $recharge = DB::table('users as user')
        ->join('users_recharge as ur','user.uid','=','ur.uid')
        ->where($where)
        ->where('ur.action','1')  //不统计彩金
        ->whereBetween('ur.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->select(DB::raw('sum(ur.price) as sum_money'))
        ->first();

        if($recharge){
            return $recharge->sum_money ?? 0;
        }
        else{
            return 0;
        }
        
    }

    /**获取充值人数 */
    protected function get_pople($request){ 

        $where    = Tools::Countroles($this->mthod,$this->id);
        $querTime = Tools::get_date_to_date($request);
        $recharge = DB::table('users as user')
        ->join('users_recharge as ur','user.uid','=','ur.uid')
        ->select(DB::raw('count(ur.uname) as count_pople'))
        ->where('ur.action','1')  //不统计彩金
        ->where($where)
        ->whereBetween('ur.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->groupBy('ur.uid')
        ->get();



        if($recharge){
            return count($recharge);  //聚合出的结果为 [{"count_pople":3},{"count_pople":2},{"count_pople":1},{"count_pople":1}]  直接统计数组长度就是人数 
        }
        else{
            return 0;
        }
        
    }

    /**获取首次充值金额 */
    protected function get_first_price($request,$num=1){ 

        $where    = Tools::Countroles($this->mthod,$this->id);
        $querTime = Tools::get_date_to_date($request);
        array_push($where,['num',$num]);
        $recharge = DB::table('users as user')
        ->join('users_recharge as ur','user.uid','=','ur.uid')
        ->select(DB::raw('sum(ur.price) as sum_money'))
        ->whereBetween('ur.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->first();

        if($recharge){
            return $recharge->sum_money ?? 0;
        }
        else{
            return 0;
        }
        
    }

    /**获取首次充值人数 */
    protected function get_first_pople($request,$num=1){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);
        array_push($where,['num',$num]);
        $recharge = DB::table('users as user')
        ->join('users_recharge as ur','user.uid','=','ur.uid')
        ->select(DB::raw('count(ur.uname) as count_pople'))
        ->whereBetween('ur.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->groupBy('ur.uid')
        ->get();



        if($recharge){
            return count($recharge);  //聚合出的结果为 [{"count_pople":3},{"count_pople":2},{"count_pople":1},{"count_pople":1}]  直接统计数组长度就是人数 
        }
        else{
            return 0;
        }
        
    }

    /**获取充值笔数 */
    protected function get_recharge_number($request){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);
        $recharge = DB::table('users as user')
        ->join('users_recharge as ur','user.uid','=','ur.uid')
        ->select(DB::raw('count(ur.uname) as recharge_number'))
        ->whereBetween('ur.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->where('ur.action','1')  //不统计彩金
        ->first();
        if($recharge){
            return $recharge->recharge_number;  
        }
        else{
            return 0;
        }
        
    }


    /**获取提现金额 */
    protected function get_withdraw_money($request){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);

        $withdraw = DB::table('users as user')
        ->join('users_withdraw as ur','user.uid','=','ur.uid')
        ->select(DB::raw('sum(ur.price) as sum_money'))
        ->whereBetween('ur.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->where('ur.status',1)  //只有审核通过了的才能算被正式提现
        ->first();

        if($withdraw){
            return $withdraw->sum_money ?? 0;
        }
        else{
            return 0;
        }
        
    }

    /**获取提现人数 */
    protected function get_withdraw_pople($request){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);

        $withdraw = DB::table('users as user')
        ->join('users_withdraw as ur','user.uid','=','ur.uid')
        ->select(DB::raw('count(ur.uid) as count_pople'))
        ->whereBetween('ur.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->groupBy('ur.uid')
        ->get();



        if($withdraw){
            return count($withdraw);  //聚合出的结果为 [{"count_pople":3},{"count_pople":2},{"count_pople":1},{"count_pople":1}]  直接统计数组长度就是人数 
        }
        else{
            return 0;
        }
        
    }

    /**获取提现笔数 */
    protected function get_withdraw_number($request){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);
        $withdraw = DB::table('users as user')
        ->join('users_withdraw as ur','user.uid','=','ur.uid')
        ->select(DB::raw('count(ur.uid) as withdraw_number'))
        ->whereBetween('ur.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->where('ur.status',1)  //只有审核通过了的才能算被正式提现
        ->first();
        if($withdraw){
            return $withdraw->withdraw_number;  
        }
        else{
            return 0;
        }
        
    }


    /**获取投注金额 */
    protected function get_order_money($request){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);

        $order = DB::table('users as user')
        ->join('product_ordering as order','user.uid','=','order.uid')
        ->select(DB::raw('sum(order.quantity_price) as sum_money'))
        ->whereBetween('order.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->first();

        if($order){
            return $order->sum_money ?? 0;
        }
        else{
            return 0;
        }
        
    }

     /**获取中奖金额 */
     protected function get_order_lottery($request){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);

        $order = DB::table('users as user')
        ->join('product_ordering as order','user.uid','=','order.uid')
        ->select(DB::raw('sum(order.lottery_price) as sum_lottery'))
        ->whereBetween('order.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->first();

        if($order){
            return $order->sum_lottery ?? 0;
        }
        else{
            return 0;
        }
        
    }

    /**获取投注人数 */
    protected function get_order_pople($request){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);

        $order = DB::table('users as user')
        ->join('product_ordering as order','user.uid','=','order.uid')
        ->select(DB::raw('count(order.uname) as order_number'))
        ->whereBetween('order.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->groupBy('order.uname')
        ->get();

        if($order){
            return count($order);  //聚合出的结果为 [{"count_pople":3},{"count_pople":2},{"count_pople":1},{"count_pople":1}]  直接统计数组长度就是人数 
        }
        else{
            return 0;
        }
        
    }

    /**获取投注笔数 */
    protected function get_order_number($request){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);
        $order = DB::table('users as user')
        ->join('product_ordering as order','user.uid','=','order.uid')
        ->select(DB::raw('count(order.uname) as order_number'))
        ->whereBetween('order.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->first();
        if($order){
            return $order->order_number;  
        }
        else{
            return 0;
        }
        
    }

    /**获取注册人数 */
    protected function get_zhuche_pople($request){ 
        $querTime = Tools::get_date_to_date($request);
        $where    = Tools::Countroles($this->mthod,$this->id);
        $user  = DB::table('users as user')
        ->select(DB::raw('count(uid) as zhuche_number'))
        ->whereBetween('user.created_at',[$querTime['start_at'],$querTime['end_at']])
        ->where($where)
        ->first();
        if($user){
            return $user->zhuche_number;  
        }
        else{
            return 0;
        }
        
    }

    /**
     * 分割线  总数据统计
     * 
     * **/
    
    /**获取注册总人数 */
    protected function get_zhuche_sum_pople(){ 

        $where    = Tools::Countroles($this->mthod,$this->id);
        $user  = DB::table('users as user')
        ->select(DB::raw('count(uid) as zhuche_number'))
        ->where($where)
        ->first();
        if($user){
            return $user->zhuche_number;  
        }
        else{
            return 0;
        }
        
    }
    /**获取充值总金额 */
    protected function get_sum_price(){ 

        $where    = Tools::Countroles($this->mthod,$this->id);

        $recharge = DB::table('users as user')
        ->join('users_recharge as ur','user.uid','=','ur.uid')
        ->select(DB::raw('sum(ur.price) as sum_money'))
        ->where($where)
        ->where('ur.action','1')  //不统计彩金
        ->first();

        if($recharge){
            return $recharge->sum_money ?? 0;
        }
        else{
            return 0;
        }
        
    }

     /**获取提现总金额 */
     protected function get_withdraw_sum_money(){ 

        $where    = Tools::Countroles($this->mthod,$this->id);

        $withdraw = DB::table('users as user')
        ->join('users_withdraw as ur','user.uid','=','ur.uid')
        ->select(DB::raw('sum(ur.price) as sum_money'))
        ->where($where)
        ->where('ur.status',1)  //只有审核通过了的才能算被正式提现
        ->first();

        if($withdraw){
            return $withdraw->sum_money ?? 0;
        }
        else{
            return 0;
        }
        
    }


    /**获取投注(订单)总金额 */
    protected function get_order_sum_money(){ 

        $where    = Tools::Countroles($this->mthod,$this->id);

        $order = DB::table('users as user')
        ->join('product_ordering as order','user.uid','=','order.uid')
        ->select(DB::raw('sum(order.quantity_price) as sum_money'))
        ->where($where)
        ->first();

        if($order){
            return $order->sum_money ?? 0;
        }
        else{
            return 0;
        }
        
    }
    /**获取总投注笔数(订单数量) */
    protected function get_order_sum_number(){ 

        $where    = Tools::Countroles($this->mthod,$this->id);
        $order = DB::table('users as user')
        ->join('product_ordering as order','user.uid','=','order.uid')
        ->select(DB::raw('count(order.uname) as order_number'))
        ->where($where)
        ->first();
        if($order){
            return $order->order_number;  
        }
        else{
            return 0;
        }
        
    }
     /**获取中奖总金额 */
     protected function get_order_sum_lottery(){ 

        $where    = Tools::Countroles($this->mthod,$this->id);

        $order = DB::table('users as user')
        ->join('product_ordering as order','user.uid','=','order.uid')
        ->select(DB::raw('sum(order.lottery_price) as sum_lottery'))
        ->where($where)
        ->first();

        if($order){
            return $order->sum_lottery ?? 0;
        }
        else{
            return 0;
        }
        
    }

}
