<template>
	<view>
		<view class="vip" v-for="(item,index) in levelArr" :key="index">
			<image :src="'/static/img/vip/'+index+'.png'"></image>
			<view class="name">{{$t("new3.txt"+index)}}</view>
			<view class="jifen">
				<span>{{i18n.jifen}}</span>{{item.price}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data(){
			return {
				levelArr:[]
			}
		},
		computed: {
		    i18n () {
		       return this.$t("mine")
		    }
		},
		onLoad(){
			this.getList()
		},
		onReady() {
			uni.setNavigationBarTitle({
			    title: this.i18n.level
			})
		},
		methods:{
			getList(){
				var that = this;
				this.$tools.Get('api/user/lv', {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then(function(res) {
				      that.levelArr = res.data
				}).catch(function(error) {
				    //这里只会在接口是失败状态返回，不需要去处理错误提示
				    console.log(error);
				});
			}
		}
	}
</script>

<style scoped lang="less">
	page{
		background-color: #fff;
		padding-top: 20rpx;
	}
	.vip{
		margin:0 30rpx 10rpx;
		position: relative;
		image{
			width: 100%;
			height: 200rpx;
		}
		.jifen{
			position: absolute;
			bottom: 69rpx;
			left: 185rpx;
			font-size: 32rpx;
			font-family: Euclid Circular A;
			font-weight: 500;
			color: #333333;
			height: 36rpx;
			display: flex;
			align-items: center;
			span{
				height: 36rpx;
				background: #81898B;
				border-radius: 4rpx;
				padding: 0 5rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 10rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
			}
		}
		.name{
			position: absolute;
			left: 185rpx;
			top: 40rpx;
			font-size: 32rpx;
			font-family: Euclid Circular A;
			font-weight: bold;
			color: #333333;
		}
	}
</style>
