<template>
	<view class="noticeList">
		<view class="listItem" v-for="(item, index) in noticeList" :key="index" @click="gotoDetail(item)">
			<view class="listItem-top">
				<view class="listItem-txt">{{item.title}}<span>{{item.updated_at}}</span></view>
			</view>
			<view class="listItem-det">{{item.body.replace(/<.*?>/g,"")}}</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "noticeList",
		data() {
			return {
				noticeList: [],
                pageIndex: 1,
                pageSize: 10,
				status: 'more',
                hasMore: false,
				staffId: 0
			}
		},
		onLoad() {
			this.queryByInput() // 获取公告列表
		},
		computed: {
		    i18n () {
		       return this.$t("notice")
		    }
		},
        onShow() {

        },
		onReady() {
			uni.setNavigationBarTitle({
				title:this.i18n.notice
			})
		},
		//下拉刷新
		onPullDownRefresh(){
		    uni.stopPullDownRefresh();
		    this.noticeList = [];
		    this.queryByInput()
		},
		//上拉加载
		onReachBottom(){
		    if (this.status == 'noMore'){
		        return;
		    }
		    this.pageIndex ++;
		    this.getUserInfo(); // 获取活动列表
		},
		methods: {
			// 获取用户信息
			getUserInfo(){
			    this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
			        if(res.status == 200){
						this.staffId = res.data.staff_id
						this.getMessageAll() // 获取公告列表
			        } else {
			            uni.showToast({
			                title: res.msg,
			                duration: 1500,
			                icon:'none'
			            });
			        }
			    })
			},

			// 初始数据
			queryByInput(){
			    this.pageIndex = 1;
			    this.getUserInfo(); //获取公告列表
			},

			// 获取公告列表
			getMessageAll(){
				var that = this;
			    this.$tools.Get("api/user/centerinfo/bulletin",{
			    	staff_id: this.staffId,
					language: uni.getStorageSync('lang')
				 }).then((res) =>{
					//这里只会在接口是成功状态返回
					that.noticeList = res.data
			    })
			},

			// 详情页
			gotoDetail(item){
				uni.navigateTo({
				    url: '/pages/mine/noticeDetail?strTitle='+ item.title + '&strings=' + item.body + '&strDate=' + item.created_at
				});
			}
		}
	}
</script>

<style scoped lang="less">
	page{
		background: #FFFFFF;
		.noticeList{
			.listItem{
				margin: 0 20rpx;
				border-bottom: 1rpx solid #E0E0E0;
				padding-bottom: 36rpx;
				padding-top: 33rpx;
				.listItem-top{
					height: 80rpx;
					display: flex;
					align-items: center;
				}
				.listItem-ball{
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 34rpx;
					background: #00b7f0;
					image{
						width: 35rpx;
						height: 39rpx;
					}
				}
				.listItem-txt{
					font: normal normal medium 26rpx/26rpx Roboto;
					letter-spacing: 0;
					color: #38383D;
					height: 80rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					span{
						font: normal normal normal 24rpx/24rpx Roboto;
						letter-spacing: 0;
						color: #787878;
						margin-top: 14rpx;
					}
				}
				.listItem-det{
					padding: 0 12rpx;
					font: normal normal normal 26rpx/38rpx Roboto;
					letter-spacing: 0;
					color: #212121;
					margin-top: 26rpx;
				}
			}
		}
	}
</style>
