{"name": "@intlify/vue-devtools", "version": "9.2.2", "description": "@intlify/vue-devtools", "keywords": ["i18n", "internationalization", "intlify", "vue-devtools"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/vue-devtools#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/vue-devtools"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "index.mjs", "dist"], "main": "index.js", "module": "dist/vue-devtools.esm-bundler.js", "types": "dist/vue-devtools.d.ts", "dependencies": {"@intlify/core-base": "9.2.2", "@intlify/shared": "9.2.2"}, "engines": {"node": ">= 14"}, "buildOptions": {"name": "IntlifyVueDevtools", "formats": ["esm-bundler", "cjs"]}, "exports": {".": {"import": {"node": "./index.mjs", "default": "./dist/vue-devtools.esm-bundler.js"}, "require": "./index.js"}, "./dist/*": "./dist/*", "./index.mjs": "./index.mjs", "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "sideEffects": false}