<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UsersWithdraw;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\UsersBillInfo as BillTable;
use App\Models\User;
use App\Models\UsersWithdraw as Withdraw;
use Illuminate\Support\Facades\Auth;
use Dcat\Admin\Admin;
use App\Http\Controllers\Tools;
class UsersWithdrawController extends AdminController
{


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UsersWithdraw(), function (Grid $grid) {
            
      
            $grid->disableCreateButton();
            $grid->withBorder();
            $grid->column('id');
            $grid->column('uname','用户账号');
            $grid->column('bname','持卡人');
            $grid->column('bank_name','银行');
            $grid->column('bank_id','卡号');
            $grid->column('price');
            $grid->column('amount');
            $grid->column('status')->display(function(){

                  if($this->status==0){
                    return '<span class="label create-form'.$this->id.'" style="background:#ffcd18">待审核</span>';
                  }
                  else if($this->status==1){
                    return '<span class="label" style="background:#21b978">审核成功</span>';
                  }
                  else{
                    return '<span class="label" style="background:#e30000">审核驳回</span>';
                  }


            });
            $grid->column('system_msg','系统备注');
           
            $grid->column('msg','备注信息');
            $grid->column('created_at');
            $grid->column('updated_at');
      
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit(); 
                $actions->disableView();
                $ids =  $actions->row->id;
                UsersWithdrawController::build( $ids);

            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UsersWithdraw(), function (Show $show) {
            $show->field('id');
            $show->field('uid');
            $show->field('action');
            $show->field('price');
            $show->field('status');
            $show->field('system_msg');
            $show->field('action_user');
            $show->field('amount');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UsersWithdraw(), function (Form $form) {
            $roles = Admin::user()->roles; //获取权限分组
            $form->text('price','提现金额')->disable();
            $form->text('amount','账户余额')->disable();


            if($roles[0]->slug=='administrator'){

                $form->select('status')->options([
                    '0' =>'待审核',
                    '1' =>'审核通过',
                    '2' =>'审核驳回'
                ]);

            }
            else{

                $form->select('status')->options([
                    '0' =>'待审核',
                    '2' =>'审核驳回'
                ]);

            }
           


            $form->text('system_msg','系统备注')->disable();
            $form->text('msg','用户备注');

            $form->confirm('您确定要提交吗？', $form->code);
            $form->disableResetButton();
        });
    }

    static function build($ids)
    {

        Form::dialog('提现审核')
            ->click('.create-form'.$ids.'') // 绑定点击按钮
            ->url(Tools::http().'member/withdraw/'.$ids.'/edit') // 表单页面链接，此参数会被按钮中的 “data-url” 属性替换。。
            ->width('700px') // 指定弹窗宽度，可填写百分比，默认 720px
            ->height('650px') // 指定弹窗高度，可填写百分比，默认 690px
            ->success(
                    <<<JS
                    //更新账单 和 用户表 金额
                    if(response.status){
                       ajax_user_bill($ids); //JS请求 跟下面那个PHP 不是一个事情
                    }
                    
                    // 保存成功之后刷新页面
                    Dcat.reload();
                    JS 
            ); 

     
                 
    }

    //更新账单
    protected function updata_user_bill($ids)
    {
         $Withdraw = Withdraw::find($ids);

       
         if($Withdraw->status==1){  //证明审核通过了
            $now_money = $Withdraw->amount - $Withdraw->price;
            $user        = User::where('uid',$Withdraw->uid)->first();
            $user_updata = User::where('uid',$Withdraw->uid)->update(['price'=>$now_money]);
            $admin       = Auth::guard('admin')->user();
            $create_data = [
                'uid'   => $user->uid,
                'uname' =>  $user->uname,
                'action' => 3,  //3表示提现操作
                'price' => 0 - $Withdraw->price,  //被提现的金额
                'amount' => $Withdraw->amount,  //提现前金额
                'direction' => 2, //出账
                'action_user'=>  $admin->username,

            ];
            $bill = BillTable::create($create_data);

         }

    }


    
    //获取提现数量
    protected function getcount()
    {
         $Withdraw = Withdraw::where('status',0)->get();
         $count = count($Withdraw);
         
         return Tools::Returnajax($count,'获取成功',200);

    }

}
