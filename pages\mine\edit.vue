<template>
    <view>
        <view class="page-name">{{title}}</view>
        <view class="page-input">
            <input type="number" v-model="txt" v-if="type===3" />
            <picker @change="bindPickerChange" :value="sexIn" :range="sex" v-if="type===4">
                <view class="uni-input">{{sex[sexIn]}}</view>
            </picker>
            <picker @change="bindPickerChange2" :value="nationalityIn" :range="nationality" v-if="type===5">
                <view class="uni-input">{{nationality[nationalityIn]}}</view>
            </picker>
            <input type="number" v-model="txt" v-if="type===6" />
            <input type="text" v-model="txt" v-if="type===7" />
        </view>
        <view class="page-tips">{{tips}}</view>
        <view class="page-btn" @click="setDate">{{$t("new.userSave")}}</view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                title: '',
                value: '',
                tips: '',
                type: 0,
                userInfo:{},
                txt: '',
                par: {},
                sex: [],
                sexIn: 2,
                nationality: [],
                nationalityIn: 0
            };
        },
        // 页面加载
        onLoad(options) {
            this.type = parseInt(options.type)
            this.sex = [this.$t('new.sex1'), this.$t('new.sex2'), this.$t('new.sex3')]
            this.nationality = this.$t('new.nationality').split('、')
        },
        onReady() {
            if(this.type === 1) {
                this.title = this.$t("new.userName")
                this.value = 'bname'
                this.tips = this.$t("new.userTips1")
            }
            if(this.type === 2) {
                this.title = this.$t("new.userName2")
                this.value = 'uname'
                this.tips = this.$t("new.userTips2")
            }
            if(this.type === 3) {
                this.title = this.$t("new.userName3")
                this.value = 'birthday'
                this.tips = this.$t("new.userTips3")
            }
            if(this.type === 4) {
                this.title = this.$t("new.userName4")
                this.value = 'sex'
                this.tips = this.$t("new.userTips4")
            }
            if(this.type === 5) {
                this.title = this.$t("new.userName5")
                this.value = 'nationality'
                this.tips = this.$t("new.userTips5")
            }
            if(this.type === 6) {
                this.title = this.$t("new.userName6")
                this.value = 'phone'
                this.tips = this.$t("new.userTips6")
            }
            if(this.type === 7) {
                this.title = this.$t("new.userName7")
                this.value = 'email'
                this.tips = this.$t("new.userTips7")
            }
            uni.setNavigationBarTitle({
                title: this.title
            })
        },
        onShow() {
            this.getUserInfo()
        },
        methods:{
            bindPickerChange: function(e) {
                this.sexIn = e.detail.value
            },
            bindPickerChange2: function(e) {
                this.nationalityIn = e.detail.value
            },
            getUserInfo(){
                this.$tools.Post("api/user/info", {
                    api_token: uni.getStorageSync('token'),
                    language: uni.getStorageSync('lang')
                }).then(res=>{
                    if(res.status==200){
                        this.userInfo=res.data;
                        if(this.type===3){
                            this.txt = this.userInfo.birthday
                        }
                        if(this.type===4){
                            if(this.userInfo.sex===this.$t('new.sex1')) this.sexIn = 0
                            if(this.userInfo.sex===this.$t('new.sex2')) this.sexIn = 1
                            if(this.userInfo.sex===this.$t('new.sex3')) this.sexIn = 2
                        }
                        if(this.type===5){
                            this.nationalityIn = this.userInfo.nationality
                        }
                        if(this.type===6){
                            this.txt = this.userInfo.phone
                        }
                        if(this.type===7){
                            this.txt = this.userInfo.email
                        }
                    }
                });
            },
            setDate(){
                if(this.type===3){
                    this.par = {
                        uid: this.userInfo.uid,
                        api_token: uni.getStorageSync('token'),
                        birthday: this.txt,
                        language: uni.getStorageSync('lang')
                    }
                }
                if(this.type===4){
                    this.par = {
                        uid: this.userInfo.uid,
                        api_token: uni.getStorageSync('token'),
                        sex: this.sex[this.sexIn],
                        language: uni.getStorageSync('lang')
                    }
                }
                if(this.type===5){
                    this.par = {
                        uid: this.userInfo.uid,
                        api_token: uni.getStorageSync('token'),
                        nationality: this.nationalityIn,
                        language: uni.getStorageSync('lang')
                    }
                }
                if(this.type===6){
                    this.par = {
                        uid: this.userInfo.uid,
                        api_token: uni.getStorageSync('token'),
                        phone: this.txt,
                        language: uni.getStorageSync('lang')
                    }
                }
                if(this.type===7){
                    this.par = {
                        uid: this.userInfo.uid,
                        api_token: uni.getStorageSync('token'),
                        email: this.txt,
                        language: uni.getStorageSync('lang')
                    }
                }
                this.$tools.Post("api/user/changeinfo", this.par).then(res=>{
                    if(res.status==200){
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                        setTimeout(function () {
                            uni.navigateTo({
                                url: '/pages/mine/user'
                            })
                        },2000)
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                });
            }
        }
    }
</script>

<style lang="less" scoped>
    page{
        background: #FFFFFF;
        padding: 0 32rpx;
        .page-name{
            height: 99rpx;
            display: flex;
            align-items: center;
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #333333;
        }
        .page-input{
            height: 88rpx;
            background: #EBEFF5;
            border: 1rpx solid #172D52;
            border-radius: 20rpx;
            padding: 0 22rpx;
            input{
                height: 88rpx;
                line-height: 88rpx;
                font-size: 32rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #333;
                background: #EBEFF5;
            }
        }
        .page-tips{
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #999999;
            margin-top: 20rpx;
        }
        .page-btn{
            height: 100rpx;
            background: #65B11D;
            border-radius: 20rpx;
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 58rpx;
        }
        ::v-deep .uni-input{
            height: 85rpx;
            background: #EBEFF5;
        }
    }
</style>
