(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-public-login"],{2849:function(e,t,n){var a=n("2c96");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("967d").default;i("03ef76bb",a,!0,{sourceMap:!1,shadowMode:!1})},"2c96":function(e,t,n){var a=n("c86c"),i=n("2ec5"),o=n("dcd8");t=a(!1);var r=i(o);t.push([e.i,"uni-page-body .loginWrap[data-v-edddea5e]{background:url("+r+") no-repeat top;background-size:100% 100%;min-height:100vh}uni-page-body .loginWrap .loginWrap_cont_title[data-v-edddea5e]{height:%?500?%;display:flex;align-items:center;justify-content:center}uni-page-body .loginWrap .loginWrap_cont_title uni-image[data-v-edddea5e]{width:%?230?%;height:%?230?%}uni-page-body .loginWrap .cont_textCon_item[data-v-edddea5e]{display:flex;margin:%?50?% %?55?% 0 %?55?%;flex-direction:column;justify-content:center;font-size:%?32?%;font-family:PingFang SC;font-weight:500;color:#fff}uni-page-body .loginWrap .cont_textCon_item .inputStyle[data-v-edddea5e]{margin-top:%?11?%;height:%?88?%;background:#fff;border-radius:%?20?%;padding:0 %?19?%;border:0;font-size:%?32?%;font-family:PingFang SC;font-weight:500;color:#333}uni-page-body .loginWrap .cont_textCon_item .inpuyh[data-v-edddea5e]{color:#b3b3b3}uni-page-body .loginWrap .loginWrap_cont_tip[data-v-edddea5e]{padding:%?63?% %?65?% 0 %?65?%;font-size:%?28?%;font-family:PingFang SC;font-weight:500;color:#fff}uni-page-body .loginWrap .loginWrap_cont_tip span[data-v-edddea5e]{color:#96e727}uni-page-body .loginWrap .cont_textCon_but[data-v-edddea5e]{height:%?88?%;background:#65b11d;border-radius:%?20?%;font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#fff;display:flex;align-items:center;justify-content:center;margin:%?60?% %?55?% 0 %?55?%}uni-page-body .loginWrap .remember-password[data-v-edddea5e]{margin:%?20?% %?55?%;display:flex;align-items:center;color:#fff}uni-page-body .loginWrap .remember-password uni-checkbox[data-v-edddea5e]{margin-right:%?10?%;-webkit-transform:scale(.8);transform:scale(.8)}uni-page-body .loginWrap .remember-password uni-text[data-v-edddea5e]{font-size:%?28?%}uni-page-body .loginWrap .lang-switch[data-v-edddea5e]{position:absolute;top:%?40?%;right:%?40?%;display:flex;align-items:center;padding:%?10?% %?20?%;background:hsla(0,0%,100%,.2);border-radius:%?30?%;z-index:999}uni-page-body .loginWrap .lang-switch .lang-icon[data-v-edddea5e]{font-size:%?32?%;margin-right:%?10?%}uni-page-body .loginWrap .lang-switch uni-text[data-v-edddea5e]{color:#fff;font-size:%?28?%}uni-page-body[data-v-edddea5e] :-moz-placeholder{\n  /* Mozilla Firefox 4 to 18 */color:#b3b3b3}uni-page-body[data-v-edddea5e] ::-moz-placeholder{\n  /* Mozilla Firefox 19+ */color:#b3b3b3}uni-page-body uni-input[data-v-edddea5e]:-ms-input-placeholder{color:#b3b3b3}uni-page-body uni-input[data-v-edddea5e]::-webkit-input-placeholder{color:#b3b3b3}",""]),e.exports=t},"2ec5":function(e,t,n){"use strict";e.exports=function(e,t){return t||(t={}),e=e&&e.__esModule?e.default:e,"string"!==typeof e?e:(/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e)}},3965:function(e,t,n){"use strict";var a=n("2849"),i=n.n(a);i.a},"4a40":function(e,t,n){"use strict";n.r(t);var a=n("86ac"),i=n("6857");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("3965");var r=n("828b"),s=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"edddea5e",null,!1,a["a"],void 0);t["default"]=s.exports},6857:function(e,t,n){"use strict";n.r(t);var a=n("b4ab"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},7429:function(e,t,n){e.exports=n.p+"static/img/logo.png"},"86ac":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"loginWrap"},[a("v-uni-view",{staticClass:"lang-switch",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goLang.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"lang-icon"},[e._v("🌐")]),a("v-uni-text",[e._v(e._s(e.currentLang))])],1),a("v-uni-view",{staticClass:"loginWrap_cont_title"},[a("v-uni-image",{attrs:{mode:"aspectFit",src:n("7429")}})],1),a("v-uni-view",{staticClass:"cont_textCon_item"},[a("v-uni-text",[e._v(e._s(e.$t("login.tit1")))]),a("v-uni-input",{staticClass:"inputStyle",attrs:{type:"text","placeholder-class":"inpuyh",placeholder:e.$t("login.txt1")},model:{value:e.username,callback:function(t){e.username=t},expression:"username"}})],1),a("v-uni-view",{staticClass:"cont_textCon_item"},[a("v-uni-text",[e._v(e._s(e.$t("login.tit2")))]),a("v-uni-input",{staticClass:"inputStyle",attrs:{type:"password","placeholder-class":"inpuyh",placeholder:e.$t("login.txt2")},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}})],1),a("v-uni-view",{staticClass:"remember-password"},[a("v-uni-checkbox",{attrs:{checked:e.rememberMe},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.rememberMe=!e.rememberMe}}}),a("v-uni-text",[e._v("Remember Password")])],1),a("v-uni-view",{staticClass:"cont_textCon_but",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.signIn()}}},[e._v(e._s(e.$t("login.btn")))]),a("v-uni-view",{staticClass:"loginWrap_cont_tip",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.gotoRegister()}}},[e._v(e._s(e.$t("login.txt3"))),a("span",[e._v(e._s(e.$t("login.txt4")))])])],1)},i=[]},b4ab:function(e,t,n){"use strict";n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("2634")),o=a(n("2fdc"));n("f7a5");var r={data:function(){return{username:"",password:"",headTitle:"",systemInfo:{},langage:"",rememberMe:!1,currentLang:"English",isLoading:!1,loginTimeout:null}},onLoad:function(){this.$tools.clearSensitiveApiCache(),this.preloadLanguage(),uni.setNavigationBarTitle({title:this.$t("login.title")})},mounted:function(){var e=uni.getStorageSync("username"),t=uni.getStorageSync("password");e&&t?(this.username=e,this.password=t,this.rememberMe=!0):(this.username="",this.password="")},methods:{preloadLanguage:function(){try{var e,t=uni.getSystemInfoSync().language,n=t.slice(0,2).toLowerCase();e={zh:"CN",es:"ES",pt:"PT",id:"IDN",vi:"VN"}[n]||"EN";var a=uni.getStorageSync("lang")||e;uni.setStorageSync("lang",a),this.$i18n.locale=a;this.currentLang={CN:"简体中文",EN:"English",ES:"Español",PT:"Português",IDN:"Bahasa Indonesia",VN:"Tiếng Việt",TW:"繁體中文"}[a]||"English"}catch(i){console.error("Language initialization error:",i),this.currentLang="English"}},goLang:function(){uni.navigateTo({url:"/pages/public/lang"})},signIn:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var n,a;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:if(e.username){t.next=5;break}return uni.showToast({title:e.$t("login.txt1"),duration:1500,icon:"none"}),t.abrupt("return");case 5:if(e.password){t.next=8;break}return uni.showToast({title:e.$t("login.txt2"),duration:1500,icon:"none"}),t.abrupt("return");case 8:return e.isLoading=!0,uni.showLoading({title:"loading..."}),e.loginTimeout=setTimeout((function(){e.isLoading&&(e.isLoading=!1,uni.hideLoading(),uni.showToast({title:e.$t("tool.errMsg"),duration:1500,icon:"none"}))}),15e3),t.prev=11,n={uname:e.username,password:e.password,language:uni.getStorageSync("lang")},t.next=15,e.$tools.Get("api/user/login",n);case 15:a=t.sent,e.loginTimeout&&(clearTimeout(e.loginTimeout),e.loginTimeout=null),200===a.status?(e.rememberMe?(uni.setStorageSync("username",e.username),uni.setStorageSync("password",e.password)):(uni.removeStorageSync("username"),uni.removeStorageSync("password")),e.$tools.setLoginStatus(a.data,e.rememberMe),uni.setStorageSync("loginInfo",n),uni.setStorageSync("less",!0),e.preloadHomeData(),uni.reLaunch({url:"../home/<USER>"})):uni.showToast({title:a.msg,duration:1500,icon:"none"}),t.next=24;break;case 20:t.prev=20,t.t0=t["catch"](11),console.error("Login error:",t.t0),uni.showToast({title:e.$t("tool.errMsg"),duration:1500,icon:"none"});case 24:return t.prev=24,e.isLoading=!1,uni.hideLoading(),t.finish(24);case 28:case"end":return t.stop()}}),t,null,[[11,20,24,28]])})))()},preloadHomeData:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{e.$tools.Get("api/user/info",{api_token:uni.getStorageSync("token"),language:uni.getStorageSync("lang")})}catch(n){console.error("Preload home data error:",n)}case 1:case"end":return t.stop()}}),t)})))()},gotoRegister:function(){this.isLoading||uni.navigateTo({url:"/pages/public/register"})}},beforeDestroy:function(){this.loginTimeout&&(clearTimeout(this.loginTimeout),this.loginTimeout=null)}};t.default=r},dcd8:function(e,t,n){e.exports=n.p+"static/img/login.jpg"}}]);