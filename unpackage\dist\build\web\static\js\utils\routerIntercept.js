// 白名单页面（不需要登录验证的页面）
const whiteList = [
    '/pages/public/login',
    '/pages/public/register',
    '/pages/public/lang'
];

// 路由拦截
const routerIntercept = {
    // 页面跳转前
    beforeEach(to) {
        const token = uni.getStorageSync('token');
        
        // 在白名单中直接通过
        if (whiteList.includes(to)) {
            return true;
        }
        
        // 未登录跳转到登录页
        if (!token) {
            uni.reLaunch({
                url: '/pages/public/login'
            });
            return false;
        }
        
        return true;
    },
    
    // 检查token是否过期
    checkTokenExpired() {
        const token = uni.getStorageSync('token');
        if (!token) return true;
        
        // 这里可以添加token过期验证逻辑
        // 比如解析JWT token或者调用后端验证接口
        
        return false;
    },
    
    // 处理token过期
    handleTokenExpired() {
        uni.removeStorageSync('token');
        uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none',
            duration: 2000
        });
        setTimeout(() => {
            uni.reLaunch({
                url: '/pages/public/login'
            });
        }, 2000);
    }
};

export default routerIntercept; 