<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ContentLetter;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Http\Controllers\Tools;
use Illuminate\Support\Facades\Auth;
class ContentLetterController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ContentLetter(), function (Grid $grid) {
            Tools::viewroles($grid);  //视图权限筛选
            $grid->column('title');
            $grid->column('body');
    
            $grid->column('created_at');
          
        
            $grid->enableDialogCreate(); //开启 弹窗新增

            $grid->actions(function (Grid\Displayers\Actions $actions) {
               // $actions->disableDelete();
                $actions->disableEdit();  //禁用普通编辑 
                $actions->QuickEdit();    // 启用快速编辑（弹窗）
                $actions->disableView();

            });
        
   
            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();

                // 注意切换为panel布局方式时需要重新调整表单字段的宽度
                $filter->equal('title','名称')->width(3);
            });

        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ContentLetter(), function (Show $show) {
            $show->field('id');
            $show->field('target');
            $show->field('target_user');
            $show->field('win_mods');
            $show->field('title');
            $show->field('msg');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ContentLetter(), function (Form $form) {
          
         
            $form->select('target')->options([1 => '个人', 2 => '所有人']);
            $form->textarea('target_user')->rows(10);
            $form->switch('win_mods');
            $form->text('title');
            $form->textarea('body')->rows(10);
            if($form->isCreating()){
                $admin   = Auth::guard('admin')->user(); 
                $form->hidden('staff_id');
                $form->hidden('team_id');
                $form->staff_id = $admin->id;
                $form->team_id = $admin->team_id;

            }
        });
    }
}
