<template>
	<view>
		<view class="top">{{i18n.yue}}：{{Math.floor(Number(withdrawalInfo.price))}}</view>
		<view class="title">{{$t('new.remTxt1')}}<span @click="toRecord('/pages/mine/billRecord')">{{$t("bill").head_title}}</span></view>
		<view class="list">
			<view class="listItem">
				<span>{{i18n.user}}</span>
				<text class="right">{{withdrawalInfo.bname}}</text>
			</view>
			<view class="listItem">
				<span>{{i18n.huizh}}</span>
				<picker :value="index" style="margin-right: -26rpx;" :range="bankCardList" range-key="bank_id" @change="bindPickerChange">
				    <text class="uni-input">{{bankCard}}</text>
				</picker>
			</view>
			<view class="listItem" style="border: 0">
				<span>{{i18n.huiMoney}}</span>
				<input type="digit" v-model="money" :placeholder="i18n.toast_money" confirm-type='done' placeholder-class="input-placeholder"/>
			</view>
		</view>

		<view class="btn" @click="submit">{{i18n.submit}}</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				index:0,
				bankId:"",
                bankCard:"请选择回款账户",
				bankName: '',
                money:"",
				array:[
					{name:"王立",account:"555555"},
					{name:"张三",account:"666"}
				],
                withdrawalInfo:{},
                bankCardList:[],
                bankCardList2:[],
				backMoney:'',
				shenTime:'',
				timer:null,
				full: 0,
				minPrice: 0
			}
		},
        onLoad() {
			this.getWi()
            this.getWithdrawalInfo()

        },
		computed: {
		    i18n () {
		       return this.$t("remittance")
		    }
		},
		onShow() {
			this.bankCard = this.i18n.select


		},
		onUnload() {
			// clearInterval(this.timer)
			// this.timer = null
		},
		onReady() {
			uni.setNavigationBarTitle({
				title:this.i18n.head_title
			})
		},
		methods: {
			toRecord(value){
				uni.navigateTo({
					url: value
				});
			},
			getTime:function(){
						var date = new Date(),
						year = date.getFullYear(),
						month = date.getMonth() + 1,
						day = date.getDate(),
						hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours(),
						minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes(),
						second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
						month >= 1 && month <= 9 ? (month = "0" + month) : "";
						day >= 0 && day <= 9 ? (day = "0" + day) : "";
						var timer = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
						return timer;
			},
            // 获取银行卡列表
            getBankCardList(){
                this.$tools.Post("api/user/bank/get", {
					uid: this.withdrawalInfo.uid,
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
                    if(res.status == 200){
						if(!res.data){

						}else{
							this.bankCardList = [...res.data]
							this.bankId = this.bankCardList[this.index].bank_id;
							this.bankCard = this.bankCardList[this.index].bank_id;
							this.bankName = this.bankCardList[this.index].bank_name;
						}

                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
            // 获取提现信息
            getWithdrawalInfo(){
                this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
                    if(res.status == 200){
                        this.withdrawalInfo = res.data;
						this.backMoney = this.i18n.min + res.data.price
						this.getBankCardList()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },

			bindPickerChange(e){
				this.index = e.detail.value;
				this.bankId = this.bankCardList[this.index].bank_id;
                this.bankCard = this.bankCardList[this.index].bank_id;
				this.bankName = this.bankCardList[this.index].bank_name;
			},
			getWi(){
			    this.$tools.Get("api/system/info",{
					language: uni.getStorageSync('lang')
				}).then((res) =>{
			        if(res.status === 200){
						this.full = res.data[0].is_int
						this.minPrice = res.data[0].minPrice
			        } else {
			            uni.showToast({
			                title: res.msg,
			                duration: 1500,
			                icon:'none'
			            });
			        }
			    })
			},

            submit(){
				if(parseInt(this.withdrawalInfo.credit)<100){
					uni.showToast({
						title: this.$t('new.mineNew2'),
						duration: 1500,
						icon:'none'
					});
					return false
				}
                if(this.full==1){
                    if(String(this.money).indexOf(".")>-1){
						uni.showToast({
						    title: this.i18n.noWith,
						    duration: 1500,
						    icon:'none'
						});
						return false
					}
                }
				if(!this.bankId){
				    uni.showToast({
				        title: this.i18n.select,
				        duration: 1500,
				        icon:'none'
				    });
				    return false
				}
				if(!this.money){
				    uni.showToast({
				        title: this.i18n.toast_money,
				        duration: 1500,
				        icon:'none'
				    });
				    return false
				}
				else{
				    if(Number(this.money) < Number(this.minPrice)){
				        uni.showToast({
				            title: this.i18n.wMin+this.minPrice,
				            duration: 1500,
				            icon:'none'
				        });
				        return false
				    }
				}
				this.submitWithdrawal()
            },

            //提现申请
            submitWithdrawal(){
                var params = {
                    money: this.money,
                    bank_id: this.bankId,
					bank_name: this.bankName,
					api_token: uni.getStorageSync('token'),
					uid: this.withdrawalInfo.uid,
					language: uni.getStorageSync('lang')
                }
                this.$tools.Post("api/user/withdraw/add",params).then((res) =>{
                    if(res.status == 200){
                        uni.showToast({
                            title: this.i18n.succ,
                            duration: 1000,
                            icon:'none',
                        	success(){
                        		setTimeout(()=> {
                        			uni.navigateTo({
                        			    url: '/pages/mine/mine'
                        			});
                        		}, 1000)
                        	}
                        });
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
		}
	}
</script>

<style scoped lang="less">
	.footer{
		position: absolute;
		bottom: 30rpx;
		width: calc(100% - 60rpx) ;
		.title{
			font-size: 16px;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			margin-bottom: 20rpx;
		}
		.box{
			// background: #FFC0A9;
			width: 100%;
			border: 1px solid #FFC0A9;
			border-radius: 5px;
			overflow: hidden;
			.item{
				display: flex;

				.left{
					background-color: #FCD8CB;
					display: flex;
					height: 72rpx;
					align-items: center;
					width: 230rpx;
					font-size: 16px;
					font-family: PingFang SC;
					font-weight: 500;
					color: #E78F6F;
					text-align: center;
					justify-content: center;
				}
				.right{
					background-color: #FFF8F5;
					flex: 1;
					display: flex;
					height: 72rpx;
					align-items: center;
					padding-left: 32rpx;
				}
			}
			.item:nth-child(2){
				border-top: 1px solid #FFC0A9;
				border-bottom: 1px solid #FFC0A9;
			}
		}
	}
	.myBanlace{
		height: 80px !important;
		background: linear-gradient(0deg, #FCBBA3, #FFB194);
		border-radius: 5px;
		display: flex;
		.item{
			margin: 30rpx 0 20rpx;
			display: flex;
			flex-direction: column;
			flex: 1;
			justify-content: space-between;
			border-right: 1px solid #fff;

			.money{
				font-size: 20px;
				font-family: Euclid Circular A;
				font-weight: 500;
				color: #FFFFFF;
				text-align: center;
			}
			.title{
				font-size: 14px;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				text-align: center;
			}
		}
		.item:last-child{
			border-right: none;
		}
	}
	page{
		background: #EEEEEE;
		.top{
			height: 166rpx;
			display: flex;
			align-items: center;
			padding: 0 32rpx;
			font: normal normal bold 34rpx/34rpx Roboto;
			letter-spacing: 0;
			background: #fff;
			color: #38383D;
		}
		.title{
			height: 114rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font: normal normal bold 34rpx/34rpx Roboto;
			letter-spacing: 0;
			color: #38383D;
			padding: 0 32rpx;
			span{
				font: normal normal medium 28rpx/28rpx Roboto;
				letter-spacing: 0;
				color: #65B11D;
			}
		}
		.list{
			height: 378rpx;
			background: #FFFFFF 0% 0% no-repeat padding-box;
			border-radius: 16rpx;
			margin: 0 32rpx;
			padding: 0 41rpx;
			.listItem{
				height: 120rpx;
				display: flex;
				align-items: center;
				border-bottom: 1rpx solid #707070;
				justify-content: space-between;
				span{
					font: normal normal normal 32rpx/32rpx Roboto;
					letter-spacing: 0;
					color: #38383D;
				}
				input{
					text-align: right;
					font: normal normal bold 32rpx/32rpx Roboto;
					letter-spacing: 0;
					color: #38383D;
				}
				.right{
					font: normal normal bold 32rpx/32rpx Roboto;
					letter-spacing: 0;
					color: #38383D;
				}
				.uni-input{
					font: normal normal bold 32rpx/32rpx Roboto;
					letter-spacing: 0;
					color: #38383D;
				}
			}
		}
		.btn{
			height: 100rpx;
			background: #65B11D;
			border-radius: 20rpx;
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 60rpx 35rpx 0 35rpx;
		}
	}
</style>
