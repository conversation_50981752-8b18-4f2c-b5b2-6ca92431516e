<?php
/**
 * 修复订单API错误的完整解决方案
 * 解决405和500错误问题
 */

echo "<h1>🔧 订单API错误修复工具</h1>\n";
echo "<p>修复时间: " . date('Y-m-d H:i:s') . "</p>\n";

// 1. 检查并修复UsersDm模型的null问题
echo "<h2>1. 修复UsersDm模型null访问问题</h2>\n";

$controllerFile = 'admin.inspap.com/app/Http/Controllers/ApiController.php';
if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    
    // 检查是否已经修复了第668行的问题
    $hasNullCheck = strpos($content, 'if (!$userdm) {') !== false;
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4>UsersDm null检查修复状态：</h4>\n";
    echo "<p>null检查已添加: " . ($hasNullCheck ? "✅ 是" : "❌ 否") . "</p>\n";
    echo "</div>\n";
    
    if ($hasNullCheck) {
        echo "<p style='color: green;'>✅ UsersDm null访问问题已修复</p>\n";
    } else {
        echo "<p style='color: red;'>❌ 需要手动修复UsersDm null访问问题</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ 控制器文件不存在</p>\n";
}

// 2. 清除Laravel缓存
echo "<h2>2. 清除Laravel缓存</h2>\n";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>\n";
echo "<h4>🔄 执行缓存清除命令：</h4>\n";
echo "<p>请在终端中执行以下命令：</p>\n";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px;'>\n";
echo "cd admin.inspap.com\n";
echo "php artisan route:clear\n";
echo "php artisan config:clear\n";
echo "php artisan cache:clear\n";
echo "php artisan view:clear\n";
echo "</pre>\n";
echo "</div>\n";

// 3. 检查路由配置
echo "<h2>3. 验证路由配置</h2>\n";

$routeFile = 'admin.inspap.com/routes/api.php';
if (file_exists($routeFile)) {
    $routeContent = file_get_contents($routeFile);
    
    $getRouteExists = strpos($routeContent, "Route::middleware('auth:api')->get('user/ordering/{action}','ApiController@ordering')") !== false;
    $postRouteExists = strpos($routeContent, "Route::middleware('auth:api')->post('user/ordering/{action}','ApiController@ordering')") !== false;
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4>路由配置状态：</h4>\n";
    echo "<p>GET路由: " . ($getRouteExists ? "✅ 已配置" : "❌ 未配置") . "</p>\n";
    echo "<p>POST路由: " . ($postRouteExists ? "✅ 已配置" : "❌ 未配置") . "</p>\n";
    echo "</div>\n";
}

// 4. 检查前端配置
echo "<h2>4. 验证前端配置</h2>\n";

$toolsFile = 'static/js/utils/Tools.js';
if (file_exists($toolsFile)) {
    $toolsContent = file_get_contents($toolsFile);
    
    $defaultGet = strpos($toolsContent, "method: options.method || 'GET'") !== false;
    $postMethodGet = strpos($toolsContent, "options['method'] = 'GET'; // 改为GET方法确保稳定性") !== false;
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4>前端配置状态：</h4>\n";
    echo "<p>默认GET方法: " . ($defaultGet ? "✅ 已配置" : "❌ 未配置") . "</p>\n";
    echo "<p>Post方法改为GET: " . ($postMethodGet ? "✅ 已配置" : "❌ 未配置") . "</p>\n";
    echo "</div>\n";
}

// 5. 测试建议
echo "<h2>5. 测试建议</h2>\n";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 5px; border-left: 4px solid #007bff;'>\n";
echo "<h3>🧪 测试步骤：</h3>\n";
echo "<ol>\n";
echo "<li><strong>清除所有缓存</strong><br>\n";
echo "执行上面的Laravel缓存清除命令</li>\n";

echo "<li><strong>重启Web服务器</strong><br>\n";
echo "重启Apache/Nginx以确保所有更改生效</li>\n";

echo "<li><strong>测试简单API</strong><br>\n";
echo "首先测试不需要认证的API：<br>\n";
echo "<code>http://localhost:8080/api/health</code></li>\n";

echo "<li><strong>测试用户认证</strong><br>\n";
echo "确认用户登录功能正常：<br>\n";
echo "<code>http://localhost:8080/api/user/login</code></li>\n";

echo "<li><strong>测试订单API</strong><br>\n";
echo "使用有效token测试：<br>\n";
echo "<code>http://localhost:8080/api/user/ordering/get?api_token=TOKEN&uid=USER_ID</code></li>\n";
echo "</ol>\n";
echo "</div>\n";

// 6. 错误排查指南
echo "<h2>6. 错误排查指南</h2>\n";

echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; border-left: 4px solid #dc3545;'>\n";
echo "<h3>🚨 如果仍有错误：</h3>\n";

echo "<h4>A. 405错误 (Method Not Allowed):</h4>\n";
echo "<ul>\n";
echo "<li>检查路由是否支持GET方法</li>\n";
echo "<li>确认URL路径正确</li>\n";
echo "<li>验证中间件配置</li>\n";
echo "</ul>\n";

echo "<h4>B. 500错误 (Internal Server Error):</h4>\n";
echo "<ul>\n";
echo "<li>查看Laravel日志：<code>tail -f admin.inspap.com/storage/logs/laravel.log</code></li>\n";
echo "<li>检查数据库连接</li>\n";
echo "<li>验证模型文件存在</li>\n";
echo "<li>确认UsersDm表存在且结构正确</li>\n";
echo "</ul>\n";

echo "<h4>C. 认证错误:</h4>\n";
echo "<ul>\n";
echo "<li>检查API token是否有效</li>\n";
echo "<li>确认用户ID正确</li>\n";
echo "<li>验证auth:api中间件配置</li>\n";
echo "</ul>\n";
echo "</div>\n";

// 7. 数据库检查
echo "<h2>7. 数据库检查</h2>\n";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>\n";
echo "<h4>📊 需要检查的数据库表：</h4>\n";
echo "<ul>\n";
echo "<li><strong>users_dm</strong> - 用户打码量表</li>\n";
echo "<li><strong>users</strong> - 用户表</li>\n";
echo "<li><strong>orders</strong> - 订单表</li>\n";
echo "<li><strong>products</strong> - 产品表</li>\n";
echo "</ul>\n";

echo "<p><strong>SQL检查命令：</strong></p>\n";
echo "<pre style='background: #f8f9fa; padding: 10px;'>\n";
echo "-- 检查users_dm表结构\n";
echo "DESCRIBE users_dm;\n\n";
echo "-- 检查是否有用户打码量记录\n";
echo "SELECT COUNT(*) FROM users_dm;\n\n";
echo "-- 检查特定用户的打码量记录\n";
echo "SELECT * FROM users_dm WHERE uid = 'USER_ID';\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h2>8. 修复完成状态</h2>\n";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; border-left: 4px solid #28a745;'>\n";
echo "<h3>✅ 已完成的修复：</h3>\n";
echo "<ul>\n";
echo "<li>✅ 添加了UsersDm null检查和自动创建逻辑</li>\n";
echo "<li>✅ 路由配置支持GET和POST方法</li>\n";
echo "<li>✅ 前端工具类配置为使用GET方法</li>\n";
echo "<li>✅ 控制器ordering方法支持add和get操作</li>\n";
echo "</ul>\n";

echo "<h3>🔄 需要手动执行：</h3>\n";
echo "<ul>\n";
echo "<li>🔄 清除Laravel缓存</li>\n";
echo "<li>🔄 重启Web服务器</li>\n";
echo "<li>🔄 测试API端点</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: 'Courier New', monospace; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
ol li, ul li { margin: 8px 0; }
</style>\n";

echo "<p><strong>修复完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
