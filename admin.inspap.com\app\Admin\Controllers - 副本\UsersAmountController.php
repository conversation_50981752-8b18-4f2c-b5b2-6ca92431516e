<?php

namespace App\Admin\Controllers;

use App\Admin\Metrics\Examples;
use App\Http\Controllers\Controller;
use Dcat\Admin\Http\Controllers\Dashboard;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Widgets\Card;
use App\Admin\Forms\Setting;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Illuminate\Http\Request;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Http\Controllers\Tools;
use App\Models\User;
use Dcat\Admin\Admin;
use App\Models\AdminTeam;
use Illuminate\Support\Facades\Auth;
class UsersAmountController extends AdminController
{
    public function index(Content $content)
    {
       
    }

    public function amount(Request $request){

 
        $action =  $request->input('action');
        if($action=='select'){


            $where = [
                ['uname',$request->input('uname')]
            ];

            $res  =  User::where($where)->first();
            $roles = Admin::user()->roles; //获取权限分组

            $admin  = Auth::guard('admin')->user(); 

            //员工权限
            if($roles[0]->slug=='staff'){
               
                if($res->staff_id!=$admin->id){

                    return Tools::Returnajax(null,'无权操作此用户',403);
                }
            }

            //团队权限
            if($roles[0]->slug=='team'){
               
                if($res->team_id!=$admin->team_id){

                    return Tools::Returnajax(null,'无权操作此用户',403);
                }
            }
           

            
    
            if($res):
                return Tools::Returnajax($res,'查询成功');
            else:
                return Tools::Returnajax(null,'未查询到该用户！',404);
            endif;


        }
        else{
            return Tools::Returnajax(null,'Action参数错误',404);
        }

        
    }
}
