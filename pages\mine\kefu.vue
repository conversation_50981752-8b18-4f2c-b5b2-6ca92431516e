<template>
	<view class="list_wrap">
		<iframe :src="list"  scrolling="no"  style="width: 100vw;height: 100vh;" frameborder="0" ></iframe>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: ''
			}
		},
		computed: {
		    i18n () {
		       return this.$t("mine")
		    }
		},
		onShow() {

		},
		onReady() {
			uni.setNavigationBarTitle({
				title:this.i18n.lian
			})
		},
		created() {
			this.getServiceUrl()
		},
		methods:{
			// 获取客服链接
			getServiceUrl(){
			    this.$tools.Get("api/system/info",{
					language: uni.getStorageSync('lang')
				}).then((res) =>{
			        if(res.status == 200){

			            this.list = res.data[0].extension
			        } else {
			            uni.showToast({
			                title: res.msg,
			                duration: 1500,
			                icon:'none'
			            });
			        }
			    })
			},
			jumpLink(){

			        //#ifdef H5
			            window.open(this.list)
			        //#endif

			        //#ifdef APP-PLUS
			            // plus.runtime.openWeb(item.path);
			            plus.webview.open(this.list)
			        //#endif

			},
		}
	}
</script>

<style scoped lang="less">
	.list_wrap{
		height: 100vh;
		width: 100vw;
		background: #fff;
	}
	.list-item{
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #ddd;
		.left{
			font-size: 16px;
			color: #000;
		}
		.right{
			display: flex;
			align-items: center;
			image{
				width: 40rpx;
				height: 40rpx;
			}
		}
	}
</style>
