<template>
	<view class="container">
		<view class="list_wrap">

			<view class="item">
				<view class="title">{{i18n.new}}</view>
				<input type="text" password v-model="passwordInfo.newPassword" :placeholder="i18n.place_new" confirm-type='done' placeholder-class="input-placeholder"/>
			</view>

			<view class="item">
				<view class="title">{{i18n.que}}</view>
				<input type="text" password v-model="passwordInfo.confirmWord" :placeholder="i18n.place_que" confirm-type='done' placeholder-class="input-placeholder"/>
			</view>
		</view>

		<view class="submit" @click="updatePassword">{{i18n.confim}}</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				passwordInfo:{
					password:"",
					newPassword:"",
					confirmWord:""
				},
				userInfo: {}
			}
		},
		computed: {
		    i18n () {
		       return this.$t("setpass")
		    }
		},
		onLoad(){

		},

		onShow(){
		  this.getUserInfo()
		},
		onReady(){
			uni.setNavigationBarTitle({
			 	title:this.i18n.head_title
			})
		},
		methods: {
            // 获取用户信息
            getUserInfo(){
                this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
                    if(res.status == 200){
                        this.userInfo = res.data;
						uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
			updatePassword(){
				const {password,newPassword,confirmWord} = this.passwordInfo
				// if(!password){
				//     uni.showToast({
				//         title: this.i18n.place_old,
				//         duration: 1500,
				//         icon:'none'
				//     });
				//     return false
				// }
				if(!newPassword){
				    uni.showToast({
				        title: this.i18n.place_new,
				        duration: 1500,
				        icon:'none'
				    });
				    return false
				}
				if(!confirmWord){
				    uni.showToast({
				        title: this.i18n.place_que,
				        duration: 1500,
				        icon:'none'
				    });
				    return false
				}
				if(newPassword!==confirmWord){
					uni.showToast({
					    title: this.i18n.two,
					    duration: 1500,
					    icon:'none'
					});
					return false
				}

				this.$tools.Post("api/user/changepassword",{
					uid: this.userInfo.uid,
					api_token: uni.getStorageSync('token'),
					password: confirmWord,
					language: uni.getStorageSync('lang')
				}).then((res) =>{
				    if(res.status == 200){
				        uni.showToast({
				            title: this.i18n.succ,
				            duration: 1000,
				            icon:'none',
							success(){
								setTimeout(()=> {
									uni.navigateTo({
									    url: '/pages/public/login'
									});
								}, 1000)
							}
				        });
				    } else {
				        uni.showToast({
				            title: res.msg,
				            duration: 1500,
				            icon:'none'
				        });
				    }
				})

			}
		}
	}
</script>

<style scoped lang="less">
	page{
		background: #fff;
		font-family: PingFang SC;
		.container{
			padding: 10rpx 50rpx 0 50rpx;
			.list_wrap{
				.item{
					.title{
						font-size: 32rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #333333;
						height: 102rpx;
						display: flex;
						align-items: center;
					}
					.input-placeholder{
						font-size: 32rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #B3B3B3;
					}
					input{
						height: 88rpx;
						background: #EBEFF5;
						border: 1rpx solid #172D52;
						border-radius: 20rpx;
						padding: 0 22rpx;
						line-height: 88rpx;
						font-size: 32rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333;
						background: #EBEFF5;
					}

				}
			}
			.submit{
				height: 100rpx;
				background: #65B11D;
				border-radius: 20rpx;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 58rpx;
			}

		}
	}
</style>
