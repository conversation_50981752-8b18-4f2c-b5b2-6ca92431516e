<template>
    <view class="loginWrap">
		<view class="loginWrap_cont_title"><image src="../../static/img/logo.png" /></view>
		<view class="cont_textCon_item">
			{{$t('regis.tit2')}}
			<input type="text" class="inputStyle" placeholder-class="inpuyh" :placeholder="$t('regis.txt2')" v-model="formData.username" >
		</view>
<!--		<view class="cont_textCon_item">-->
<!--			{{$t('regis.tit3')}}-->
<!--			<input type="text" class="inputStyle" placeholder-class="inpuyh" :placeholder="$t('regis.txt3')" v-model="formData.bname" >-->
<!--		</view>-->
		<view class="cont_textCon_item">
			{{$t('regis.tit4')}}
			<input type="password" class="inputStyle" placeholder-class="inpuyh" :placeholder="$t('regis.txt4')" v-model="formData.password" >
		</view>
		<view class="cont_textCon_item">
			{{$t('regis.tit4-1')}}
			<input type="password" class="inputStyle" placeholder-class="inpuyh" :placeholder="$t('regis.txt5')" v-model="formData.new_password" >
		</view>
		<view class="cont_textCon_item">
			{{$t('regis.tit1')}}
			<input type="number" maxlength="6" class="inputStyle" placeholder-class="inpuyh" :placeholder="$t('regis.txt1')" v-model="formData.staffId" >
		</view>
		<view class="cont_textCon_but" @click="submit()">{{$t('regis.btn')}}</view>
		<view class="loginWrap_cont_tip" @click="goLogin()">{{$t('regis.txt6')}}<span>{{$t('regis.txt7')}}</span></view>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                formData: {
                	username: "",
					password: "",
					new_password:"",
					staffId: "",
					bname: ""
                }
            }
        },
		onLoad() {
			uni.setNavigationBarTitle({
				title: this.$t('regis.title')
			})
		},
        methods: {
            goLogin(){
				uni.navigateTo({
					url:'/pages/public/login'
				})
			},
            // 提交
            submit(){

                if(this.formData.username.length < 6){

                     uni.showToast({
                        title:this.$t('regis.tip1'),
                        icon:"none"
                     })
                     return false
                }else{
                  const rule = new RegExp(/[^\w\.\/]/ig) ;//如果你们需要字母或者数字，就改这儿！
                  if(rule.test(this.formData.username)){
                    uni.showToast({
						title:this.$t('regis.tip2'),
                      icon:"none"
                    })
                    return false
                  }
                }
                if(this.formData.password.length == 0){
                  uni.showToast({
					  title:this.$t('regis.txt4'),
                    icon:"none"
                  })
                  return false
                }
              if(this.formData.new_password.length == 0){
                uni.showToast({
					title:this.$t('regis.txt5'),
                  icon:"none"
                })
                return false
              }
              if(this.formData.password != this.formData.new_password){
                uni.showToast({
					title:this.$t('regis.tip3'),
                  icon:"none"
                })
                return false
              }
              if(this.formData.staffId.length == 0){
                uni.showToast({
					title:this.$t('regis.txt1'),
                  icon:"none"
                })
                return false
              }
                // this.$refs.form.validate().then(data=>{

                    let params ={
                        uname: this.formData.username,
                        password: this.formData.password,
                        code:  this.formData.staffId,
                        //bname: this.formData.bname,
						phone: '',
						language: uni.getStorageSync('lang')
                    }
                    // 注册
				let _this = this
                    this.$tools.Post("api/user/register",params).then((res) =>{
                        if(res.status == 200){
							uni.showToast({
								title: res.msg,
								duration: 1500,
								icon:'none'
							});
                           setTimeout(function(){
							   _this.$tools.Get("api/user/login",{
								   uname: _this.formData.username,
								   password: _this.formData.password,
								   language: uni.getStorageSync('lang')
							   }).then((res) =>{
								   if(res.status === 200){
									   uni.setStorageSync('loginInfo', {
										   uname: _this.formData.username,
										   password: _this.formData.password
									   });
									   uni.setStorageSync('less',true);
									   uni.hideLoading()
									   uni.setStorage({
										   key: 'token',
										   data: res.data,
										   success: function () {
											   uni.reLaunch({
												   url: '../home/<USER>'
											   });
										   }
									   });
								   } else {
									   uni.showToast({
										   title: res.msg,
										   duration: 1500,
										   icon:'none'
									   });
								   }
							   })
						   },2000)
                        } else {
                            uni.showToast({
                                title: res.msg,
                                duration: 1500,
                                icon:'none'
                            });
                        }
                    })
                // }).catch(err =>{
                // })
            }
        }
    }
</script>

<style scoped lang="less">
	page{
		.loginWrap{
			background: url(~@/static/img/login.jpg) no-repeat top center;
			background-size: 100% 100%;
			min-height: 100vh;
			.loginWrap_cont_title{
				height: 360rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				image{
					width: 236rpx;
					height: 236rpx;
				}
			}
			.cont_textCon_item{
				display: flex;
				margin: 30rpx 55rpx 0 55rpx;
				flex-direction: column;
				justify-content: center;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				.inputStyle{
					margin-top: 11rpx;
					height: 88rpx;
					background: #FFFFFF;
					border-radius: 20rpx;
					padding: 0 19rpx;
					border: 0;
					font-size: 32rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333;
				}
				.inpuyh{
					color: rgba(179, 179, 179, 1);
				}

			}
			.loginWrap_cont_tip{
				padding: 63rpx 65rpx 0 65rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				span{
					color: rgba(150, 231, 39, 1);
				}
			}
			.cont_textCon_but{
				height: 88rpx;
				background: #65B11D;
				border-radius: 20rpx;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 60rpx 55rpx 0 55rpx;
			}
		}
		:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
			color: rgba(179, 179, 179, 1);
		}
		::-moz-placeholder {  /* Mozilla Firefox 19+ */
			color: rgba(179, 179, 179, 1);
		}
		input:-ms-input-placeholder{
			color: rgba(179, 179, 179, 1);
		}
		input::-webkit-input-placeholder{
			color: rgba(179, 179, 179, 1);
		}
	}
</style>
