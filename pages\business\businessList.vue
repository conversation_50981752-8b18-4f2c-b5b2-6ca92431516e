<template>
    <view class="business_listWrap">
		<view class="top">
			<image :src="$tools.setImgUrl(orderImages)" />
			<view class="boxFoot">
				<view class="boxFoot-top">
					<!-- 暂时注释掉期号和开奖结果显示 -->
					<!-- {{$t('new.itemTxt5')}}
					<span class="red">{{result.issue}}</span>
					{{$t('new.itemTxt6')}}
					<span class="red">[{{result.dx == 2 ? i18n.tong : i18n.you}}]</span>
					<span class="blue">[{{result.sd == 3 ? i18n.dan : i18n.shuang}}]</span> -->
				</view>
				<view class="boxFoot-line">{{$t('new.itemTxt1')}}<span>[{{branIssue}}]</span></view>
			</view>
			<view class="boxTop">
				<view class="boxTop-item"><span>{{hourString}}</span>{{$t('new.itemTxt2')}}</view>
				<view class="boxTop-item"><span>{{minuteString}}</span>{{$t('new.itemTxt3')}}</view>
				<view class="boxTop-item"><span>{{secondString}}</span>{{$t('new.itemTxt4')}}</view>
			</view>
		</view>
		<view class="boxMiddle">
			<view class="boxMiddle-item" :class="item.click===1?'boxMiddle-item-on':''" v-for="(item, index) in imagesList" :key="index" @click="proChange(index)"><image :src="$tools.setImgUrl(item.img)" /><span></span></view>
		</view>
		<view class="boxSelect">
			<view class="boxSelect-item" 
				  v-for="(item, index) of typeList" 
				  :key="index" 
				  :class="{
					  'active': arrLeng.includes(index),
					  'disabled': !branIssue || isLastTenSeconds
				  }" 
				  @tap="titleFun(index, item, item.value)">
				{{item && item.cnname ? item.cnname : ''}}
			</view>
		</view>
		<view class="boxNum">{{$t('new.itemTxt7')}}{{Math.floor(Number(allMoney))}}</view>
		<view class="boxDown">
			<view class="boxBottom">
				<view class="boxBottom-left">
					{{$t('new.itemTxt9')}}
					<view class="boxBottom-box">
						<input type="number" @input="inputFun" class="numbers" :value="multiple" />
					</view>
				</view>
				<view class="boxBottom-right">{{total}}{{$t('new.itemTxt8')}}</view>
			</view>
			<view class="boxBtn" @tap="SubmitOrder()">{{i18n.submit}}</view>
		</view>
		<u-popup v-model="showPopup" mode="center" width="580" borderRadius="10">
			<view class="popupBox" >
			<view class="popupHead flexCenter">
				{{i18n.订单确认}}
			</view>
			<view class="content">
				<view class="item">
					<view class="left">{{i18n.订单期号}}：</view>
					<view class="right">{{branIssue}}</view>
				</view>
				<view class="item">
					<view class="left">{{i18n.订单总数}}：</view>
					<view class="right">{{arrLeng.length}}</view>
				</view>
				<view class="item">
					<view class="left">{{i18n.订单内容}}：</view>
					<view class="right">
						<view v-for="(item,index) in arrLeng" :key="index">
							{{typeList[item] && typeList[item].cnname ? typeList[item].cnname : ''}}
						</view>
					</view>
				</view>
			</view>
			<view class="btnBox">
				<view class="cancelBtn flexCenter" @click="showPopup = false">
					{{i18n.取消}}
				</view>
				<view class="confirmBtn flexCenter" @click="confirmClick">
					{{i18n.确认}}
				</view>
			</view>
			</view>
		</u-popup>
    </view>
</template>

<script>
	var setIn;
    export default {
        name: "businessList",
        data() {
            return {
            	proSelect: -1,
				pname: '',
				heights: '110rpx',
                banraName: '',
                branCode: '',
                branIssue: '',
                brandList: [],
                allMoney: '',
                result: {
                    expect: '',
                    openCode: '',
                    quantity: '',
                    size: '',
                },
                current: {
                    expectLast: '',
                    expectCurr: '',
                    timeOpen: 0
                },
				prevExpectLast:'',
				multiple: 1,
				selcIndex: 0,
                selcMoney: '1.00',
				moneyName: '1元',
				getMoneyFlag:false,
                total: 0,
                amount: 0,
                imgIndex: "-1",
                typeList: [],
                arrLeng : [],
                isShow: false,
                timeExpect: 0,
                timeInterval: 0,
                remainTime: 0,
                hour: '',
                minute: '',
                second: '',
                promiseTimer: '',
				moneyTimer:null,
                isShowNewList:false,
                options: [],
				brandImg: [],
                objData: {
                    expect: '',
                    name: '',
                    moneyTypeId: '',
                    number: 1,
                    money: 0,
                    code: [],
					product:[]
                },
				product1 : [],
				openCodeTimer:null,
				orderId:'',
				showPopup:false,
				mineUId: 0,
				orderImages: '',
				brand: [],
				time: 0,
				imagesList: [],
				errorShown: false,
				updateTimer: null,
				lastUpdateTime: 0,
				updateInterval: 10000,
				resultUpdateTimer: null,
				resultUpdateInterval: 5000,
				nextIssueData: null,
                isLastTenSeconds: false,
            }
        },
        onLoad(options) {
            if (!options.id || !options.images || !options.pname) {
                uni.showToast({
                    title: this.$t('common.invalidParams') || '参数无效',
                    icon: 'none',
                    duration: 1500
                });
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
                return;
            }

            this.orderId = options.id;
            this.orderImages = options.images;
            this.imagesList = [];
            
            try {
                let img = JSON.parse(options.imagesList);
                if (Array.isArray(img)) {
                    this.imagesList = img.map(url => ({
                        img: url,
                        click: 0
                    }));
                }
            } catch (e) {
                console.error('Failed to parse imagesList:', e);
            }

            this.pname = options.pname;
            
            uni.setNavigationBarTitle({
                title: this.pname
            });

            Promise.all([
                this.getDeails(this.orderId),
                this.getOpenCodeData(this.orderId),
                this.getCurrentUser(),
                this.getBrandList()
            ]).catch(err => {
                console.error('Initialization error:', err);
                this.handleError();
            });

            this.brandList = uni.getStorageSync('brandList') || [];
        },
        onShow() {
            // 先初始化投注项，确保界面可用
            this.initBetItems();
            // 然后尝试获取数据
            this.initializeData();
        },
		created() {
		},
		beforeDestroy() {
			console.log("我离开了")
			this.clearAllTimers();
		},
		beforeRouteLeave (to, from, next) {
			console.log("我离开了")
			this.clearAllTimers();
			next()
		},

		methods: {
        	proChange(index){
        		if(this.imagesList[index].click === 0){
					this.imagesList[index].click = 1
				} else {
					this.imagesList[index].click = 0
				}
			},
			toDetail(item,index){
				uni.navigateTo({
					url: '/pages/business/businessList?id='+item.pid + '&images='+ item.images + '&pname=' + item.pname
				});
			},
			getBrandList(){
				const currentLang = uni.getStorageSync('lang');
				this.$tools.Get("api/product/list", {
					language: currentLang
				}).then((res) =>{
					if(res.status == 200){
						if(res.data && res.data.length > 0) {
							this.brand = res.data.reverse();
						} else {
							if(currentLang !== 'EN') {
								this.$tools.Get("api/product/list", {
									language: 'EN'
								}).then((enRes) => {
									if(enRes.status == 200 && enRes.data) {
										this.brand = enRes.data.reverse();
									}
								});
							}
						}
					} else {
						uni.showToast({
							title: this.$t('common.loadFailed') || '加载失败',
							duration: 1500,
							icon:'none'
						});
					}
				}).catch(err => {
					console.error('Product list loading error:', err);
					uni.showToast({
						title: this.$t('common.networkError') || '网络错误',
						duration: 1500,
						icon:'none'
					});
				})
			},
			confirmClick(){
				this.SubmitOrderFun()
			},
            getDeails(id){
				clearInterval(this.time);
				uni.hideLoading();
				this.$tools.Get("api/product/issue", {
					pid: id, 
					language: uni.getStorageSync('lang')
				}).then((res) =>{
					if(res.status == 200){
						if(res.data.data && res.data.data.length > 0){
							const currentIssue = this.branIssue;
							this.banraName = res.data.data[0].pname;
							this.branIssue = res.data.data[0].issue;

							if(res.data.data[0].open_at){
								let thisTime = parseInt(res.data.data[0].open_at * 1000);
								let _this = this;
								let nowTime = Date.now();
								
								if(this.time) {
									clearInterval(this.time);
								}
								
								if(thisTime >= nowTime){
									this.time = setInterval(function(){
										nowTime = Date.now();
										let cut = parseInt(thisTime - nowTime);
										if(parseInt(cut) > 0) {
											_this.hour = Math.floor(cut / 3600 / 1000);
											_this.minute = Math.floor((cut - ( _this.hour * 3600 * 1000)) / 60 / 1000);
											_this.second = Math.floor((cut - ( _this.hour * 3600 * 1000) - (_this.minute * 60 * 1000)) / 1000);
											
											if(cut < 30000) {
												_this.isShow = true;
										} else {
												_this.isShow = false;
										}
								} else {
									clearInterval(_this.time);
											_this.handlePeriodEnd();
								}
									},1000);
							} else {
									this.isShow = true;
								clearInterval(this.time);
							}
						} else {
								this.isShow = true;
							clearInterval(this.time);
							}
						} else {
							this.handleNoData();
						}
					} else {
						clearInterval(this.time);
						this.handleError(res.msg || this.$t('common.loadFailed'));
					}
				}).catch(err => {
					clearInterval(this.time);
					console.error('Product detail loading error:', err);
					this.handleError(this.$t('common.networkError'));
				});
			},
			
			handlePeriodEnd() {
				this.isLoading = true;
				
				setTimeout(() => {
					this.getDeails(this.orderId).then(() => {
						this.isLoading = false;
					}).catch(() => {
						this.isLoading = false;
					});
					
					this.getOpenCodeData(this.orderId).catch(err => {
						console.error('Failed to get open code:', err);
					});
				}, 2000);
			},

			handleNoData() {
				uni.showToast({
					title: this.$t('common.noDataAvailable') || '暂无可用期号',
					icon: 'none',
					duration: 2000
				});
				this.isShow = true;
				clearInterval(this.time);
			},

			handleError(msg) {
				if (!this.errorShown) {
					this.errorShown = true;
				uni.showToast({
						title: msg || this.$t('common.systemBusy'),
						icon: 'none',
						duration: 2000
					});
					setTimeout(() => {
						this.errorShown = false;
					}, 2000);
				}
			},
            navigateBack() {
				clearInterval(setIn);
				clearInterval(this.moneyTimer)
				uni.navigateTo({
					url:"/pages/home/<USER>"
				})
            },
            isShowNewListFun(){
                if(this.isShowNewList == false){
                    this.isShowNewList = true
                }else{
                    this.isShowNewList = false
                }
            },
            getOpenCodeData(id) {
				uni.hideLoading()
                this.$tools.Get("api/product/open",{pid: id, language: uni.getStorageSync('lang')}).then((res) =>{
                    if(res.status == 200 ){
						if(res.data.data.length>0){
							this.result = res.data.data[0];
						}
                    } else{
                        uni.showToast({
                            title: res.msg,
                            duration: 1500,
                            icon:'none'
                        });
                    }
                })
            },
			bindPickerChange(e){
				this.selcIndex = e.detail.value;
			    this.selcMoney = this.options[this.selcIndex].money;
				this.moneyName = this.options[this.selcIndex].value;

				let _this = this
				let obj = this.options.filter(function (val, index, arry){
				    if(val.money == _this.selcMoney){
				        return val
				    }
				})
				this.objData.moneyTypeId = obj[0].id
			},
            changeSelect(){
                let _this = this
                let obj = this.options.filter(function (val, index, arry){
                    if(val.money == _this.selcMoney){
                        return val
                    }
                })
                this.objData.moneyTypeId = obj[0].id
            },
            getCurrentUser(){
				this.$tools.Post("api/user/info", {
					api_token: uni.getStorageSync('token'),
					language: uni.getStorageSync('lang')
				}).then((res) =>{
							if(res.status == 200){
								this.allMoney = res.data.price;
								this.mineUId = res.data.uid
							} else {
								uni.showToast({
									title: res.msg,
									duration: 1500,
									icon:'none'
								});
							}
						})
            },
            titleFun(index, row, name) {
                if (!this.branIssue || this.typeList[index].disabled) {
                    return;
                }
                
                if(this.arrLeng.includes(index)) {
                    this.arrLeng = this.arrLeng.filter(function (ele) {
                        return ele != index;
                    });
                    this.objData.code = this.objData.code.filter(function (ele) {
                        return ele.pair_data != name;
                    });
                } else {
                    let obj = row;
                    let {cnname, ...newObj} = obj;
                    this.arrLeng.push(index);
					this.objData.code.push({
                        'pname': this.banraName,
                        'pid': this.orderId,
                        'pair_data': newObj.value,
                        'quantity': this.multiple,
                        'quantity_price': 1.00,
                        'issue': this.branIssue,
                        'msg': ''
                    });
                }
                
                if(this.arrLeng.length >= 1) {
                    this.heights = "320rpx";
                } else {
                    this.heights = "110rpx";
                }
            },
            selectBran(row) {
               this.banraName = row.title
               this.branCode = row.name
               this.objData.name = row.name
               this.timeExpect = row.timeExpect
               this.timeInterval = row.timeInterval
               this.isShowNewList = false
               this.initData()
            },
			reduceFun(){
				if(this.multiple <=1){
					return false
				} else{
					this.multiple --
				}
			},
            brandImgFun(item,index){
           				let num = 0;
           				for(let i=0;i<this.brandImg.length;i++){
           					if(this.brandImg[i].value != -1){
           						num+=1;
           					}
           				}
           				if(item.value == index ){
           					item.value = -1
           				}else{
           					if(num == 2){
           						return
           					}
           					item.value = index
           				}
           				this.product1 = []
           				for(let i=0;i<this.brandImg.length;i++){
           					if(this.brandImg[i].value != -1){
           						this.product1.push(this.brandImg[i].title)
           					}
           				}
                           //this.imgIndex = index
                       },
            cleanUp(){
                this.arrLeng = []
				this.objData.code = []
				this.multiple = 1
                this.heights = "110rpx"

                this.selcMoney = '1.00'
                this.total = 0
				this.moneyName = '1'
				this.changeSelect()
            },
			inputFun(e) {
				this.multiple = e.target.value.replace(/\D/g, '').replace(/^0{1,}/g, '');
			},
			plusAdd(){
				this.multiple ++
			},
            SubmitOrder(){
                let _this = this
                if(_this.objData.code.length <= 0) {
                    uni.showToast({
                        title: this.i18n.zhushu,
                        duration: 1500,
                        icon:'none'
                    });
                    return false
                }
				if(_this.multiple == ''){
					uni.showToast({
					    title: "请输入倍数",
					    duration: 1500,
					    icon:'none'
					});
					return false
				}
                _this.objData.number = _this.multiple;
                _this.objData.money = _this.total
				_this.objData.product = _this.product1
				let codeLength = this.objData.code.length
				let a
				for(a=0;a<codeLength;a++){
					_this.objData.code[a].quantity = _this.multiple
				}
				_this.showPopup = true
            },
            SubmitOrderFun(){
                uni.showLoading({
                    title: this.$t('common.submitting') || '提交中'
                });

                this.$tools.Get("api/product/issue", {
                    pid: this.orderId,
                    language: uni.getStorageSync('lang')
                }).then(res => {
                    if (res.status === 200 && res.data.data && res.data.data.length > 0) {
                        const latestIssue = res.data.data[0].issue;
                        
                        if (latestIssue !== this.branIssue) {
                            uni.hideLoading();
                            uni.showToast({
                                title: this.$t('common.issueExpired') || '期号已更新，请重新选择',
                                icon: 'none',
                                duration: 2000
                            });
                            this.getDeails(this.orderId);
                            return;
                        }

                        return this.$tools.Post("api/user/ordering/add", {
                            data: this.objData.code,
                            uid: this.mineUId,
                            api_token: uni.getStorageSync('token'),
                            language: uni.getStorageSync('lang')
                        });
                    } else {
                        throw new Error('Failed to verify issue');
                    }
                }).then(res => {
                    if (!res) return;
                    
                    if(res.status == 200){
                        this.handleOrderSuccess();
                    } else {
                        throw new Error(res.msg || 'Order submission failed');
                    }
                }).catch(err => {
                    console.error('Order submission error:', err);
                    uni.showToast({
                        title: err.message || this.$t('common.submitFailed') || '提交失败',
                        icon: 'none',
                        duration: 2000
                    });
                }).finally(() => {
                    uni.hideLoading();
                });
            },

            handleOrderSuccess() {
                Promise.all([
                    this.getDeails(this.orderId),
                    this.getOpenCodeData(this.orderId),
                    this.getCurrentUser()
                ]).catch(err => {
                    console.error('Refresh error after order:', err);
                });

                this.objData.code = [];
                this.multiple = 1;
                this.arrLeng = [];
                this.showPopup = false;

                uni.showToast({
                    title: this.i18n.succ,
                    duration: 1500,
                    icon: 'success'
                });

                uni.$emit('orderSubmitted', {
                    forceRefresh: true
                });
            },
            countDowm () {
                let self = this
                clearInterval(this.promiseTimer)
                this.promiseTimer = setInterval(function () {
                    if (self.hour === 0) {
                        if (self.minute !== 0 && self.second === 0) {
                          self.second = 59
                          self.minute -= 1
                        } else if (self.minute === 0 && self.second === 0) {
                          self.second = 0
                          clearInterval(self.promiseTimer)
                        } else {
                          self.second -= 1
                        }
                    } else {
                        if (self.minute !== 0 && self.second === 0) {
                          self.second = 59
                          self.minute -= 1
                        } else if (self.minute === 0 && self.second === 0) {
                          self.hour -= 1
                          self.minute = 59
                          self.second = 59
                        } else {
                          self.second -= 1
                        }
                    }
                    self.remainTime-=1
                    if(Number(self.remainTime) <= Number(self.timeInterval)){
                        self.isShow = true
                    } else{
                        self.isShow = false
                    }
					if(Number(self.remainTime) < 2){
						self.initData()
					}
					if(Number(self.remainTime) ==0){
						uni.showLoading()
					}
                }, 1000)
            },
            formatNum (num) {
                return num < 10 ? '0' + num : '' + num
            },
            initAutoUpdate() {
                // 先清理可能存在的定时器
                if(this.updateTimer) {
                    clearInterval(this.updateTimer);
                    this.updateTimer = null;
                }
                if(this.resultUpdateTimer) {
                    clearInterval(this.resultUpdateTimer);
                    this.resultUpdateTimer = null;
                }
                
                // 使用更短的更新间隔以提高实时性
                this.updateInterval = 5000; // 5秒更新一次期号
                this.resultUpdateInterval = 3000; // 3秒更新一次开奖结果
                
                // 启动定时更新
                this.updateTimer = setInterval(() => {
                    this.checkAndUpdate();
                }, this.updateInterval);

                // 暂时注释开奖结果更新
                // this.resultUpdateTimer = setInterval(() => {
                //     this.updateOpenResult();
                // }, this.resultUpdateInterval);
            },

            async checkAndUpdate() {
                try {
                    const issueRes = await this.$tools.Get("api/product/issue", {
                        pid: this.orderId,
                        language: uni.getStorageSync('lang'),
                        _t: Date.now() // 添加时间戳防止缓存
                    });

                    if(issueRes.status === 200 && issueRes.data.data && issueRes.data.data.length > 0) {
                        const newIssue = issueRes.data.data[0].issue;
                        
                        if(newIssue !== this.branIssue) {
                            await this.updateIssueData(issueRes.data.data[0]);
                        }
                    }
                } catch(err) {
                    console.error('Check and update failed:', err);
                    // 静默失败，不显示错误提示
                    return false;
                }
            },

            resetCountdown(endTime) {
                // 先清除可能存在的倒计时
                if(this.time) {
                    clearInterval(this.time);
                }

                // 立即计算并显示第一次倒计时
                const updateCountdown = () => {
                    const nowTime = Date.now();
                    const cut = parseInt(endTime - nowTime);
                    
                    if(parseInt(cut) > 0) {
                        this.hour = Math.floor(cut / 3600 / 1000);
                        this.minute = Math.floor((cut - (this.hour * 3600 * 1000)) / 60 / 1000);
                        this.second = Math.floor((cut - (this.hour * 3600 * 1000) - (this.minute * 60 * 1000)) / 1000);
                        
                        // 最后30秒显示提示
                        if(cut < 30000) {
                            this.isShow = true;
                        } else {
                            this.isShow = false;
                        }

                        // 最后2秒预加载下一期
                        if(cut <= 2000) {
                            this.preloadNextIssue();
                        }
                        
                        // 最后10秒灰显投注项
                        this.isLastTenSeconds = cut <= 10000;
                    } else {
                        clearInterval(this.time);
                        this.updateToNextIssue();
                    }
                };

                // 立即执行一次
                updateCountdown();

                // 设置定时器，每500毫秒更新一次以提高精度
                this.time = setInterval(updateCountdown, 500);
            },

            // 预加载下一期数据
            async preloadNextIssue() {
                try {
                    const issueRes = await this.$tools.Get("api/product/issue", {
                        pid: this.orderId,
                        language: uni.getStorageSync('lang')
                    });

                    if(issueRes.status === 200 && issueRes.data.data && issueRes.data.data.length > 0) {
                        this.nextIssueData = issueRes.data.data[0];
                    }
                } catch(err) {
                    console.error('Preload next issue failed:', err);
                }
            },

            // 更新到下一期
            async updateToNextIssue() {
                try {
                    // 如果有预加载的数据，直接使用
                    if(this.nextIssueData) {
                        this.updateIssueData(this.nextIssueData);
                        this.nextIssueData = null;
                    } else {
                        // 否则重新获取数据
                        await this.checkAndUpdate();
                    }
                } catch(err) {
                    console.error('Update to next issue failed:', err);
                    // 失败后的重试机制
                    setTimeout(() => {
                        this.checkAndUpdate();
                    }, 1000);
                }
            },

            // 更新期号数据
            async updateIssueData(issueData) {
                try {
                    if(issueData.issue !== this.branIssue) {
                        // 更新期号
                        this.branIssue = issueData.issue;
                        this.banraName = issueData.pname;
                        
                        // 清空投注选择
                        this.clearBetSelections();
                        
                        // 重置投注项
                        this.initBetItems();
                        
                        // 更新开奖结果
                        await this.getOpenCodeData(this.orderId);
                        
                        // 重置倒计时
                        if(issueData.open_at) {
                            this.resetCountdown(parseInt(issueData.open_at * 1000));
                        }
                    }
                } catch(err) {
                    console.error('Update issue data failed:', err);
                    // 出错时重置状态
                    this.initBetItems();
                }
            },

            async updateOpenResult() {
                try {
                    const res = await this.$tools.Get("api/product/open", {
                        pid: this.orderId,
                        language: uni.getStorageSync('lang'),
                        _t: Date.now() // 添加时间戳防止缓存
                    });

                    if(res.status === 200 && res.data.data && res.data.data.length > 0) {
                        const newResult = res.data.data[0];
                        // 只在结果变化时更新
                        if(!this.result.issue || newResult.issue !== this.result.issue || 
                           newResult.openCode !== this.result.openCode) {
                            this.result = newResult;
                            this.animateResultUpdate();
                            // 开奖后刷新投注项
                            this.refreshBetItems();
                            
                            // 发出开奖结果更新事件
                            this.$emit('resultUpdate', newResult);
                        }
                    }
                } catch(err) {
                    console.error('Failed to update open result:', err);
                }
            },

            animateResultUpdate() {
                const resultElement = document.querySelector('.boxFoot-top');
                if(resultElement) {
                    resultElement.style.transition = 'opacity 0.3s';
                    resultElement.style.opacity = '0';
                    setTimeout(() => {
                        resultElement.style.opacity = '1';
                    }, 300);
                }
            },
            // 刷新投注项状态
            refreshBetItems() {
                // 完全重置投注项状态
                this.typeList = [];
                // 重新初始化投注项
                setTimeout(() => {
                    this.typeList = [
                        {
                            cnname: this.i18n.you,
                            name: "优品AR+1",
                            field: 'size',
                            value: 1,
                            disabled: false
                        },
                        {
                            cnname: this.i18n.tong,
                            name: "统货Ct+1",
                            field: 'size',
                            value: 2,
                            disabled: false
                        },
                        {
                            cnname: this.i18n.dan,
                            name: "单/件",
                            field: 'quantity',
                            value: 3,
                            disabled: false
                        },
                        {
                            cnname: this.i18n.shuang,
                            name: "双/件",
                            field: 'quantity',
                            value: 4,
                            disabled: false
                        }
                    ];
                }, 0);
            },

            // 完全清空所有状态
            cleanUpAll() {
                // 清空投注选择
                this.arrLeng = [];
                this.objData.code = [];
                this.multiple = 1;
                this.total = 0;
                
                // 重置投注项状态
                this.refreshBetItems();
                
                // 重置其他相关状态
                this.isShow = false;
                this.getMoneyFlag = false;
                
                // 强制更新视图
                this.$forceUpdate();
            },

            // 初始化数据
            async initializeData() {
                try {
                    // 先停止所有可能的定时器
                    this.clearAllTimers();

                    // 获取期号信息
                    const issueRes = await this.$tools.Get("api/product/issue", {
                        pid: this.orderId,
                        language: uni.getStorageSync('lang'),
                        _t: Date.now() // 添加时间戳防止缓存
                    });

                    if (issueRes.status === 200 && issueRes.data.data && issueRes.data.data.length > 0) {
                        const issueData = issueRes.data.data[0];
                        
                        // 同步更新期号和倒计时
                        await Promise.all([
                            // 更新期号相关数据
                            (async () => {
                                this.branIssue = issueData.issue;
                                this.banraName = issueData.pname;
                                await this.getOpenCodeData(this.orderId);
                            })(),
                            // 立即设置倒计时
                            (async () => {
                                if (issueData.open_at) {
                                    this.resetCountdown(parseInt(issueData.open_at * 1000));
                                }
                            })()
                        ]);

                        // 初始化自动更新
                        this.initAutoUpdate();
                    }
                } catch (err) {
                    console.error('Initialize data failed:', err);
                    // 出错后1秒重试
                    setTimeout(() => {
                        this.initializeData();
                    }, 1000);
                }
            },

            // 初始化投注项
            initBetItems() {
                this.typeList = [
                    {
                        cnname: this.i18n.you,
                        name: "优品AR+1",
                        field: 'size',
                        value: 1
                    },
                    {
                        cnname: this.i18n.tong,
                        name: "统货Ct+1",
                        field: 'size',
                        value: 2
                    },
                    {
                        cnname: this.i18n.dan,
                        name: "单/件",
                        field: 'quantity',
                        value: 3
                    },
                    {
                        cnname: this.i18n.shuang,
                        name: "双/件",
                        field: 'quantity',
                        value: 4
                    }
                ];
            },

            // 清空投注选择
            clearBetSelections() {
                this.arrLeng = [];
                this.objData.code = [];
                this.multiple = 1;
                this.total = 0;
            },

            clearAllTimers() {
                // 清理所有定时器
                const timers = [
                    this.time,
                    this.updateTimer,
                    this.resultUpdateTimer,
                    this.moneyTimer,
                    this.openCodeTimer,
                    this.promiseTimer
                ];
                
                timers.forEach(timer => {
                    if(timer) {
                        clearInterval(timer);
                    }
                });
                
                // 重置定时器变量
                this.time = null;
                this.updateTimer = null;
                this.resultUpdateTimer = null;
                this.moneyTimer = null;
                this.openCodeTimer = null;
                this.promiseTimer = null;
            },
        },
        computed: {
            hourString() {
                return this.formatNum(this.hour);
            },
            minuteString() {
                return this.formatNum(this.minute);
            },
            secondString() {
                return this.formatNum(this.second);
            },
            i18n() {
                return this.$t("business");
            },
            lang() {
                return this.$t("contentText");
            },
            // 计算每个投注项的宽度
            itemWidths() {
                if (!this.typeList || this.typeList.length === 0) return [];
                
                const screenWidth = uni.getSystemInfoSync().windowWidth;
                const availableWidth = screenWidth - 20; // 容器padding 20px
                const minGap = 4; // 最小间距
                const minWidth = 60; // 最小宽度
                
                // 计算每个项目的基础宽度
                const baseWidths = this.typeList.map(item => {
                    // 中文字符按15px计算，其他字符按8px计算
                    return item.cnname.split('').reduce((width, char) => {
                        return width + (/[\u4e00-\u9fa5]/.test(char) ? 15 : 8);
                    }, 0) + 16; // 16px为padding和border
                });
                
                // 计算总宽度
                const totalBaseWidth = baseWidths.reduce((sum, width) => sum + width, 0);
                
                // 如果总宽度小于可用宽度，按实际宽度分配
                if (totalBaseWidth + (minGap * 3) <= availableWidth) {
                    return baseWidths.map(width => {
                        return `${Math.max(minWidth, width)}px`;
                    });
                }
                
                // 否则按比例分配可用空间
                const totalRatio = baseWidths.reduce((sum, width) => sum + width, 0);
                return baseWidths.map(width => {
                    const ratio = width / totalRatio;
                    const finalWidth = Math.max(minWidth, (availableWidth - (minGap * 3)) * ratio);
                    return `${finalWidth}px`;
                });
            }
        },
        destroyed() {
            clearInterval(this.promiseTimer);
            if(this.time) {
                clearInterval(this.time);
            }
            if(this.updateTimer) {
                clearInterval(this.updateTimer);
            }
            if(this.resultUpdateTimer) {
                clearInterval(this.resultUpdateTimer);
            }
        },
        onUnload() {
            clearInterval(this.promiseTimer);
            if(this.time) {
                clearInterval(this.time);
            }
            if(this.updateTimer) {
                clearInterval(this.updateTimer);
            }
            if(this.resultUpdateTimer) {
                clearInterval(this.resultUpdateTimer);
            }
        },
        watch: {
            selcMoney: {
                handler(newName, oldName) {
                    this.total = this.multiple * newName * this.arrLeng.length;
                }
            },
            multiple: {
                handler(newName, oldName) {
                    this.total = this.selcMoney * newName * this.arrLeng.length;
                }
            },
            arrLeng: {
                handler(newName, oldName) {
                    this.total = this.selcMoney * this.multiple * newName.length;
                }
            }
        }
    }
</script>

<style scoped lang="less">
    @import url('../../static/css/mian.css');
	@import url('../../static/css/shop.less');

    .business_listWrap {
        padding-bottom: 120px;
    }

    .boxSelect {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 12px 4px;
        margin: 30px 0 8px;
        width: 100%;
        box-sizing: border-box;
        gap: 3px;
    }

    .boxNum {
        margin-top: 30px;
    }

    .boxSelect-item {
        flex: 1 1 auto;
        height: 32px;
        line-height: 32px;
        margin: 0;
        padding: 0 6px;
        text-align: center;
        border-radius: 16px;
        background: #fff;
        border: 1px solid #68B51F;
        font-size: 12px;
        color: #333;
        transition: all 0.3s ease;
        white-space: nowrap;
        min-width: 0; 
        
        &.active {
            background: #68B51F;
            color: #fff;
            border-color: #68B51F;
        }
        
        &.disabled {
            opacity: 0.5;
            background: #f5f5f5;
            border-color: #ddd;
            color: #999;
            pointer-events: none;
        }
    }

    .boxSelect-item {
        vertical-align: middle;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .boxDown {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background: #172D52;
        z-index: 999;
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);

        .boxBottom {
            color: #fff;
            
            .boxBottom-box {
                .numbers {
                    background: #fff;
                    color: #333;
                    border-radius: 16px;
                    padding: 2px 8px;
                    border: none;
                    text-align: center;
                    min-width: 60px;
                    height: 28px;
                    font-size: 13px;
                    outline: none;
                }
            }
        }

        .boxBottom-right {
            color: #fff;
        }
    }
</style>
